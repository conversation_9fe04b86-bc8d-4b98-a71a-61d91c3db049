"""
Comprehensive Procurement System API with AI-Powered Analysis
Flask backend with agentic framework for automated procurement workflow
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import logging
import threading
import time

# Local imports
from procurement_system import (
    ProcurementRequest, Vendor, Quotation, PurchaseOrder,
    ProcurementStatus, UrgencyLevel
)
from mongodb_manager import get_mongodb_manager
from ai_document_processor import AIDocumentProcessor
from ai_quotation_analyzer import AIQuotationAnalyzer, AnalysisResult
from email_automation import EmailAutomationSystem
from email_reader import EmailQuotationReader

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize system components
db_manager = get_mongodb_manager()
doc_processor = AIDocumentProcessor()
quotation_analyzer = AIQuotationAnalyzer()
email_system = EmailAutomationSystem()
email_reader = EmailQuotationReader()

# Create uploads directory
UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def send_rfq_emails_background(procurement_request, vendors, email_system, db_manager):
    """Background function to send RFQ emails without blocking the API response"""
    try:
        logger.info(f"🔄 Background: Starting RFQ email sending to {len(vendors)} vendors...")
        start_time = datetime.now()

        email_results = email_system.send_rfq_to_vendors(procurement_request, vendors)

        end_time = datetime.now()
        email_duration = (end_time - start_time).total_seconds()
        logger.info(f"⏱️ Background: Email sending completed in {email_duration:.2f} seconds")

        # Update status after emails are sent
        procurement_request.status = ProcurementStatus.RFQ_SENT
        db_manager.save_procurement_request(procurement_request)

        successful_sends = sum(email_results.values())
        logger.info(f"✅ Background: RFQ process completed - {successful_sends}/{len(vendors)} emails sent")

    except Exception as e:
        logger.error(f"❌ Background email sending failed: {e}")
        # Update status to indicate failure
        procurement_request.status = ProcurementStatus.DRAFT
        db_manager.save_procurement_request(procurement_request)



@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'AI Procurement System is running',
        'timestamp': datetime.now().isoformat(),
        'components': {
            'database': 'connected',
            'ai_processor': 'loaded',
            'email_system': 'configured'
        }
    })

@app.route('/api/procurement/request', methods=['POST'])
def create_procurement_request():
    """Create new procurement request and send RFQ to vendors"""
    try:
        data = request.json
        logger.info(f"📝 Received procurement request data: {data}")

        # Validate required fields
        required_fields = ['category', 'items', 'specifications', 'created_by']
        for field in required_fields:
            if field not in data:
                logger.error(f"❌ Missing required field: {field}")
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Create procurement request with proper defaults
        procurement_request = ProcurementRequest(
            id=str(uuid.uuid4()),
            category=data['category'],
            items=data['items'] if isinstance(data['items'], list) else [data['items']],
            specifications=data['specifications'],
            quantity=data.get('quantity', 1),  # Default to 1 if not provided
            urgency=UrgencyLevel(data.get('urgency', 'medium')),  # Default to medium
            budget_limit=data.get('budget_limit'),
            created_by=data['created_by'],
            created_at=datetime.now(),
            status=ProcurementStatus.DRAFT,
            deadline=datetime.now() + timedelta(days=data.get('deadline_days', 7))
        )
        
        # Save to database
        db_manager.save_procurement_request(procurement_request)
        
        # Get relevant vendors
        vendors = db_manager.get_vendors_by_category(procurement_request.category)
        
        if not vendors:
            return jsonify({
                'error': f'No vendors found for category: {procurement_request.category}'
            }), 404
        
        # Check if fast response mode is requested
        fast_mode = data.get('fast_response', True)  # Default to fast mode

        if fast_mode:
            # Fast response: Start email sending in background
            logger.info(f"🚀 Fast mode: Starting background RFQ email sending to {len(vendors)} vendors...")

            # Start background email sending
            email_thread = threading.Thread(
                target=send_rfq_emails_background,
                args=(procurement_request, vendors, email_system, db_manager)
            )
            email_thread.daemon = True
            email_thread.start()

            # Return immediate response
            return jsonify({
                'message': 'Procurement request created successfully! RFQ emails are being sent in background.',
                'request_id': procurement_request.id,
                'vendors_contacted': len(vendors),
                'status': 'processing',
                'fast_mode': True,
                'vendor_details': [
                    {
                        'name': vendor.name,
                        'email': vendor.email,
                        'status': 'queued'
                    }
                    for vendor in vendors
                ]
            })
        else:
            # Traditional mode: Wait for emails to complete
            logger.info(f"📧 Traditional mode: Starting RFQ email sending to {len(vendors)} vendors...")
            start_time = datetime.now()

            email_results = email_system.send_rfq_to_vendors(procurement_request, vendors)

            end_time = datetime.now()
            email_duration = (end_time - start_time).total_seconds()
            logger.info(f"⏱️ Email sending completed in {email_duration:.2f} seconds")

            # Update status
            procurement_request.status = ProcurementStatus.RFQ_SENT
            db_manager.save_procurement_request(procurement_request)

            return jsonify({
                'message': 'Procurement request created and RFQ sent successfully',
                'request_id': procurement_request.id,
                'vendors_contacted': len(vendors),
                'emails_sent': sum(email_results.values()),
                'email_duration': f"{email_duration:.2f}s",
                'fast_mode': False,
                'vendor_details': [
                    {
                        'name': vendor.name,
                        'email': vendor.email,
                        'email_sent': email_results.get(vendor.id, False)
                    }
                    for vendor in vendors
                ]
            })
        
    except Exception as e:
        logger.error(f"❌ Error creating procurement request: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/quotation/upload', methods=['POST'])
def upload_quotation():
    """Upload and process quotation document using AI"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']
        request_id = request.form.get('request_id')
        vendor_id = request.form.get('vendor_id')

        if not request_id or not vendor_id:
            return jsonify({'error': 'request_id and vendor_id are required'}), 400

        # Save uploaded file
        filename = f"{uuid.uuid4()}_{file.filename}"
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)

        # Process document with AI
        logger.info(f"🔄 Processing quotation document: {filename}")
        processing_result = doc_processor.process_quotation_document(file_path)

        if 'error' in processing_result:
            return jsonify(processing_result), 400

        # Extract comprehensive information
        key_info = processing_result.get('key_info', {})
        structured_data = processing_result.get('structured_data', {})
        metadata = processing_result.get('metadata', {})

        # Create quotation object with comprehensive data
        quotation = Quotation(
            id=str(uuid.uuid4()),
            request_id=request_id,
            vendor_id=vendor_id,
            total_amount=key_info.get('total_amount', 0.0),
            delivery_days=key_info.get('delivery_time_days', 30),
            payment_terms=key_info.get('payment_terms', 'Standard terms'),
            validity_days=key_info.get('quotation_validity', '30 days').replace(' days', '') if key_info.get('quotation_validity') else 30,
            items=[],  # Will be populated from extracted data
            document_path=file_path,
            extracted_data={
                'structured_data': structured_data,
                'key_info': key_info,
                'metadata': metadata,
                'delivery_time': key_info.get('delivery_time'),
                'warranty_period': key_info.get('warranty_period'),
                'certifications': key_info.get('certifications', []),
                'gst_rate': key_info.get('gst_rate'),
                'installation_included': key_info.get('installation_included', False),
                'shipping_included': key_info.get('shipping_included', False),
                'specifications': key_info.get('specifications_summary', []),
                'compliance_standards': key_info.get('compliance_standards', []),
                'vendor_contact': key_info.get('vendor_contact', {}),
                'quality_score': key_info.get('quality_score', 0.0),
                'completeness_score': key_info.get('completeness_score', 0.0)
            },
            ai_score=0.0,  # Will be calculated during analysis
            confidence_score=metadata.get('extraction_confidence', 0.0),
            received_at=datetime.now(),
            status="received"
        )

        # Check if quotation from this vendor already exists
        existing_quotations = db_manager.get_quotations_by_request(request_id)
        existing_vendor_quotation = next((q for q in existing_quotations if q.vendor_id == vendor_id), None)

        if existing_vendor_quotation:
            logger.warning(f"⚠️ Quotation from vendor {vendor_id} already exists for request {request_id}")
            return jsonify({
                'error': 'A quotation from this vendor already exists for this request',
                'existing_quotation_id': existing_vendor_quotation.id
            }), 409

        # Save quotation
        db_manager.save_quotation(quotation)

        # Get total quotations count for this request
        all_quotations = db_manager.get_quotations_by_request(request_id)

        return jsonify({
            'message': 'Quotation uploaded and processed successfully',
            'quotation_id': quotation.id,
            'total_quotations': len(all_quotations),
            'vendor_name': db_manager.get_vendor(vendor_id).name if db_manager.get_vendor(vendor_id) else 'Unknown Vendor',
            'extracted_data': {
                'total_amount': quotation.total_amount,
                'delivery_time': key_info.get('delivery_time', 'Not specified'),
                'delivery_days': quotation.delivery_days,
                'payment_terms': quotation.payment_terms,
                'warranty_period': key_info.get('warranty_period', 'Not specified'),
                'gst_rate': key_info.get('gst_rate', 'Not specified'),
                'certifications_count': len(key_info.get('certifications', [])),
                'installation_included': key_info.get('installation_included', False),
                'shipping_included': key_info.get('shipping_included', False),
                'confidence_score': quotation.confidence_score,
                'quality_score': key_info.get('quality_score', 0.0),
                'completeness_score': key_info.get('completeness_score', 0.0)
            },
            'comprehensive_analysis': {
                'text_extracted': metadata.get('text_length', 0),
                'entities_found': len(structured_data.get('entities', [])),
                'amounts_detected': len(structured_data.get('amounts', [])),
                'delivery_times_found': len(structured_data.get('delivery_times', [])),
                'certifications_found': len(key_info.get('certifications', [])),
                'specifications_found': len(key_info.get('specifications_summary', [])),
                'compliance_standards': len(key_info.get('compliance_standards', [])),
                'features_extracted': {
                    'has_delivery_time': structured_data.get('extracted_features', {}).get('has_delivery_time', False),
                    'has_warranty': structured_data.get('extracted_features', {}).get('has_warranty', False),
                    'has_certifications': structured_data.get('extracted_features', {}).get('has_certifications', False),
                    'has_gst_info': structured_data.get('extracted_features', {}).get('has_gst_info', False),
                    'has_installation': structured_data.get('extracted_features', {}).get('has_installation', False),
                    'has_specifications': structured_data.get('extracted_features', {}).get('has_specifications', False),
                    'payment_terms_clear': structured_data.get('extracted_features', {}).get('payment_terms_clear', False)
                }
            },
            'document_summary': processing_result.get('summary', {})
        })

    except Exception as e:
        logger.error(f"❌ Error uploading quotation: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/quotation/bulk-upload', methods=['POST'])
def bulk_upload_quotations():
    """Upload multiple quotations from multiple vendors at once"""
    try:
        request_id = request.form.get('request_id')
        if not request_id:
            return jsonify({'error': 'request_id is required'}), 400

        # Get all files and their corresponding vendor IDs
        files = request.files.getlist('files')
        vendor_ids = request.form.getlist('vendor_ids')

        if not files:
            return jsonify({'error': 'No files uploaded'}), 400

        if len(files) != len(vendor_ids):
            return jsonify({'error': 'Number of files must match number of vendor IDs'}), 400

        # Get existing quotations to check for duplicates
        existing_quotations = db_manager.get_quotations_by_request(request_id)
        existing_vendor_ids = {q.vendor_id for q in existing_quotations}

        results = []
        successful_uploads = 0
        failed_uploads = 0

        for i, (file, vendor_id) in enumerate(zip(files, vendor_ids)):
            try:
                # Check if quotation from this vendor already exists
                if vendor_id in existing_vendor_ids:
                    results.append({
                        'file_index': i,
                        'filename': file.filename,
                        'vendor_id': vendor_id,
                        'status': 'skipped',
                        'message': 'Quotation from this vendor already exists',
                        'vendor_name': db_manager.get_vendor(vendor_id).name if db_manager.get_vendor(vendor_id) else 'Unknown Vendor'
                    })
                    continue

                # Save uploaded file
                filename = f"{uuid.uuid4()}_{file.filename}"
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                file.save(file_path)

                # Process document with AI
                logger.info(f"🔄 Processing quotation document {i+1}/{len(files)}: {filename}")
                extracted_data = doc_processor.process_quotation_document(file_path)

                if 'error' in extracted_data:
                    results.append({
                        'file_index': i,
                        'filename': file.filename,
                        'vendor_id': vendor_id,
                        'status': 'failed',
                        'message': f"AI processing failed: {extracted_data['error']}",
                        'vendor_name': db_manager.get_vendor(vendor_id).name if db_manager.get_vendor(vendor_id) else 'Unknown Vendor'
                    })
                    failed_uploads += 1
                    continue

                # Extract key information from the correct structure
                key_info = extracted_data.get('key_info', {})
                structured_data = extracted_data.get('structured_data', {})

                # Get total amount from key_info (preferred) or structured_data
                total_amount = key_info.get('total_amount', 0.0)
                if total_amount == 0.0 and structured_data.get('amounts'):
                    total_amount = max(structured_data['amounts'])

                # Get other information
                delivery_days = key_info.get('delivery_time_days', 30)
                if delivery_days == 0 and structured_data.get('delivery_times'):
                    delivery_days = min(structured_data['delivery_times'])

                payment_terms = key_info.get('payment_terms') or 'Standard terms'
                if not payment_terms or payment_terms == 'Standard terms':
                    payment_terms_list = structured_data.get('payment_terms', [])
                    if payment_terms_list:
                        payment_terms = payment_terms_list[0]

                logger.info(f"💰 Quotation data: Amount=₹{total_amount:,.2f}, Delivery={delivery_days} days, Terms={payment_terms}")

                # Create quotation object
                quotation = Quotation(
                    id=str(uuid.uuid4()),
                    request_id=request_id,
                    vendor_id=vendor_id,
                    total_amount=total_amount,
                    delivery_days=delivery_days,
                    payment_terms=payment_terms,
                    validity_days=key_info.get('quotation_validity', 30),
                    items=[],  # Will be populated from extracted data
                    document_path=file_path,
                    extracted_data=extracted_data,
                    ai_score=0.0,  # Will be calculated during analysis
                    confidence_score=extracted_data.get('metadata', {}).get('extraction_confidence', 0.0),
                    received_at=datetime.now(),
                    status="received"
                )

                # Save quotation
                db_manager.save_quotation(quotation)
                existing_vendor_ids.add(vendor_id)  # Add to prevent duplicates in same batch

                vendor_name = db_manager.get_vendor(vendor_id).name if db_manager.get_vendor(vendor_id) else 'Unknown Vendor'

                results.append({
                    'file_index': i,
                    'filename': file.filename,
                    'vendor_id': vendor_id,
                    'vendor_name': vendor_name,
                    'quotation_id': quotation.id,
                    'status': 'success',
                    'message': 'Quotation uploaded and processed successfully',
                    'extracted_data': {
                        'total_amount': quotation.total_amount,
                        'delivery_days': quotation.delivery_days,
                        'payment_terms': quotation.payment_terms,
                        'confidence_score': quotation.confidence_score
                    }
                })
                successful_uploads += 1

            except Exception as file_error:
                logger.error(f"❌ Error processing file {i+1}: {file_error}")
                results.append({
                    'file_index': i,
                    'filename': file.filename,
                    'vendor_id': vendor_id,
                    'status': 'failed',
                    'message': f"Processing error: {str(file_error)}",
                    'vendor_name': db_manager.get_vendor(vendor_id).name if db_manager.get_vendor(vendor_id) else 'Unknown Vendor'
                })
                failed_uploads += 1

        # Get updated quotations count
        all_quotations = db_manager.get_quotations_by_request(request_id)

        return jsonify({
            'message': f'Bulk upload completed: {successful_uploads} successful, {failed_uploads} failed',
            'total_files_processed': len(files),
            'successful_uploads': successful_uploads,
            'failed_uploads': failed_uploads,
            'skipped_uploads': len(files) - successful_uploads - failed_uploads,
            'total_quotations': len(all_quotations),
            'results': results
        })

    except Exception as e:
        logger.error(f"❌ Error in bulk upload: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/requests', methods=['GET'])
def get_procurement_requests():
    """Get all procurement requests"""
    try:
        requests = db_manager.get_all_procurement_requests()

        requests_data = []
        for req in requests:
            requests_data.append({
                'id': req.id,
                'category': req.category,
                'items': req.items,
                'specifications': req.specifications,
                'quantity': req.quantity,
                'urgency': req.urgency.value if hasattr(req.urgency, 'value') else req.urgency,
                'created_by': req.created_by,
                'created_at': req.created_at.isoformat() if req.created_at else None,
                'status': req.status.value if hasattr(req.status, 'value') else req.status,
                'deadline': req.deadline.isoformat() if req.deadline else None
            })

        return jsonify({
            'requests': requests_data,
            'total_count': len(requests_data)
        })

    except Exception as e:
        logger.error(f"❌ Error fetching procurement requests: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/request/<request_id>/status', methods=['GET'])
def get_request_status(request_id):
    """Get the current status of a procurement request including email sending progress"""
    try:
        req = db_manager.get_procurement_request(request_id)

        if not req:
            return jsonify({'error': 'Procurement request not found'}), 404

        # Get vendors for this category
        vendors = db_manager.get_vendors_by_category(req.category)

        return jsonify({
            'request_id': request_id,
            'status': req.status.value if hasattr(req.status, 'value') else req.status,
            'vendors_contacted': len(vendors),
            'created_at': req.created_at.isoformat() if req.created_at else None,
            'email_status': 'completed' if req.status == ProcurementStatus.RFQ_SENT else 'processing'
        })

    except Exception as e:
        logger.error(f"❌ Error getting request status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/request/<request_id>', methods=['GET'])
def get_procurement_request(request_id):
    """Get a specific procurement request"""
    try:
        req = db_manager.get_procurement_request(request_id)

        if not req:
            return jsonify({'error': 'Procurement request not found'}), 404

        request_data = {
            'id': req.id,
            'category': req.category,
            'items': req.items,
            'specifications': req.specifications,
            'quantity': req.quantity,
            'urgency': req.urgency.value if hasattr(req.urgency, 'value') else req.urgency,
            'created_by': req.created_by,
            'created_at': req.created_at.isoformat() if req.created_at else None,
            'status': req.status.value if hasattr(req.status, 'value') else req.status,
            'deadline': req.deadline.isoformat() if req.deadline else None
        }

        return jsonify({
            'request': request_data
        })

    except Exception as e:
        logger.error(f"❌ Error fetching procurement request: {e}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/procurement/quotations/<request_id>', methods=['GET'])
def get_quotations_by_request(request_id):
    """Get all quotations for a specific procurement request"""
    try:
        quotations = db_manager.get_quotations_by_request(request_id)

        quotations_data = []
        for quotation in quotations:
            vendor = db_manager.get_vendor(quotation.vendor_id)
            extracted_data = quotation.extracted_data or {}
            key_info = extracted_data.get('key_info', {})

            quotations_data.append({
                'id': quotation.id,
                'vendor_name': vendor.name if vendor else 'Unknown Vendor',
                'vendor_id': quotation.vendor_id,
                'total_amount': quotation.total_amount,
                'delivery_days': quotation.delivery_days,
                'delivery_time': key_info.get('delivery_time', f"{quotation.delivery_days} days"),
                'payment_terms': quotation.payment_terms,
                'warranty_period': key_info.get('warranty_period', 'Not specified'),
                'gst_rate': key_info.get('gst_rate', 'Not specified'),
                'certifications': key_info.get('certifications', []),
                'installation_included': key_info.get('installation_included', False),
                'shipping_included': key_info.get('shipping_included', False),
                'specifications': key_info.get('specifications_summary', []),
                'compliance_standards': key_info.get('compliance_standards', []),
                'quality_score': key_info.get('quality_score', 0.0),
                'completeness_score': key_info.get('completeness_score', 0.0),
                'confidence_score': quotation.confidence_score,
                'status': quotation.status,
                'received_at': quotation.received_at.isoformat() if quotation.received_at else None,
                'created_at': quotation.received_at.isoformat() if quotation.received_at else None
            })

        return jsonify({
            'quotations': quotations_data,
            'total_count': len(quotations_data)
        })

    except Exception as e:
        logger.error(f"❌ Error fetching quotations: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/analyze/<request_id>', methods=['POST'])
def analyze_quotations(request_id):
    """Analyze all quotations for a request using AI and send to managers"""
    try:
        # Get quotations for the request
        quotations = db_manager.get_quotations_by_request(request_id)

        if not quotations:
            return jsonify({'error': 'No quotations found for this request. Please upload quotations first.'}), 404
        
        # Get vendors
        vendor_ids = [q.vendor_id for q in quotations]
        vendors = {}
        for vendor_id in vendor_ids:
            vendor = db_manager.get_vendor(vendor_id)
            if vendor:
                vendors[vendor_id] = vendor
            else:
                logger.error(f"❌ Vendor {vendor_id} not found in database")
                return jsonify({'error': f'Vendor {vendor_id} not found in database'}), 404
        
        # Get procurement request
        procurement_request = db_manager.get_procurement_request(request_id)
        if not procurement_request:
            logger.error(f"❌ Procurement request {request_id} not found in database")
            return jsonify({'error': f'Procurement request {request_id} not found in database'}), 404
        
        # Analyze quotations using AI
        logger.info(f"🤖 Starting AI analysis for {len(quotations)} quotations...")
        analysis_results = quotation_analyzer.analyze_quotations(quotations, vendors, procurement_request)
        
        # Generate comprehensive report
        report = quotation_analyzer.generate_recommendation_report(analysis_results, vendors)
        
        # Send notification to managers
        manager_emails = ['<EMAIL>']  # In real implementation, fetch from database
        email_sent = email_system.notify_managers(procurement_request, analysis_results, report, manager_emails)
        
        # Update quotation AI scores
        for result in analysis_results:
            for quotation in quotations:
                if quotation.id == result.quotation_id:
                    quotation.ai_score = result.total_score * 100
                    db_manager.save_quotation(quotation)
        
        return jsonify({
            'message': 'AI analysis completed successfully',
            'analysis_summary': {
                'total_quotations': len(quotations),
                'recommended_vendor': report['summary']['recommended_vendor'],
                'confidence_level': report['summary']['confidence_level'],
                'top_score': analysis_results[0].total_score if analysis_results else 0
            },
            'detailed_results': [
                {
                    'quotation_id': result.quotation_id,
                    'vendor_id': result.vendor_id,
                    'vendor_name': vendors.get(result.vendor_id).name if vendors.get(result.vendor_id) else 'Unknown Vendor',
                    'rank': result.recommendation_rank,
                    'total_amount': next((q.total_amount for q in quotations if q.id == result.quotation_id), 0),
                    'delivery_days': next((q.delivery_days for q in quotations if q.id == result.quotation_id), 30),
                    'overall_score': result.total_score,
                    'cost_score': result.cost_score,
                    'delivery_score': result.delivery_score,
                    'quality_score': result.quality_score,
                    'risk_score': result.risk_score
                }
                for result in analysis_results
            ],
            'manager_notification_sent': email_sent,
            'full_report': report
        })
        
    except Exception as e:
        logger.error(f"❌ Error analyzing quotations: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/approve/<request_id>', methods=['POST'])
def approve_procurement(request_id):
    """Manager approval endpoint - generates purchase order"""
    try:
        data = request.json
        approved_quotation_id = data.get('quotation_id')
        manager_comments = data.get('comments', '')

        if not approved_quotation_id:
            return jsonify({'error': 'quotation_id is required'}), 400

        # Get approved quotation
        quotations = db_manager.get_quotations_by_request(request_id)
        approved_quotation = next((q for q in quotations if q.id == approved_quotation_id), None)

        if not approved_quotation:
            return jsonify({'error': 'Quotation not found'}), 404

        # Generate PO number
        po_number = f"PO-{datetime.now().strftime('%Y%m%d')}-{request_id[:8]}"

        # Get vendor details
        vendor = db_manager.get_vendor(approved_quotation.vendor_id)
        if not vendor:
            logger.error(f"❌ Vendor {approved_quotation.vendor_id} not found in database")
            return jsonify({'error': f'Vendor {approved_quotation.vendor_id} not found in database'}), 404

        # Create Purchase Order object
        purchase_order = PurchaseOrder(
            id=str(uuid.uuid4()),
            po_number=po_number,
            request_id=request_id,
            quotation_id=approved_quotation_id,
            vendor_id=approved_quotation.vendor_id,
            total_amount=approved_quotation.total_amount,
            po_date=datetime.now(),
            expected_delivery=datetime.now() + timedelta(days=approved_quotation.delivery_days),
            items=[
                {
                    'description': 'Procurement Items',
                    'quantity': 1,
                    'unit_price': approved_quotation.total_amount,
                    'total': approved_quotation.total_amount
                }
            ],
            payment_terms=approved_quotation.payment_terms,
            delivery_address='Company Address',
            delivery_timeline=f"{approved_quotation.delivery_days} days",
            warranty_terms='Standard warranty terms',
            manager_comments=manager_comments,
            status='generated',
            created_at=datetime.now()
        )

        # Save Purchase Order to database
        po_saved = db_manager.save_purchase_order(purchase_order)
        if not po_saved:
            logger.error(f"❌ Failed to save purchase order {po_number}")
            return jsonify({'error': 'Failed to save purchase order'}), 500

        # Create PO data for email
        po_data = {
            'po_number': po_number,
            'po_date': purchase_order.po_date.strftime('%Y-%m-%d'),
            'total_amount': purchase_order.total_amount,
            'expected_delivery': purchase_order.expected_delivery.strftime('%Y-%m-%d'),
            'items': purchase_order.items,
            'payment_terms': purchase_order.payment_terms,
            'delivery_address': purchase_order.delivery_address,
            'delivery_timeline': purchase_order.delivery_timeline,
            'warranty_terms': purchase_order.warranty_terms
        }

        # Send PO confirmation email
        po_sent = email_system.send_po_confirmation(vendor, po_data)

        # Update PO status if email sent successfully
        if po_sent:
            purchase_order.status = 'sent'
            db_manager.save_purchase_order(purchase_order)

        logger.info(f"✅ Purchase Order {po_number} created and saved successfully")

        return jsonify({
            'message': 'Procurement approved and PO generated successfully',
            'po_number': po_number,
            'po_id': purchase_order.id,
            'approved_quotation_id': approved_quotation_id,
            'total_amount': approved_quotation.total_amount,
            'vendor_name': vendor.name,
            'expected_delivery': purchase_order.expected_delivery.strftime('%Y-%m-%d'),
            'po_confirmation_sent': po_sent,
            'manager_comments': manager_comments,
            'po_status': purchase_order.status
        })

    except Exception as e:
        logger.error(f"❌ Error approving procurement: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/vendors', methods=['GET'])
def get_vendors():
    """Get all vendors or filter by category"""
    try:
        category = request.args.get('category')

        if category:
            vendors = db_manager.get_vendors_by_category(category)
        else:
            vendors = db_manager.get_all_vendors()

        return jsonify({
            'vendors': [
                {
                    'id': vendor.id,
                    'name': vendor.name,
                    'email': vendor.email,
                    'phone': vendor.phone,
                    'categories': vendor.categories,
                    'rating': vendor.rating,
                    'performance_score': vendor.performance_score,
                    'location': vendor.location
                }
                for vendor in vendors
            ]
        })

    except Exception as e:
        logger.error(f"❌ Error fetching vendors: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/vendor', methods=['POST'])
def register_vendor():
    """Register a new vendor"""
    try:
        data = request.json
        logger.info(f"📝 Received vendor registration data: {data}")

        # Validate required fields
        required_fields = ['name', 'email', 'phone', 'categories', 'location']
        for field in required_fields:
            if field not in data or not data[field]:
                logger.error(f"❌ Missing required field: {field}")
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Create vendor object
        vendor = Vendor(
            id=str(uuid.uuid4()),
            name=data['name'],
            email=data['email'],
            phone=data['phone'],
            categories=data['categories'],
            rating=data.get('rating', 4.0),
            performance_score=data.get('performance_score', 75.0),
            payment_terms=data.get('payment_terms', '30 days credit'),
            delivery_capability=data.get('delivery_capability', 'Regional'),
            location=data['location'],
            is_active=data.get('is_active', True)
        )

        # Save to database
        success = db_manager.save_vendor(vendor)

        if success:
            return jsonify({
                'message': 'Vendor registered successfully',
                'vendor_id': vendor.id,
                'vendor_name': vendor.name,
                'email': vendor.email,
                'categories': vendor.categories
            })
        else:
            return jsonify({'error': 'Failed to save vendor to database'}), 500

    except Exception as e:
        logger.error(f"❌ Error registering vendor: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/vendor/<vendor_id>', methods=['PUT'])
def update_vendor(vendor_id):
    """Update an existing vendor"""
    try:
        data = request.json
        logger.info(f"📝 Updating vendor {vendor_id}: {data}")

        # Get existing vendor
        existing_vendor = db_manager.get_vendor(vendor_id)
        if not existing_vendor:
            return jsonify({'error': 'Vendor not found'}), 404

        # Update vendor object
        updated_vendor = Vendor(
            id=vendor_id,
            name=data.get('name', existing_vendor.name),
            email=data.get('email', existing_vendor.email),
            phone=data.get('phone', existing_vendor.phone),
            categories=data.get('categories', existing_vendor.categories),
            rating=data.get('rating', existing_vendor.rating),
            performance_score=data.get('performance_score', existing_vendor.performance_score),
            payment_terms=data.get('payment_terms', existing_vendor.payment_terms),
            delivery_capability=data.get('delivery_capability', existing_vendor.delivery_capability),
            location=data.get('location', existing_vendor.location),
            is_active=data.get('is_active', existing_vendor.is_active)
        )

        # Save to database
        success = db_manager.save_vendor(updated_vendor)

        if success:
            return jsonify({
                'message': 'Vendor updated successfully',
                'vendor_id': updated_vendor.id,
                'vendor_name': updated_vendor.name
            })
        else:
            return jsonify({'error': 'Failed to update vendor in database'}), 500

    except Exception as e:
        logger.error(f"❌ Error updating vendor: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/email/auto-process', methods=['POST'])
def auto_process_email_quotations():
    """Automatically read emails and process quotations with AI analysis"""
    try:
        data = request.get_json() or {}
        request_id = data.get('request_id')  # Optional: filter by specific request

        logger.info(f"🤖 Starting automated email quotation processing for request: {request_id or 'ALL'}")

        # Run automated processing
        result = email_reader.auto_process_email_quotations(request_id)

        if result['success']:
            return jsonify({
                'success': True,
                'message': result['message'],
                'processed_count': result['processed_count'],
                'quotations_by_request': result['quotations_by_request'],
                'analysis_available': bool(result['analysis_results']),
                'analysis_results': result['analysis_results']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['message'],
                'processed_count': result['processed_count']
            }), 400

    except Exception as e:
        logger.error(f"❌ Error in auto-processing endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/email/pending-requests', methods=['GET'])
def get_pending_quotation_requests():
    """Get procurement requests that are waiting for quotations"""
    try:
        pending_requests = email_reader.get_pending_quotation_requests()

        return jsonify({
            'success': True,
            'pending_requests': pending_requests,
            'count': len(pending_requests)
        })

    except Exception as e:
        logger.error(f"❌ Error getting pending requests: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/email/check-quotations', methods=['POST'])
def check_email_quotations():
    """Check for new quotation emails without processing them"""
    try:
        data = request.get_json() or {}
        days_back = data.get('days_back', 7)

        logger.info(f"📧 Checking for quotation emails from last {days_back} days")

        request_id = data.get('request_id')  # Optional: filter by specific request

        if request_id:
            logger.info(f"📧 Checking for quotation emails for request {request_id} from last {days_back} days")
            # Clean up existing quotations when checking for a specific request
            email_reader.cleanup_duplicate_quotations(request_id)
        else:
            logger.info(f"📧 Checking for quotation emails from last {days_back} days")

        # Read quotation emails with optional request filtering
        email_quotations = email_reader.read_quotation_emails(days_back, request_id)

        # Format response
        quotations_info = []
        for eq in email_quotations:
            quotations_info.append({
                'vendor_name': eq.vendor_name,
                'vendor_email': eq.vendor_email,
                'subject': eq.subject,
                'received_date': eq.received_date.isoformat(),
                'attachment_name': eq.attachment_name,
                'request_id': eq.request_id,
                'has_request_id': bool(eq.request_id)
            })

        message = f'Found {len(quotations_info)} quotation emails'
        if request_id:
            message += f' for request {request_id}'

        return jsonify({
            'success': True,
            'quotations_found': len(quotations_info),
            'quotations': quotations_info,
            'message': message
        })

    except Exception as e:
        logger.error(f"❌ Error checking email quotations: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/email/process-rfq-emails', methods=['POST'])
def process_rfq_specific_emails():
    """Process emails specifically for a given RFQ request ID"""
    try:
        data = request.get_json() or {}
        request_id = data.get('request_id')

        if not request_id:
            return jsonify({'error': 'request_id is required'}), 400

        logger.info(f"🎯 Processing emails specifically for RFQ: {request_id}")

        # Run targeted email processing
        result = email_reader.auto_process_email_quotations(request_id)

        if result['success']:
            return jsonify({
                'success': True,
                'message': result['message'],
                'processed_count': result['processed_count'],
                'analysis_results': result['analysis_results'],
                'quotations_by_request': result['quotations_by_request'],
                'analysis_available': bool(result['analysis_results']),
                'targeted_processing': True,
                'request_id': request_id
            })
        else:
            return jsonify({
                'success': False,
                'error': result['message'],
                'processed_count': result['processed_count'],
                'targeted_processing': True,
                'request_id': request_id
            }), 400

    except Exception as e:
        logger.error(f"❌ Error in RFQ-specific email processing: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/cleanup-duplicates/<request_id>', methods=['POST'])
def cleanup_duplicate_quotations(request_id):
    """Manually cleanup duplicate quotations for a specific request"""
    try:
        logger.info(f"🧹 Manual cleanup requested for request: {request_id}")

        # Get quotations before cleanup
        before_quotations = mongodb_manager.get_quotations_by_request(request_id)
        before_count = len(before_quotations)

        # Run cleanup
        email_reader.cleanup_duplicate_quotations(request_id)

        # Get remaining quotations count
        after_quotations = mongodb_manager.get_quotations_by_request(request_id)
        after_count = len(after_quotations)
        removed_count = before_count - after_count

        return jsonify({
            'success': True,
            'message': f'Cleanup completed. Removed {removed_count} duplicates, {after_count} quotations remaining.',
            'before_count': before_count,
            'after_count': after_count,
            'removed_count': removed_count
        })

    except Exception as e:
        logger.error(f"❌ Error cleaning up duplicates: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/vendor/<vendor_id>', methods=['DELETE'])
def delete_vendor(vendor_id):
    """Delete a vendor"""
    try:
        success = db_manager.delete_vendor(vendor_id)

        if success:
            return jsonify({
                'message': 'Vendor deleted successfully',
                'vendor_id': vendor_id
            })
        else:
            return jsonify({'error': 'Vendor not found or failed to delete'}), 404

    except Exception as e:
        logger.error(f"❌ Error deleting vendor: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/categories', methods=['GET'])
def get_categories():
    """Get available procurement categories"""
    categories = [
        {'id': 'cement', 'name': 'Cement & Concrete', 'description': 'PPC, OPC, Ready Mix Concrete'},
        {'id': 'steel', 'name': 'Steel & TMT Bars', 'description': 'TMT Bars, Steel Sheets, Structural Steel'},
        {'id': 'pipes', 'name': 'Pipes & Fittings', 'description': 'HDPE, PVC, MS Pipes and Fittings'},
        {'id': 'electrical', 'name': 'Electrical Equipment', 'description': 'Motors, Cables, Switches, Panels'},
        {'id': 'machinery', 'name': 'Machinery & Tools', 'description': 'Construction Equipment, Hand Tools'},
        {'id': 'aggregates', 'name': 'Aggregates & Sand', 'description': 'Crushed Stone, Sand, Gravel'},
        {'id': 'wood', 'name': 'Wood & Timber', 'description': 'Plywood, Timber, Wooden Panels'}
    ]

    return jsonify({'categories': categories})

@app.route('/api/procurement/ims-products', methods=['GET'])
def get_ims_products():
    """Get products from existing IMS system"""
    try:
        products = db_manager.get_existing_products()
        return jsonify({
            'products': products,
            'total_count': len(products),
            'message': f'Found {len(products)} products in IMS system'
        })

    except Exception as e:
        logger.error(f"❌ Error fetching IMS products: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/ims-stores', methods=['GET'])
def get_ims_stores():
    """Get stores from existing IMS system"""
    try:
        stores = db_manager.get_existing_stores()
        return jsonify({
            'stores': stores,
            'total_count': len(stores),
            'message': f'Found {len(stores)} stores in IMS system'
        })

    except Exception as e:
        logger.error(f"❌ Error fetching IMS stores: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/database-stats', methods=['GET'])
def get_database_stats():
    """Get database statistics including IMS data"""
    try:
        stats = db_manager.get_database_stats()
        return jsonify({
            'database_stats': stats,
            'database_name': db_manager.database_name,
            'connection_status': 'connected'
        })

    except Exception as e:
        logger.error(f"❌ Error fetching database stats: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/purchase-orders', methods=['GET'])
def get_purchase_orders():
    """Get all purchase orders"""
    try:
        purchase_orders = db_manager.get_all_purchase_orders()

        po_data = []
        for po in purchase_orders:
            vendor = db_manager.get_vendor(po.vendor_id)
            procurement_request = db_manager.get_procurement_request(po.request_id)

            po_data.append({
                'id': po.id,
                'po_number': po.po_number,
                'request_id': po.request_id,
                'quotation_id': po.quotation_id,
                'vendor_id': po.vendor_id,
                'vendor_name': vendor.name if vendor else 'Unknown Vendor',
                'vendor_email': vendor.email if vendor else 'N/A',
                'total_amount': po.total_amount,
                'po_date': po.po_date.isoformat() if po.po_date else None,
                'expected_delivery': po.expected_delivery.isoformat() if po.expected_delivery else None,
                'items': po.items,
                'payment_terms': po.payment_terms,
                'delivery_address': po.delivery_address,
                'delivery_timeline': po.delivery_timeline,
                'warranty_terms': po.warranty_terms,
                'manager_comments': po.manager_comments,
                'status': po.status,
                'created_at': po.created_at.isoformat() if po.created_at else None,
                'category': procurement_request.category if procurement_request else 'Unknown',
                'created_by': procurement_request.created_by if procurement_request else 'Unknown'
            })

        return jsonify({
            'purchase_orders': po_data,
            'total_count': len(po_data)
        })

    except Exception as e:
        logger.error(f"❌ Error fetching purchase orders: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/purchase-order/<po_number>', methods=['GET'])
def get_purchase_order_details(po_number):
    """Get detailed information about a specific purchase order"""
    try:
        purchase_order = db_manager.get_purchase_order_by_number(po_number)

        if not purchase_order:
            return jsonify({'error': 'Purchase order not found'}), 404

        # Get related information
        vendor = db_manager.get_vendor(purchase_order.vendor_id)
        procurement_request = db_manager.get_procurement_request(purchase_order.request_id)
        quotations = db_manager.get_quotations_by_request(purchase_order.request_id)
        approved_quotation = next((q for q in quotations if q.id == purchase_order.quotation_id), None)

        po_details = {
            'id': purchase_order.id,
            'po_number': purchase_order.po_number,
            'request_id': purchase_order.request_id,
            'quotation_id': purchase_order.quotation_id,
            'vendor_id': purchase_order.vendor_id,
            'total_amount': purchase_order.total_amount,
            'po_date': purchase_order.po_date.isoformat() if purchase_order.po_date else None,
            'expected_delivery': purchase_order.expected_delivery.isoformat() if purchase_order.expected_delivery else None,
            'items': purchase_order.items,
            'payment_terms': purchase_order.payment_terms,
            'delivery_address': purchase_order.delivery_address,
            'delivery_timeline': purchase_order.delivery_timeline,
            'warranty_terms': purchase_order.warranty_terms,
            'manager_comments': purchase_order.manager_comments,
            'status': purchase_order.status,
            'created_at': purchase_order.created_at.isoformat() if purchase_order.created_at else None,
            'vendor_details': {
                'name': vendor.name if vendor else 'Unknown Vendor',
                'email': vendor.email if vendor else 'N/A',
                'phone': vendor.phone if vendor else 'N/A',
                'location': vendor.location if vendor else 'N/A',
                'rating': vendor.rating if vendor else 0.0
            } if vendor else None,
            'procurement_request': {
                'category': procurement_request.category if procurement_request else 'Unknown',
                'items': procurement_request.items if procurement_request else [],
                'specifications': procurement_request.specifications if procurement_request else '',
                'created_by': procurement_request.created_by if procurement_request else 'Unknown',
                'urgency': procurement_request.urgency.value if procurement_request and hasattr(procurement_request.urgency, 'value') else 'Unknown'
            } if procurement_request else None,
            'quotation_details': {
                'delivery_days': approved_quotation.delivery_days if approved_quotation else 0,
                'ai_score': approved_quotation.ai_score if approved_quotation else 0.0,
                'confidence_score': approved_quotation.confidence_score if approved_quotation else 0.0
            } if approved_quotation else None
        }

        return jsonify({
            'purchase_order': po_details
        })

    except Exception as e:
        logger.error(f"❌ Error fetching purchase order details: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/purchase-order/<po_number>/status', methods=['PUT'])
def update_purchase_order_status(po_number):
    """Update purchase order status"""
    try:
        data = request.json
        new_status = data.get('status')

        if not new_status:
            return jsonify({'error': 'Status is required'}), 400

        valid_statuses = ['generated', 'sent', 'acknowledged', 'delivered', 'completed']
        if new_status not in valid_statuses:
            return jsonify({'error': f'Invalid status. Valid statuses: {valid_statuses}'}), 400

        purchase_order = db_manager.get_purchase_order_by_number(po_number)
        if not purchase_order:
            return jsonify({'error': 'Purchase order not found'}), 404

        # Update status
        purchase_order.status = new_status
        success = db_manager.save_purchase_order(purchase_order)

        if success:
            logger.info(f"✅ Purchase Order {po_number} status updated to {new_status}")
            return jsonify({
                'message': f'Purchase order status updated to {new_status}',
                'po_number': po_number,
                'new_status': new_status
            })
        else:
            return jsonify({'error': 'Failed to update purchase order status'}), 500

    except Exception as e:
        logger.error(f"❌ Error updating purchase order status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/test-email', methods=['POST'])
def test_email_system():
    """Test email system configuration"""
    try:
        data = request.json
        test_email = data.get('email', '<EMAIL>')

        # Create a test email
        test_subject = "🧪 Procurement System Email Test"
        test_content = """
        <html>
        <body>
            <h2>✅ Email System Test Successful!</h2>
            <p>This is a test email from the AI Procurement System.</p>
            <p><strong>System Status:</strong> Email configuration is working correctly.</p>
            <p><strong>Timestamp:</strong> {timestamp}</p>
            <hr>
            <p><em>This is an automated test message.</em></p>
        </body>
        </html>
        """.format(timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # Send test email
        success = email_system.send_email(test_email, test_subject, test_content)

        if success:
            return jsonify({
                'message': 'Test email sent successfully',
                'email_sent_to': test_email,
                'status': 'success'
            })
        else:
            return jsonify({
                'message': 'Failed to send test email',
                'email_attempted': test_email,
                'status': 'failed'
            }), 500

    except Exception as e:
        logger.error(f"❌ Error testing email system: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/procurement/clean-sample-data', methods=['DELETE'])
def clean_sample_data():
    """Remove all sample/demo data from the system"""
    try:
        # Remove sample vendors
        sample_vendor_names = [
            "Premium Cement Suppliers",
            "Steel & Pipes Industries",
            "Electrical Equipment Co.",
            "Standard Building Materials",
            "Budget Construction Supply"
        ]

        vendors_removed = 0
        for vendor_name in sample_vendor_names:
            vendors = db_manager.get_all_vendors()
            for vendor in vendors:
                if vendor.name in sample_vendor_names:
                    try:
                        db_manager.delete_vendor(vendor.id)
                        vendors_removed += 1
                        logger.info(f"🗑️ Removed sample vendor: {vendor.name}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not remove vendor {vendor.name}: {e}")

        # Remove sample procurement requests and quotations
        # Note: In a real implementation, you'd have proper cleanup methods

        return jsonify({
            'message': 'Sample data cleanup completed',
            'vendors_removed': vendors_removed,
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"❌ Error cleaning sample data: {e}")
        return jsonify({'error': str(e)}), 500



if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
