#!/usr/bin/env python3
"""
Test script for enhanced email reading with text content support
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
sys.path.append(os.path.dirname(__file__))

from email_reader import EmailQuotationReader

def test_text_quotation_parsing():
    """Test the text quotation parsing functionality"""
    print("🧪 Testing Text Quotation Parsing...")
    
    reader = EmailQuotationReader()
    
    # Test cases with different email content
    test_cases = [
        {
            'subject': 'Re: RFQ: cement - 1fda7b1e-a371-4c3f-9642-1079b7708652',
            'body': '''Dear Sir,

Thank you for your RFQ for cement. Please find our quotation below:

Item: Portland Cement (Grade 53)
Quantity: 100 bags
Price: ₹45,000 per 100 bags
Total Amount: ₹45,000

Delivery: 5 days from order confirmation
Payment Terms: 30 days credit
Validity: 15 days

Best regards,
Vendor Team''',
            'vendor': 'Test Vendor'
        },
        {
            'subject': 'Quotation for Steel Bars',
            'body': '''Hi,

Our quote for steel bars:
- Rate: Rs. 65,000 per ton
- Delivery: within 7 days
- Payment: 45 days credit
- Valid for 30 days

Thanks''',
            'vendor': 'Steel Supplier'
        },
        {
            'subject': 'Price for Bricks',
            'body': '''Hello,

Brick price: ₹8 per piece
For 10,000 pieces: ₹80,000
Delivery in 3 days
Payment terms: Cash on delivery

Regards''',
            'vendor': 'Brick Manufacturer'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📧 Test Case {i}: {test_case['subject']}")
        
        # Test quotation detection
        is_quotation = reader.is_quotation_email_content(test_case['subject'], test_case['body'])
        print(f"   Quotation Detected: {'✅ YES' if is_quotation else '❌ NO'}")
        
        if is_quotation:
            # Test quotation parsing
            parsed_data = reader.parse_quotation_from_text(
                test_case['subject'], 
                test_case['body'], 
                test_case['vendor']
            )
            
            print(f"   Confidence Score: {parsed_data['confidence']:.1%}")
            
            extracted_info = parsed_data.get('extracted_info', {})
            if 'total_amount' in extracted_info:
                print(f"   💰 Total Amount: ₹{extracted_info['total_amount']:,.2f}")
            if 'delivery_days' in extracted_info:
                print(f"   🚚 Delivery: {extracted_info['delivery_days']} days")
            if 'payment_terms' in extracted_info:
                print(f"   💳 Payment: {extracted_info['payment_terms']}")
            if 'validity_days' in extracted_info:
                print(f"   ⏰ Validity: {extracted_info['validity_days']} days")

def test_enhanced_email_reading():
    """Test the enhanced email reading with current emails"""
    print("\n🔍 Testing Enhanced Email Reading...")
    
    reader = EmailQuotationReader()
    
    try:
        # Read quotation emails (both attachments and text)
        email_quotations = reader.read_quotation_emails(days_back=7)
        
        print(f"📊 Found {len(email_quotations)} quotation emails")
        
        for quotation in email_quotations:
            print(f"\n📧 Quotation from {quotation.vendor_name}:")
            print(f"   Subject: {quotation.subject}")
            print(f"   Type: {'📎 Attachment' if not quotation.attachment_name.endswith('_email_quotation.txt') else '📧 Email Text'}")
            print(f"   File: {quotation.attachment_name}")
            print(f"   Request ID: {quotation.request_id or 'Not found'}")
        
        if len(email_quotations) == 0:
            print("ℹ️  No quotation emails found. This could mean:")
            print("   1. No emails with attachments")
            print("   2. No emails with quotation content in text")
            print("   3. Emails don't match quotation patterns")
        
    except Exception as e:
        print(f"❌ Error testing enhanced email reading: {e}")

def main():
    """Main test function"""
    print("🚀 Enhanced Email Reading System Test")
    print("=" * 50)
    
    # Test 1: Text quotation parsing
    test_text_quotation_parsing()
    
    # Test 2: Enhanced email reading
    test_enhanced_email_reading()
    
    print("\n✅ Enhanced email reading tests completed!")
    print("\n💡 The system now supports:")
    print("   📎 Quotation attachments (PDF, DOC, XLS)")
    print("   📧 Quotation text in email body")
    print("   🤖 Automatic detection and parsing")
    print("   📊 Confidence scoring")

if __name__ == '__main__':
    main()
