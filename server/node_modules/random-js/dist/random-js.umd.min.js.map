{"version": 3, "sources": ["../src/utils/constants.ts", "../src/distribution/int32.ts", "../src/utils/add.ts", "../src/distribution/int53.ts", "../src/distribution/int53Full.ts", "../src/distribution/uint32.ts", "../src/distribution/uint53.ts", "../src/distribution/uint53Full.ts", "../src/distribution/integer.ts", "../src/distribution/bool.ts", "../src/distribution/date.ts", "../src/distribution/die.ts", "../src/distribution/dice.ts", "../src/distribution/string.ts", "../src/distribution/hex.ts", "../src/utils/convertSliceArgument.ts", "../src/utils/toInteger.ts", "../src/distribution/pick.ts", "../src/distribution/realZeroToOneExclusive.ts", "../src/distribution/realZeroToOneInclusive.ts", "../src/distribution/real.ts", "../src/utils/multiply.ts", "../src/utils/sliceArray.ts", "../src/distribution/shuffle.ts", "../src/distribution/sample.ts", "../src/utils/stringRepeat.ts", "../src/distribution/uuid4.ts", "../src/engine/nativeMath.ts", "../src/Random.ts", "../src/utils/Int32Array.ts", "../src/engine/browserCrypto.ts", "../src/utils/createEntropy.ts", "../src/utils/imul.ts", "../src/engine/MersenneTwister19937.ts", "../src/engine/nodeCrypto.ts", "../src/distribution/picker.ts"], "names": ["SMALLEST_UNSAFE_INTEGER", "LARGEST_SAFE_INTEGER", "UINT32_MAX", "UINT32_SIZE", "INT32_SIZE", "INT32_MAX", "UINT21_SIZE", "UINT21_MAX", "int32", "engine", "next", "add", "distribution", "addend", "int53", "high", "low", "int53Full", "uint32", "uint53", "uint53Full", "isPowerOfTwoMinusOne", "value", "downscaleToRange", "range", "masking", "extendedRange", "maximum", "Math", "floor", "downscaleToLoopCheckedRange", "upscaleWithinU53", "highRange", "ret", "upscaleToLoopCheckedRange", "upscaleWithinI53AndLoopCheck", "min", "max", "integer", "isFinite", "RangeError", "isLeastBitTrue", "lessThan", "bool", "numerator", "denominator", "percentage", "scaled", "round", "probability", "date", "start", "end", "Date", "die", "sideCount", "dice", "dieCount", "result", "i", "push", "DEFAULT_STRING_POOL", "string", "pool", "<PERSON><PERSON><PERSON><PERSON>", "length", "Error", "j", "char<PERSON>t", "lowerHex", "upperHex", "toUpperCase", "hex", "uppercase", "convertSliceArgument", "toInteger", "num", "ceil", "pick", "source", "begin", "finish", "realZeroToOneExclusive", "realZeroToOneInclusive", "real", "inclusive", "multiplier", "sliceArray", "Array", "prototype", "slice", "shuffle", "array", "downTo", "tmp", "sample", "population", "sampleSize", "clone", "call", "tailLength", "stringRepeat", "repeat", "pattern", "count", "_", "zeroPad", "text", "zeroCount", "uuid4", "a", "b", "c", "d", "toString", "nativeMath", "random", "Random", "this", "I32Array", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "Int32Array", "data", "index", "browserCrypto", "crypto", "getRandomValues", "createEntropy", "getTime", "imul", "al", "bl", "ARRAY_SIZE", "ARRAY_MAX", "M", "ARRAY_SIZE_MINUS_M", "A", "MersenneTwister19937", "uses", "seed", "initial", "seedWithArray", "autoSeed", "refreshData", "temper", "getUseCount", "discard", "previous", "sourceLength", "k", "nodeCrypto", "Int8Array", "require", "randomBytes"], "mappings": "mMAAO,IAAMA,EAA0B,iBAC1BC,EAAuBD,EAA0B,EACjDE,GAAc,IAAM,EACpBC,EAAcD,EAAa,EAC3BE,EAAaD,EAAc,EAC3BE,EAAYD,EAAa,EACzBE,EAAc,GAAK,GACnBC,EAAaD,EAAc,ECFxC,SAAgBE,EAAMC,GACpB,OAAuB,EAAhBA,EAAOC,gBCJAC,EAAIC,EAA4BC,GAC9C,OAAe,IAAXA,EACKD,EAEA,SAAAH,GAAU,OAAAG,EAAaH,GAAUI,GCK5C,SAAgBC,EAAML,GACpB,IAAMM,EAAuB,EAAhBN,EAAOC,OACdM,EAAMP,EAAOC,SAAW,EAC9B,OACGK,EAAOR,GAAcJ,EACtBa,GACCD,EAAOT,GAAeN,EAA0B,GCNrD,SAAgBiB,EAAUR,GACxB,OAAa,CACX,IAAMM,EAAuB,EAAhBN,EAAOC,OACpB,KAAW,QAAPK,GAIG,CACL,IAAMC,EAAMP,EAAOC,SAAW,EAC9B,OACGK,EAAOR,GAAcJ,EACtBa,GACCD,EAAOT,GAAeN,EAA0B,GARnD,GAA0B,UAAd,QAAPe,IAAyD,IAAP,EAAhBN,EAAOC,QAC5C,OAAOV,GCXf,SAAgBkB,EAAOT,GACrB,OAAOA,EAAOC,SAAW,ECA3B,SAAgBS,EAAOV,GACrB,IAAMM,EAAON,EAAOC,OAASH,EACvBS,EAAMP,EAAOC,SAAW,EAC9B,OAAOK,EAAOZ,EAAca,ECE9B,SAAgBI,EAAWX,GACzB,OAAa,CACX,IAAMM,EAAuB,EAAhBN,EAAOC,OACpB,KAAIK,EAAOT,GAIJ,CACL,IAAMU,EAAMP,EAAOC,SAAW,EAC9B,OAAQK,EAAOR,GAAcJ,EAAca,EAL3C,GAA4B,IAAvBD,EAAOR,IAA6C,IAAP,EAAhBE,EAAOC,QACvC,OAAOV,GCEf,SAASqB,EAAqBC,GAC5B,OAAiC,IAAxBA,EAAQ,EAAKA,GAmBxB,SAASC,EAAiBC,GACxB,OAAIH,EAAqBG,IAjBVC,EAkBED,EAjBV,SAACf,GAAmB,OAAAA,EAAOC,OAASe,IAG7C,SAAqCD,GACnC,IAAME,EAAgBF,EAAQ,EACxBG,EAAUD,EAAgBE,KAAKC,MAAM1B,EAAcuB,GACzD,OAAO,SAAAjB,GACL,IAAIa,EAAQ,EACZ,GACEA,EAAQb,EAAOC,SAAW,QACnBY,GAASK,GAClB,OAAOL,EAAQI,GAQRI,CAA4BN,GApBvC,IAAiBC,EAkDjB,SAASM,EAAiBP,GACxB,IAvB8BC,EAuBxBC,EAAgBF,EAAQ,EAC9B,GA3BuB,IAAP,EA2BgBE,GAAgB,CAC9C,IAAMM,GAAcN,EAAgBvB,EAAe,GAAK,EACxD,GAAIkB,EAAqBW,GACvB,OA3B0BP,EA2BIO,EA1B3B,SAAAvB,GACL,IAAMM,EAAON,EAAOC,OAASe,EACvBT,EAAMP,EAAOC,SAAW,EAC9B,OAAOK,EAAOZ,EAAca,GA0B9B,OAtBF,SAAmCU,GACjC,IAAMC,EACJD,EAAgBE,KAAKC,MAAM7B,EAA0B0B,GACvD,OAAO,SAAAjB,GACL,IAAIwB,EAAM,EACV,EAAG,CACD,IAAMlB,EAAON,EAAOC,OAASH,EACvBS,EAAMP,EAAOC,SAAW,EAC9BuB,EAAMlB,EAAOZ,EAAca,QACpBiB,GAAON,GAChB,OAAOM,EAAMP,GAYRQ,CAA0BR,GAGnC,SAASS,EAA6BC,EAAaC,GACjD,OAAO,SAAA5B,GACL,IAAIwB,EAAM,EACV,EAAG,CACD,IAAMlB,EAAuB,EAAhBN,EAAOC,OACdM,EAAMP,EAAOC,SAAW,EAC9BuB,GACGlB,EAAOR,GAAcJ,EACtBa,GACCD,EAAOT,GAAeN,EAA0B,SAC5CiC,EAAMG,GAAOH,EAAMI,GAC5B,OAAOJ,GASX,SAAgBK,EAAQF,EAAaC,GAGnC,GAFAD,EAAMR,KAAKC,MAAMO,GACjBC,EAAMT,KAAKC,MAAMQ,GACbD,GAAOpC,IAA4BuC,SAASH,GAC9C,MAAM,IAAII,WACR,gCAAgCxC,GAE7B,GAAIqC,EAAMrC,IAA4BuC,SAASF,GACpD,MAAM,IAAIG,WACR,8BAA8BxC,GAIlC,IAAMwB,EAAQa,EAAMD,EACpB,OAAIZ,GAAS,IAAMe,SAASf,GACnB,WAAM,OAAAY,GACJZ,IAAUtB,EACP,IAARkC,EACKlB,EAEAP,EAAIH,EAAO4B,EAAMhC,GAEjBoB,EAAQtB,EACVS,EAAIY,EAAiBC,GAAQY,GAC3BZ,IAAUvB,EACZU,EAAIQ,EAAQiB,GACVZ,EAAQvB,EACVU,EAAIoB,EAAiBP,GAAQY,GAC3BC,EAAM,EAAID,IAAQnC,EACpBU,EAAIS,EAAYgB,GAEvBA,KAASpC,GACTqC,IAAQrC,EAEDiB,EACEmB,KAASpC,GAA2BqC,IAAQpC,EAC9Ca,EACEsB,KAASnC,GAAwBoC,IAAQrC,EAC3CW,EAAIG,EAAO,GACTuB,IAAQrC,EACVW,EAAIwB,EAA6BC,EAAM,EAAGC,EAAM,GAAI,GAEpDF,EAA6BC,EAAKC,GC3I7C,SAASI,EAAehC,GACtB,OAA+B,IAAP,EAAhBA,EAAOC,QAGjB,SAASgC,EACP9B,EACAU,GAEA,OAAO,SAAAb,GAAU,OAAAG,EAAaH,GAAUa,GAuC1C,SAAgBqB,EACdC,EACAC,GAEA,OAAmB,MAAfA,EACe,MAAbD,EACKH,EA1Cb,SAAqBK,GACnB,GAAIA,GAAc,EAChB,OAAO,WAAM,OAAA,GACR,GAAIA,GAAc,EACvB,OAAO,WAAM,OAAA,GAEb,IAAMC,EAASD,EAAa3C,EAC5B,OAAI4C,EAAS,GAAM,EACVL,EAASlC,EAAQuC,EAAS3C,EAAc,GAExCsC,EAASvB,EAAQS,KAAKoB,MAAMF,EAAa9C,IAkC3CiD,CAAYL,GAEfA,GAAc,EACT,WAAM,OAAA,GACJA,GAAcC,EAChB,WAAM,OAAA,GAERH,EAASJ,EAAQ,EAAGO,EAAc,GAAID,GC3DjD,SAAgBM,EAAKC,EAAaC,GAChC,IAAMxC,EAAe0B,GAASa,GAAQC,GACtC,OAAO,SAAA3C,GAAU,OAAA,IAAI4C,KAAKzC,EAAaH,KCJzC,SAAgB6C,EAAIC,GAClB,OAAOjB,EAAQ,EAAGiB,GCCpB,SAAgBC,EACdD,EACAE,GAEA,IAAM7C,EAAe0C,EAAIC,GACzB,OAAO,SAAA9C,GAEL,IADA,IAAMiD,EAAS,GACNC,EAAI,EAAGA,EAAIF,IAAYE,EAC9BD,EAAOE,KAAKhD,EAAaH,IAE3B,OAAOiD,GCbX,IAAMG,EACJ,mEAcF,SAAgBC,EAAOC,QAAA,IAAAA,IAAAA,EAAAF,GACrB,IAAMG,EAAaD,EAAKE,OACxB,IAAKD,EACH,MAAM,IAAIE,MAAM,2CAGlB,IAAMtD,EAAe0B,EAAQ,EAAG0B,EAAa,GAC7C,OAAO,SAACvD,EAAQwD,GAEd,IADA,IAAIP,EAAS,GACJC,EAAI,EAAGA,EAAIM,IAAUN,EAAG,CAC/B,IAAMQ,EAAIvD,EAAaH,GACvBiD,GAAUK,EAAKK,OAAOD,GAExB,OAAOT,GC/BX,IACMW,EAAWP,EADM,oBAEjBQ,EAAWR,EAFM,mBAEgBS,eAQvC,SAAgBC,EAAIC,GAClB,OAAIA,EACKH,EAEAD,WCjBKK,EAAqBpD,EAAe2C,GAClD,OAAI3C,EAAQ,EACHM,KAAKS,IAAIf,EAAQ2C,EAAQ,GAEzBrC,KAAKQ,IAAId,EAAO2C,YCJXU,EAAUrD,GACxB,IAAMsD,GAAOtD,EACb,OAAIsD,EAAM,EACDhD,KAAKiD,KAAKD,GAEVhD,KAAKC,MAAM+C,GCOtB,SAAgBE,EACdrE,EACAsE,EACAC,EACA5B,GAEA,IAAMa,EAASc,EAAOd,OACtB,GAAe,IAAXA,EACF,MAAM,IAAIzB,WAAW,mCAEvB,IAAMW,EACK,MAAT6B,EAAgB,EAAIN,EAAqBC,EAAUK,GAAQf,GACvDgB,OACI,IAAR7B,EAAiBa,EAASS,EAAqBC,EAAUvB,GAAMa,GACjE,GAAId,GAAS8B,EACX,MAAM,IAAIzC,WAAW,8BAA8BW,EAAK,QAAQ8B,GAGlE,OAAOF,EADczC,EAAQa,EAAO8B,EAAS,EAC/BrE,CAAaH,ICvB7B,SAAgByE,EAAuBzE,GACrC,OAAOU,EAAOV,GAAUT,ECD1B,SAAgBmF,EAAuB1E,GACrC,OAAOW,EAAWX,GAAUT,ECI9B,SAAgBoF,EACdhD,EACAC,EACAgD,GAEA,QAFA,IAAAA,IAAAA,GAAA,IAEK9C,SAASH,GACZ,MAAM,IAAII,WAAW,sCAChB,IAAKD,SAASF,GACnB,MAAM,IAAIG,WAAW,sCAEvB,OAAO7B,GCnBPC,EDqBIyE,EAAYF,EAAyBD,EClBtB,KAFnBI,EDqBIjD,EAAMD,GClBDxB,EACiB,IAAf0E,EACF,WAAM,OAAA,GAEN,SAAA7E,GAAU,OAAAG,EAAaH,GAAU6E,IDgBxClD,OCxBFxB,EACA0E,ECJK,IAAMC,EAAaC,MAAMC,UAAUC,MCS1C,SAAgBC,EACdlF,EACAmF,EACAC,QAAA,IAAAA,IAAAA,EAAA,GAEA,IAAM5B,EAAS2B,EAAM3B,OACrB,GAAIA,EACF,IAAK,IAAIN,EAAKM,EAAS,IAAO,EAAGN,EAAIkC,IAAUlC,EAAG,CAChD,IACMQ,EADe7B,EAAQ,EAAGqB,EACtB/C,CAAaH,GACvB,GAAIkD,IAAMQ,EAAG,CACX,IAAM2B,EAAMF,EAAMjC,GAClBiC,EAAMjC,GAAKiC,EAAMzB,GACjByB,EAAMzB,GAAK2B,GAIjB,OAAOF,ECfT,SAAgBG,EACdtF,EACAuF,EACAC,GAEA,GACEA,EAAa,GACbA,EAAaD,EAAW/B,SACvB1B,SAAS0D,GAEV,MAAM,IAAIzD,WACR,uEAIJ,GAAmB,IAAfyD,EACF,MAAO,GAGT,IAAMC,EAAQX,EAAWY,KAAKH,GACxB/B,EAASiC,EAAMjC,OACrB,GAAIA,IAAWgC,EACb,OAAON,EAAQlF,EAAQyF,EAAO,GAEhC,IAAME,EAAanC,EAASgC,EAC5B,OAAON,EAAQlF,EAAQyF,EAAOE,EAAa,GAAGV,MAAMU,GCpC/C,IAAMC,EAAe,WAC1B,IACE,GAA+B,QAA1B,IAAYC,OAAO,GACtB,OAAO,SAACC,EAAiBC,GACvB,OAACD,EAAgBD,OAAOE,IAE5B,MAAOC,IAGT,OAAO,SAACF,EAAiBC,GAEvB,IADA,IAAI9C,EAAS,GACN8C,EAAQ,GACD,EAARA,IACF9C,GAAU6C,GAEZC,IAAU,EACVD,GAAWA,EAEb,OAAO7C,GAlBiB,GCG5B,SAASgD,EAAQC,EAAcC,GAC7B,OAAOP,EAAa,IAAKO,EAAYD,EAAK1C,QAAU0C,EAQtD,SAAgBE,EAAMpG,GACpB,IAAMqG,EAAIrG,EAAOC,SAAW,EACtBqG,EAAoB,EAAhBtG,EAAOC,OACXsG,EAAoB,EAAhBvG,EAAOC,OACXuG,EAAIxG,EAAOC,SAAW,EAE5B,OACEgG,EAAQI,EAAEI,SAAS,IAAK,GACxB,IACAR,GAAa,MAAJK,GAAYG,SAAS,IAAK,GACnC,IACAR,GAAWK,GAAK,EAAK,KAAU,OAAQG,SAAS,IAAK,GACrD,IACAR,GAAc,MAAJM,EAAc,OAAQE,SAAS,IAAK,GAC9C,IACAR,GAAUM,GAAK,EAAK,OAAQE,SAAS,IAAK,GAC1CR,EAAQO,EAAEC,SAAS,IAAK,GCtB5B,IAAaC,EAAqB,CAChCzG,KAAI,WACF,OAAQkB,KAAKwF,SAAWjH,EAAe,ICqB3CkH,EAAA,WAOE,SAAAA,EAAY5G,QAAA,IAAAA,IAAAA,EAAA0G,GACVG,KAAK7G,OAASA,EA0LlB,OApLS4G,EAAA5B,UAAAjF,MAAP,WACE,OAAOA,EAAM8G,KAAK7G,SAMb4G,EAAA5B,UAAAvE,OAAP,WACE,OAAOA,EAAOoG,KAAK7G,SAMd4G,EAAA5B,UAAAtE,OAAP,WACE,OAAOA,EAAOmG,KAAK7G,SAMd4G,EAAA5B,UAAArE,WAAP,WACE,OAAOA,EAAWkG,KAAK7G,SAMlB4G,EAAA5B,UAAA3E,MAAP,WACE,OAAOA,EAAMwG,KAAK7G,SAMb4G,EAAA5B,UAAAxE,UAAP,WACE,OAAOA,EAAUqG,KAAK7G,SAQjB4G,EAAA5B,UAAAnD,QAAP,SAAeF,EAAaC,GAC1B,OAAOC,EAAQF,EAAKC,EAAbC,CAAkBgF,KAAK7G,SAMzB4G,EAAA5B,UAAAN,uBAAP,WACE,OAAOA,EAAuBmC,KAAK7G,SAM9B4G,EAAA5B,UAAAP,uBAAP,WACE,OAAOA,EAAuBoC,KAAK7G,SAS9B4G,EAAA5B,UAAAL,KAAP,SAAYhD,EAAaC,EAAagD,GACpC,YADoC,IAAAA,IAAAA,GAAA,GAC7BD,EAAKhD,EAAKC,EAAKgD,EAAfD,CAA0BkC,KAAK7G,SAkBjC4G,EAAA5B,UAAA9C,KAAP,SAAYC,EAAoBC,GAC9B,OAAOF,EAAKC,EAAYC,EAAjBF,CAA+B2E,KAAK7G,SAUtC4G,EAAA5B,UAAAX,KAAP,SAAeC,EAAsBC,EAAgB5B,GACnD,OAAO0B,EAAKwC,KAAK7G,OAAQsE,EAAQC,EAAO5B,IAOnCiE,EAAA5B,UAAAE,QAAP,SAAkBC,GAChB,OAAOD,EAAQ2B,KAAK7G,OAAQmF,IASvByB,EAAA5B,UAAAM,OAAP,SAAiBC,EAA0BC,GACzC,OAAOF,EAAOuB,KAAK7G,OAAQuF,EAAYC,IAOlCoB,EAAA5B,UAAAnC,IAAP,SAAWC,GACT,OAAOD,EAAIC,EAAJD,CAAegE,KAAK7G,SAQtB4G,EAAA5B,UAAAjC,KAAP,SAAYD,EAAmBE,GAC7B,OAAOD,EAAKD,EAAWE,EAAhBD,CAA0B8D,KAAK7G,SAQjC4G,EAAA5B,UAAAoB,MAAP,WACE,OAAOA,EAAMS,KAAK7G,SAeb4G,EAAA5B,UAAA3B,OAAP,SAAcG,EAAgBF,GAC5B,OAAOD,EAAOC,EAAPD,CAAcwD,KAAK7G,OAAQwD,IAS7BoD,EAAA5B,UAAAjB,IAAP,SAAWP,EAAgBQ,GACzB,OAAOD,EAAIC,EAAJD,CAAe8C,KAAK7G,OAAQwD,IAQ9BoD,EAAA5B,UAAAvC,KAAP,SAAYC,EAAaC,GACvB,OAAOF,EAAKC,EAAOC,EAAZF,CAAiBoE,KAAK7G,SAEjC4G,EAlMA,GCxBME,EAA8B,WAClC,IACE,IAAMC,EAAS,IAAIC,YAAY,GACzBC,EAAO,IAAIC,WAAWH,GAE5B,GADAE,EAAK,GAAKtH,EACNsH,EAAK,MAAQtH,EACf,OAAOuH,WAET,MAAOlB,IAGT,OAAQjB,MAX0B,GCFhCoC,EAA0B,KAE1BC,EADU,IAYDC,EAAwB,CACnCpH,KAAI,WAQF,OAPImH,GAdM,MAeK,OAATD,IACFA,EAAO,IAAID,EAhBL,MAkBRI,OAAOC,gBAAgBJ,GACvBC,EAAQ,GAEc,EAAjBD,EAAMC,OCfjB,SAAgBI,EACdxH,EACAwD,QADA,IAAAxD,IAAAA,EAAA0G,QACA,IAAAlD,IAAAA,EAAA,IAEA,IAAM2B,EAAkB,GACxBA,EAAMhC,KAA4B,GAAvB,IAAIP,MAAO6E,WACtB,IAAK,IAAIvE,EAAI,EAAGA,EAAIM,IAAUN,EAC5BiC,EAAMjC,GAAqB,EAAhBlD,EAAOC,OAEpB,OAAOkF,ECdF,IAAMuC,EAAyC,WACpD,IACE,IAA2C,IAAtCvG,KAAauG,KAAKjI,EAAY,GACjC,OAAQ0B,KAAauG,KAEvB,MAAO1B,IAIT,OAAO,SAACK,EAAWC,GACjB,IACMqB,EAHW,MAGNtB,EAELuB,EALW,MAKNtB,EAGX,OAAQqB,EAAKC,IANDvB,IAAM,GAFD,OAQSuB,EAAKD,GAJnBrB,IAAM,GAJD,QAQ0B,KAAQ,GAAM,GAhBP,GCChDuB,EAAa,IACbC,EAAYD,EAAa,EACzBE,GAAI,IACJC,GAAqBH,EAAaE,GAClCE,GAAI,WAQVC,GAAA,WAiCE,SAAAA,IARiBrB,KAAAM,KAAO,IAAID,EAAWW,GAC/BhB,KAAAO,MAAQ,EACRP,KAAAsB,KAAO,EAyEjB,OA/FgBD,EAAAE,KAAd,SAAmBC,GACjB,OAAO,IAAIH,GAAuBE,KAAKC,IAO3BH,EAAAI,cAAd,SAA4BhE,GAC1B,OAAO,IAAI4D,GAAuBI,cAAchE,IAOpC4D,EAAAK,SAAd,WACE,OAAOL,EAAqBI,cAAcd,MAgBrCU,EAAAlD,UAAA/E,KAAP,YACoB,EAAb4G,KAAKO,QAAcS,IACtBW,GAAY3B,KAAKM,MACjBN,KAAKO,MAAQ,GAGf,IAAMvG,EAAQgG,KAAKM,KAAKN,KAAKO,OAG7B,OAFAP,KAAKO,MAASP,KAAKO,MAAQ,EAAK,EAChCP,KAAKsB,MAAQ,EACU,EAyE3B,SAAgBtH,GAId,OAHAA,GAASA,IAAU,GACnBA,GAAUA,GAAS,EAAK,YACxBA,GAAUA,GAAS,GAAM,YACTA,IAAU,GA7EjB4H,CAAO5H,IASTqH,EAAAlD,UAAA0D,YAAP,WACE,OAAO7B,KAAKsB,MAOPD,EAAAlD,UAAA2D,QAAP,SAAe5C,GACb,GAAIA,GAAS,EACX,OAAOc,KAOT,IALAA,KAAKsB,MAAQpC,GACK,EAAbc,KAAKO,QAAcS,IACtBW,GAAY3B,KAAKM,MACjBN,KAAKO,MAAQ,GAERrB,EAAQc,KAAKO,MAAQS,GAC1B9B,GAAS8B,EAAahB,KAAKO,MAC3BoB,GAAY3B,KAAKM,MACjBN,KAAKO,MAAQ,EAGf,OADAP,KAAKO,MAASP,KAAKO,MAAQrB,EAAS,EAC7Bc,MAGDqB,EAAAlD,UAAAoD,KAAR,SAAaC,GACX,IAAIO,EAAW,EACf/B,KAAKM,KAAK,GAAKyB,EAAqB,EAAVP,EAE1B,IAAK,IAAInF,EAAI,EAAGA,EAAI2E,EAAY3E,EAAKA,EAAI,EAAK,EAC5C2D,KAAKM,KAAKjE,GAAK0F,EACZlB,EAAKkB,EAAYA,IAAa,GAAK,YAAc1F,EAAK,EAI3D,OAFA2D,KAAKO,MAAQS,EACbhB,KAAKsB,KAAO,EACLtB,MAGDqB,EAAAlD,UAAAsD,cAAR,SAAsBhE,GAGpB,OAFAuC,KAAKuB,KAAK,UA+Bd,SAAuBjB,EAAkB7C,GAMvC,IALA,IAAIpB,EAAI,EACJQ,EAAI,EACFmF,EAAevE,EAAOd,OACxBsF,EAAyC,EAArC3H,KAAKS,IAAIiH,EAAchB,GAC3Be,EAAqB,EAAVzB,EAAK,IACR,EAAJ2B,GAAS,IAAKA,EACpB3B,EAAKjE,GAAK0F,GACNzB,EAAKjE,GAAKwE,EAAKkB,EAAYA,IAAa,GAAK,WAChC,EAAZtE,EAAOZ,KACH,EAAJA,GACH,IAEAA,GACO,GAFTR,EAAKA,EAAI,EAAK,IAEA4E,IACZX,EAAK,GAAKA,EAAKW,GACf5E,EAAI,GAEFQ,GAAKmF,IACPnF,EAAI,GAGR,IAAKoF,EAAIhB,GAAgB,EAAJgB,GAAS,IAAKA,EACjC3B,EAAKjE,GAAK0F,GACNzB,EAAKjE,GAAKwE,EAAKkB,EAAYA,IAAa,GAAK,aAAe1F,EAAK,GAE5D,GADTA,EAAKA,EAAI,EAAK,IACA4E,IACZX,EAAK,GAAKA,EAAKW,GACf5E,EAAI,GAGRiE,EAAK,GAAKxH,EA7DR2I,CAAczB,KAAKM,KAAM7C,GAClBuC,MAEXqB,EApGA,GAsGA,SAASM,GAAYrB,GAGnB,IAFA,IAAI2B,EAAI,EACJzD,EAAM,GACE,EAAJyD,GAASd,GAAoBc,EAAKA,EAAI,EAAK,EACjDzD,EAAO8B,EAAK2B,GAAKnJ,EAAewH,EAAM2B,EAAI,EAAK,GAAKlJ,EACpDuH,EAAK2B,GAAK3B,EAAM2B,EAAIf,GAAK,GAAM1C,IAAQ,GAAY,EAANA,EAAY4C,GAAI,GAG/D,MAAY,EAAJa,GAAShB,EAAWgB,EAAKA,EAAI,EAAK,EACxCzD,EAAO8B,EAAK2B,GAAKnJ,EAAewH,EAAM2B,EAAI,EAAK,GAAKlJ,EACpDuH,EAAK2B,GACH3B,EAAM2B,EAAId,GAAsB,GAAM3C,IAAQ,GAAY,EAANA,EAAY4C,GAAI,GAGxE5C,EAAO8B,EAAKW,GAAanI,EAAewH,EAAK,GAAKvH,EAClDuH,EAAKW,GAAaX,EAAKY,GAAI,GAAM1C,IAAQ,GAAY,EAANA,EAAY4C,GAAI,GCrIjE,IAAId,GAA0B,KAE1BC,GADU,IAYD2B,GAAqB,CAChC9I,KAAI,WAOF,OANImH,IAdM,MAeRD,GAAO,IAAID,WACT,IAAI8B,UAAUC,QAAQ,UAAUC,YAAY,MAAYnC,QAE1DK,GAAQ,GAEc,EAAjBD,GAAMC,yMCZjB,SACE9C,EACAC,EACA5B,GAEA,IAAM8C,EAAQX,EAAWY,KAAKpB,EAAQC,EAAO5B,GAC7C,GAAqB,IAAjB8C,EAAMjC,OACR,MAAM,IAAIzB,WAAW,2CAEvB,IAAM5B,EAAe0B,EAAQ,EAAG4D,EAAMjC,OAAS,GAC/C,OAAO,SAAAxD,GAAU,OAAAyF,EAAMtF,EAAaH", "sourcesContent": ["export const SMALLEST_UNSAFE_INTEGER = 0x20000000000000;\nexport const LARGEST_SAFE_INTEGER = SMALLEST_UNSAFE_INTEGER - 1;\nexport const UINT32_MAX = -1 >>> 0;\nexport const UINT32_SIZE = UINT32_MAX + 1;\nexport const INT32_SIZE = UINT32_SIZE / 2;\nexport const INT32_MAX = INT32_SIZE - 1;\nexport const UINT21_SIZE = 1 << 21;\nexport const UINT21_MAX = UINT21_SIZE - 1;\n", "import { Engine } from \"../types\";\n\n/**\n * Returns a value within [-0x80000000, 0x7fffffff]\n */\nexport function int32(engine: Engine): number {\n  return engine.next() | 0;\n}\n", "import { Distribution } from \"../types\";\n\nexport function add(distribution: Distribution, addend: number): Distribution {\n  if (addend === 0) {\n    return distribution;\n  } else {\n    return engine => distribution(engine) + addend;\n  }\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [-0x20000000000000, 0x1fffffffffffff]\n */\nexport function int53(engine: Engine): number {\n  const high = engine.next() | 0;\n  const low = engine.next() >>> 0;\n  return (\n    (high & UINT21_MAX) * UINT32_SIZE +\n    low +\n    (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0)\n  );\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [-0x20000000000000, 0x20000000000000]\n */\nexport function int53Full(engine: Engine): number {\n  while (true) {\n    const high = engine.next() | 0;\n    if (high & 0x400000) {\n      if ((high & 0x7fffff) === 0x400000 && (engine.next() | 0) === 0) {\n        return SMALLEST_UNSAFE_INTEGER;\n      }\n    } else {\n      const low = engine.next() >>> 0;\n      return (\n        (high & UINT21_MAX) * UINT32_SIZE +\n        low +\n        (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0)\n      );\n    }\n  }\n}\n", "import { Engine } from \"../types\";\n\n/**\n * Returns a value within [0, 0xffffffff]\n */\nexport function uint32(engine: Engine): number {\n  return engine.next() >>> 0;\n}\n", "import { Engine } from \"../types\";\nimport { UINT21_MAX, UINT32_SIZE } from \"../utils/constants\";\n\n/**\n * Returns a value within [0, 0x1fffffffffffff]\n */\nexport function uint53(engine: Engine): number {\n  const high = engine.next() & UINT21_MAX;\n  const low = engine.next() >>> 0;\n  return high * UINT32_SIZE + low;\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [0, 0x20000000000000]\n */\nexport function uint53Full(engine: Engine): number {\n  while (true) {\n    const high = engine.next() | 0;\n    if (high & UINT21_SIZE) {\n      if ((high & UINT21_MAX) === 0 && (engine.next() | 0) === 0) {\n        return SMALLEST_UNSAFE_INTEGER;\n      }\n    } else {\n      const low = engine.next() >>> 0;\n      return (high & UINT21_MAX) * UINT32_SIZE + low;\n    }\n  }\n}\n", "import { Distribution, Engine } from \"../types\";\nimport { add } from \"../utils/add\";\nimport {\n  INT32_SIZE,\n  LARGEST_SAFE_INTEGER,\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_MAX,\n  UINT32_SIZE\n} from \"../utils/constants\";\nimport { int32 } from \"./int32\";\nimport { int53 } from \"./int53\";\nimport { int53Full } from \"./int53Full\";\nimport { uint32 } from \"./uint32\";\nimport { uint53 } from \"./uint53\";\nimport { uint53Full } from \"./uint53Full\";\n\nfunction isPowerOfTwoMinusOne(value: number): boolean {\n  return ((value + 1) & value) === 0;\n}\n\nfunction bitmask(masking: number): Distribution {\n  return (engine: Engine) => engine.next() & masking;\n}\n\nfunction downscaleToLoopCheckedRange(range: number): Distribution {\n  const extendedRange = range + 1;\n  const maximum = extendedRange * Math.floor(UINT32_SIZE / extendedRange);\n  return engine => {\n    let value = 0;\n    do {\n      value = engine.next() >>> 0;\n    } while (value >= maximum);\n    return value % extendedRange;\n  };\n}\n\nfunction downscaleToRange(range: number): Distribution {\n  if (isPowerOfTwoMinusOne(range)) {\n    return bitmask(range);\n  } else {\n    return downscaleToLoopCheckedRange(range);\n  }\n}\n\nfunction isEvenlyDivisibleByMaxInt32(value: number): boolean {\n  return (value | 0) === 0;\n}\n\nfunction upscaleWithHighMasking(masking: number): Distribution {\n  return engine => {\n    const high = engine.next() & masking;\n    const low = engine.next() >>> 0;\n    return high * UINT32_SIZE + low;\n  };\n}\n\nfunction upscaleToLoopCheckedRange(extendedRange: number): Distribution {\n  const maximum =\n    extendedRange * Math.floor(SMALLEST_UNSAFE_INTEGER / extendedRange);\n  return engine => {\n    let ret = 0;\n    do {\n      const high = engine.next() & UINT21_MAX;\n      const low = engine.next() >>> 0;\n      ret = high * UINT32_SIZE + low;\n    } while (ret >= maximum);\n    return ret % extendedRange;\n  };\n}\n\nfunction upscaleWithinU53(range: number): Distribution {\n  const extendedRange = range + 1;\n  if (isEvenlyDivisibleByMaxInt32(extendedRange)) {\n    const highRange = ((extendedRange / UINT32_SIZE) | 0) - 1;\n    if (isPowerOfTwoMinusOne(highRange)) {\n      return upscaleWithHighMasking(highRange);\n    }\n  }\n  return upscaleToLoopCheckedRange(extendedRange);\n}\n\nfunction upscaleWithinI53AndLoopCheck(min: number, max: number): Distribution {\n  return engine => {\n    let ret = 0;\n    do {\n      const high = engine.next() | 0;\n      const low = engine.next() >>> 0;\n      ret =\n        (high & UINT21_MAX) * UINT32_SIZE +\n        low +\n        (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0);\n    } while (ret < min || ret > max);\n    return ret;\n  };\n}\n\n/**\n * Returns a Distribution to return a value within [min, max]\n * @param min The minimum integer value, inclusive. No less than -0x20000000000000.\n * @param max The maximum integer value, inclusive. No greater than 0x20000000000000.\n */\nexport function integer(min: number, max: number): Distribution {\n  min = Math.floor(min);\n  max = Math.floor(max);\n  if (min < -SMALLEST_UNSAFE_INTEGER || !isFinite(min)) {\n    throw new RangeError(\n      `Expected min to be at least ${-SMALLEST_UNSAFE_INTEGER}`\n    );\n  } else if (max > SMALLEST_UNSAFE_INTEGER || !isFinite(max)) {\n    throw new RangeError(\n      `Expected max to be at most ${SMALLEST_UNSAFE_INTEGER}`\n    );\n  }\n\n  const range = max - min;\n  if (range <= 0 || !isFinite(range)) {\n    return () => min;\n  } else if (range === UINT32_MAX) {\n    if (min === 0) {\n      return uint32;\n    } else {\n      return add(int32, min + INT32_SIZE);\n    }\n  } else if (range < UINT32_MAX) {\n    return add(downscaleToRange(range), min);\n  } else if (range === LARGEST_SAFE_INTEGER) {\n    return add(uint53, min);\n  } else if (range < LARGEST_SAFE_INTEGER) {\n    return add(upscaleWithinU53(range), min);\n  } else if (max - 1 - min === LARGEST_SAFE_INTEGER) {\n    return add(uint53Full, min);\n  } else if (\n    min === -SMALLEST_UNSAFE_INTEGER &&\n    max === SMALLEST_UNSAFE_INTEGER\n  ) {\n    return int53Full;\n  } else if (min === -SMALLEST_UNSAFE_INTEGER && max === LARGEST_SAFE_INTEGER) {\n    return int53;\n  } else if (min === -LARGEST_SAFE_INTEGER && max === SMALLEST_UNSAFE_INTEGER) {\n    return add(int53, 1);\n  } else if (max === SMALLEST_UNSAFE_INTEGER) {\n    return add(upscaleWithinI53AndLoopCheck(min - 1, max - 1), 1);\n  } else {\n    return upscaleWithinI53AndLoopCheck(min, max);\n  }\n}\n", "import { Distribution, Engine } from \"../types\";\nimport { INT32_SIZE, SMALLEST_UNSAFE_INTEGER, UINT32_SIZE } from \"../utils/constants\";\nimport { int32 } from \"./int32\";\nimport { integer } from \"./integer\";\nimport { uint53 } from \"./uint53\";\n\nfunction isLeastBitTrue(engine: Engine) {\n  return (engine.next() & 1) === 1;\n}\n\nfunction lessThan(\n  distribution: Distribution,\n  value: number\n): Distribution<boolean> {\n  return engine => distribution(engine) < value;\n}\n\nfunction probability(percentage: number) {\n  if (percentage <= 0) {\n    return () => false;\n  } else if (percentage >= 1) {\n    return () => true;\n  } else {\n    const scaled = percentage * UINT32_SIZE;\n    if (scaled % 1 === 0) {\n      return lessThan(int32, (scaled - INT32_SIZE) | 0);\n    } else {\n      return lessThan(uint53, Math.round(percentage * SMALLEST_UNSAFE_INTEGER));\n    }\n  }\n}\n\n// tslint:disable:unified-signatures\n\n/**\n * Returns a boolean Distribution with 50% probability of being true or false\n */\nexport function bool(): Distribution<boolean>;\n/**\n * Returns a boolean Distribution with the provided `percentage` of being true\n * @param percentage A number within [0, 1] of how often the result should be `true`\n */\nexport function bool(percentage: number): Distribution<boolean>;\n/**\n * Returns a boolean Distribution with a probability of\n * `numerator` divided by `denominator` of being true\n * @param numerator The numerator of the probability\n * @param denominator The denominator of the probability\n */\nexport function bool(\n  numerator: number,\n  denominator: number\n): Distribution<boolean>;\nexport function bool(\n  numerator?: number,\n  denominator?: number\n): Distribution<boolean> {\n  if (denominator == null) {\n    if (numerator == null) {\n      return isLeastBitTrue;\n    }\n    return probability(numerator);\n  } else {\n    if (numerator! <= 0) {\n      return () => false;\n    } else if (numerator! >= denominator) {\n      return () => true;\n    }\n    return lessThan(integer(0, denominator - 1), numerator!);\n  }\n}\n", "import { Distribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution that returns a random `Date` within the inclusive\n * range of [`start`, `end`].\n * @param start The minimum `Date`\n * @param end The maximum `Date`\n */\nexport function date(start: Date, end: Date): Distribution<Date> {\n  const distribution = integer(+start, +end);\n  return engine => new Date(distribution(engine));\n}\n", "import { Distribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution to return a value within [1, sideCount]\n * @param sideCount The number of sides of the die\n */\nexport function die(sideCount: number): Distribution<number> {\n  return integer(1, sideCount);\n}\n", "import { Distribution } from \"../types\";\nimport { die } from \"./die\";\n\n/**\n * Returns a distribution that returns an array of length `dieCount` of values\n * within [1, `sideCount`]\n * @param sideCount The number of sides of each die\n * @param dieCount The number of dice\n */\nexport function dice(\n  sideCount: number,\n  dieCount: number\n): Distribution<number[]> {\n  const distribution = die(sideCount);\n  return engine => {\n    const result = [];\n    for (let i = 0; i < dieCount; ++i) {\n      result.push(distribution(engine));\n    }\n    return result;\n  };\n}\n", "import { StringDistribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n// tslint:disable:unified-signatures\n\n// has 2**x chars, for faster uniform distribution\nconst DEFAULT_STRING_POOL =\n  \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-\";\n\n/**\n * Returns a distribution that returns a random string using numbers,\n * uppercase and lowercase letters, `_`, and `-` of length `length`.\n * @param length Length of the result string\n */\nexport function string(): StringDistribution;\n/**\n * Returns a distribution that returns a random string using the provided\n * string pool as the possible characters to choose from of length `length`.\n * @param length Length of the result string\n */\nexport function string(pool: string): StringDistribution;\nexport function string(pool: string = DEFAULT_STRING_POOL): StringDistribution {\n  const poolLength = pool.length;\n  if (!poolLength) {\n    throw new Error(\"Expected pool not to be an empty string\");\n  }\n\n  const distribution = integer(0, poolLength - 1);\n  return (engine, length) => {\n    let result = \"\";\n    for (let i = 0; i < length; ++i) {\n      const j = distribution(engine);\n      result += pool.charAt(j);\n    }\n    return result;\n  };\n}\n", "import { StringDistribution } from \"../types\";\nimport { string } from \"./string\";\n\nconst LOWER_HEX_POOL = \"0123456789abcdef\";\nconst lowerHex = string(LOWER_HEX_POOL);\nconst upperHex = string(LOWER_HEX_POOL.toUpperCase());\n\n/**\n * Returns a Distribution that returns a random string comprised of numbers\n * or the characters `abcdef` (or `ABCDEF`) of length `length`.\n * @param length Length of the result string\n * @param uppercase Whether the string should use `ABCDEF` instead of `abcdef`\n */\nexport function hex(uppercase?: boolean): StringDistribution {\n  if (uppercase) {\n    return upperHex;\n  } else {\n    return lowerHex;\n  }\n}\n", "export function convertSliceArgument(value: number, length: number): number {\n  if (value < 0) {\n    return Math.max(value + length, 0);\n  } else {\n    return Math.min(value, length);\n  }\n}\n", "export function toInteger(value: number) {\n  const num = +value;\n  if (num < 0) {\n    return Math.ceil(num);\n  } else {\n    return Math.floor(num);\n  }\n}\n", "import { Engine } from \"../types\";\nimport { convertSliceArgument } from \"../utils/convertSliceArgument\";\nimport { toInteger } from \"../utils/toInteger\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a random value within the provided `source` within the sliced\n * bounds of `begin` and `end`.\n * @param source an array of items to pick from\n * @param begin the beginning slice index (defaults to `0`)\n * @param end the ending slice index (defaults to `source.length`)\n */\nexport function pick<T>(\n  engine: Engine,\n  source: ArrayLike<T>,\n  begin?: number,\n  end?: number\n): T {\n  const length = source.length;\n  if (length === 0) {\n    throw new RangeError(\"Cannot pick from an empty array\");\n  }\n  const start =\n    begin == null ? 0 : convertSliceArgument(toInteger(begin), length);\n  const finish =\n    end === void 0 ? length : convertSliceArgument(toInteger(end), length);\n  if (start >= finish) {\n    throw new RangeError(`Cannot pick between bounds ${start} and ${finish}`);\n  }\n  const distribution = integer(start, finish - 1);\n  return source[distribution(engine)];\n}\n", "import { Engine } from \"../types\";\nimport { SMALLEST_UNSAFE_INTEGER } from \"../utils/constants\";\nimport { uint53 } from \"./uint53\";\n\n/**\n * Returns a floating-point value within [0.0, 1.0)\n */\nexport function realZeroToOneExclusive(engine: Engine): number {\n  return uint53(engine) / SMALLEST_UNSAFE_INTEGER;\n}\n", "import { Engine } from \"../types\";\nimport { SMALLEST_UNSAFE_INTEGER } from \"../utils/constants\";\nimport { uint53Full } from \"./uint53Full\";\n\n/**\n * Returns a floating-point value within [0.0, 1.0]\n */\nexport function realZeroToOneInclusive(engine: Engine): number {\n  return uint53Full(engine) / SMALLEST_UNSAFE_INTEGER;\n}\n", "import { Distribution } from \"../types\";\nimport { add } from \"../utils/add\";\nimport { multiply } from \"../utils/multiply\";\nimport { realZeroToOneExclusive } from \"./realZeroToOneExclusive\";\nimport { realZeroToOneInclusive } from \"./realZeroToOneInclusive\";\n\n/**\n * Returns a floating-point value within [min, max) or [min, max]\n * @param min The minimum floating-point value, inclusive.\n * @param max The maximum floating-point value.\n * @param inclusive If true, `max` will be inclusive.\n */\nexport function real(\n  min: number,\n  max: number,\n  inclusive: boolean = false\n): Distribution {\n  if (!isFinite(min)) {\n    throw new RangeError(\"Expected min to be a finite number\");\n  } else if (!isFinite(max)) {\n    throw new RangeError(\"Expected max to be a finite number\");\n  }\n  return add(\n    multiply(\n      inclusive ? realZeroToOneInclusive : realZeroToOneExclusive,\n      max - min\n    ),\n    min\n  );\n}\n", "import { Distribution } from \"../types\";\n\nexport function multiply(\n  distribution: Distribution,\n  multiplier: number\n): Distribution {\n  if (multiplier === 1) {\n    return distribution;\n  } else if (multiplier === 0) {\n    return () => 0;\n  } else {\n    return engine => distribution(engine) * multiplier;\n  }\n}\n", "export const sliceArray = Array.prototype.slice;\n", "import { Engine } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Shuffles an array in-place\n * @param engine The Engine to use when choosing random values\n * @param array The array to shuffle\n * @param downTo minimum index to shuffle. Only used internally.\n */\nexport function shuffle<T>(\n  engine: Engine,\n  array: T[],\n  downTo: number = 0\n): T[] {\n  const length = array.length;\n  if (length) {\n    for (let i = (length - 1) >>> 0; i > downTo; --i) {\n      const distribution = integer(0, i);\n      const j = distribution(engine);\n      if (i !== j) {\n        const tmp = array[i];\n        array[i] = array[j];\n        array[j] = tmp;\n      }\n    }\n  }\n  return array;\n}\n", "import { Engine } from \"../types\";\nimport { sliceArray } from \"../utils/sliceArray\";\nimport { shuffle } from \"./shuffle\";\n\n/**\n * From the population array, produce an array with sampleSize elements that\n * are randomly chosen without repeats.\n * @param engine The Engine to use when choosing random values\n * @param population An array that has items to choose a sample from\n * @param sampleSize The size of the result array\n */\nexport function sample<T>(\n  engine: Engine,\n  population: ArrayLike<T>,\n  sampleSize: number\n): T[] {\n  if (\n    sampleSize < 0 ||\n    sampleSize > population.length ||\n    !isFinite(sampleSize)\n  ) {\n    throw new RangeError(\n      \"Expected sampleSize to be within 0 and the length of the population\"\n    );\n  }\n\n  if (sampleSize === 0) {\n    return [];\n  }\n\n  const clone = sliceArray.call(population);\n  const length = clone.length;\n  if (length === sampleSize) {\n    return shuffle(engine, clone, 0);\n  }\n  const tailLength = length - sampleSize;\n  return shuffle(engine, clone, tailLength - 1).slice(tailLength);\n}\n", "export const stringRepeat = (() => {\n  try {\n    if ((\"x\" as any).repeat(3) === \"xxx\") {\n      return (pattern: string, count: number): string =>\n        (pattern as any).repeat(count);\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  return (pattern: string, count: number): string => {\n    let result = \"\";\n    while (count > 0) {\n      if (count & 1) {\n        result += pattern;\n      }\n      count >>= 1;\n      pattern += pattern;\n    }\n    return result;\n  };\n})();\n", "import { Engine } from \"../types\";\nimport { stringRepeat } from \"../utils/stringRepeat\";\n\nfunction zeroPad(text: string, zeroCount: number) {\n  return stringRepeat(\"0\", zeroCount - text.length) + text;\n}\n\n/**\n * Returns a Universally Unique Identifier Version 4.\n *\n * See http://en.wikipedia.org/wiki/Universally_unique_identifier\n */\nexport function uuid4(engine: Engine) {\n  const a = engine.next() >>> 0;\n  const b = engine.next() | 0;\n  const c = engine.next() | 0;\n  const d = engine.next() >>> 0;\n\n  return (\n    zeroPad(a.toString(16), 8) +\n    \"-\" +\n    zeroPad((b & 0xffff).toString(16), 4) +\n    \"-\" +\n    zeroPad((((b >> 4) & 0x0fff) | 0x4000).toString(16), 4) +\n    \"-\" +\n    zeroPad(((c & 0x3fff) | 0x8000).toString(16), 4) +\n    \"-\" +\n    zeroPad(((c >> 4) & 0xffff).toString(16), 4) +\n    zeroPad(d.toString(16), 8)\n  );\n}\n", "import { Engine } from \"../types\";\nimport { UINT32_SIZE } from \"../utils/constants\";\n\n/**\n * An int32-producing Engine that uses `Math.random()`\n */\nexport const nativeMath: Engine = {\n  next() {\n    return (Math.random() * UINT32_SIZE) | 0;\n  }\n};\n", "import { bool } from \"./distribution/bool\";\nimport { date } from \"./distribution/date\";\nimport { dice } from \"./distribution/dice\";\nimport { die } from \"./distribution/die\";\nimport { hex } from \"./distribution/hex\";\nimport { int32 } from \"./distribution/int32\";\nimport { int53 } from \"./distribution/int53\";\nimport { int53Full } from \"./distribution/int53Full\";\nimport { integer } from \"./distribution/integer\";\nimport { pick } from \"./distribution/pick\";\nimport { real } from \"./distribution/real\";\nimport { realZeroToOneExclusive } from \"./distribution/realZeroToOneExclusive\";\nimport { realZeroToOneInclusive } from \"./distribution/realZeroToOneInclusive\";\nimport { sample } from \"./distribution/sample\";\nimport { shuffle } from \"./distribution/shuffle\";\nimport { string } from \"./distribution/string\";\nimport { uint32 } from \"./distribution/uint32\";\nimport { uint53 } from \"./distribution/uint53\";\nimport { uint53Full } from \"./distribution/uint53Full\";\nimport { uuid4 } from \"./distribution/uuid4\";\nimport { nativeMath } from \"./engine/nativeMath\";\nimport { Engine } from \"./types\";\n\n// tslint:disable:unified-signatures\n\n/**\n * A wrapper around an Engine that provides easy-to-use methods for\n * producing values based on known distributions\n */\nexport class Random {\n  private readonly engine: Engine;\n\n  /**\n   * Creates a new Random wrapper\n   * @param engine The engine to use (defaults to a `Math.random`-based implementation)\n   */\n  constructor(engine: Engine = nativeMath) {\n    this.engine = engine;\n  }\n\n  /**\n   * Returns a value within [-0x80000000, 0x7fffffff]\n   */\n  public int32(): number {\n    return int32(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0xffffffff]\n   */\n  public uint32(): number {\n    return uint32(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0x1fffffffffffff]\n   */\n  public uint53(): number {\n    return uint53(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0x20000000000000]\n   */\n  public uint53Full(): number {\n    return uint53Full(this.engine);\n  }\n\n  /**\n   * Returns a value within [-0x20000000000000, 0x1fffffffffffff]\n   */\n  public int53(): number {\n    return int53(this.engine);\n  }\n\n  /**\n   * Returns a value within [-0x20000000000000, 0x20000000000000]\n   */\n  public int53Full(): number {\n    return int53Full(this.engine);\n  }\n\n  /**\n   * Returns a value within [min, max]\n   * @param min The minimum integer value, inclusive. No less than -0x20000000000000.\n   * @param max The maximum integer value, inclusive. No greater than 0x20000000000000.\n   */\n  public integer(min: number, max: number): number {\n    return integer(min, max)(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [0.0, 1.0]\n   */\n  public realZeroToOneInclusive(): number {\n    return realZeroToOneInclusive(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [0.0, 1.0)\n   */\n  public realZeroToOneExclusive(): number {\n    return realZeroToOneExclusive(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [min, max) or [min, max]\n   * @param min The minimum floating-point value, inclusive.\n   * @param max The maximum floating-point value.\n   * @param inclusive If true, `max` will be inclusive.\n   */\n  public real(min: number, max: number, inclusive: boolean = false): number {\n    return real(min, max, inclusive)(this.engine);\n  }\n\n  /**\n   * Returns a boolean with 50% probability of being true or false\n   */\n  public bool(): boolean;\n  /**\n   * Returns a boolean with the provided `percentage` of being true\n   * @param percentage A number within [0, 1] of how often the result should be `true`\n   */\n  public bool(percentage: number): boolean;\n  /**\n   * Returns a boolean with a probability of `numerator`/`denominator` of being true\n   * @param numerator The numerator of the probability\n   * @param denominator The denominator of the probability\n   */\n  public bool(numerator: number, denominator: number): boolean;\n  public bool(numerator?: number, denominator?: number): boolean {\n    return bool(numerator!, denominator!)(this.engine);\n  }\n\n  /**\n   * Return a random value within the provided `source` within the sliced\n   * bounds of `begin` and `end`.\n   * @param source an array of items to pick from\n   * @param begin the beginning slice index (defaults to `0`)\n   * @param end the ending slice index (defaults to `source.length`)\n   */\n  public pick<T>(source: ArrayLike<T>, begin?: number, end?: number): T {\n    return pick(this.engine, source, begin, end);\n  }\n\n  /**\n   * Shuffles an array in-place\n   * @param array The array to shuffle\n   */\n  public shuffle<T>(array: T[]): T[] {\n    return shuffle(this.engine, array);\n  }\n\n  /**\n   * From the population array, returns an array with sampleSize elements that\n   * are randomly chosen without repeats.\n   * @param population An array that has items to choose a sample from\n   * @param sampleSize The size of the result array\n   */\n  public sample<T>(population: ArrayLike<T>, sampleSize: number): T[] {\n    return sample(this.engine, population, sampleSize);\n  }\n\n  /**\n   * Returns a value within [1, sideCount]\n   * @param sideCount The number of sides of the die\n   */\n  public die(sideCount: number): number {\n    return die(sideCount)(this.engine);\n  }\n\n  /**\n   * Returns an array of length `dieCount` of values within [1, sideCount]\n   * @param sideCount The number of sides of each die\n   * @param dieCount The number of dice\n   */\n  public dice(sideCount: number, dieCount: number): number[] {\n    return dice(sideCount, dieCount)(this.engine);\n  }\n\n  /**\n   * Returns a Universally Unique Identifier Version 4.\n   *\n   * See http://en.wikipedia.org/wiki/Universally_unique_identifier\n   */\n  public uuid4(): string {\n    return uuid4(this.engine);\n  }\n\n  /**\n   * Returns a random string using numbers, uppercase and lowercase letters,\n   * `_`, and `-` of length `length`.\n   * @param length Length of the result string\n   */\n  public string(length: number): string;\n  /**\n   * Returns a random string using the provided string pool as the possible\n   * characters to choose from of length `length`.\n   * @param length Length of the result string\n   */\n  public string(length: number, pool: string): string;\n  public string(length: number, pool?: string): string {\n    return string(pool!)(this.engine, length);\n  }\n\n  /**\n   * Returns a random string comprised of numbers or the characters `abcdef`\n   * (or `ABCDEF`) of length `length`.\n   * @param length Length of the result string\n   * @param uppercase Whether the string should use `ABCDEF` instead of `abcdef`\n   */\n  public hex(length: number, uppercase?: boolean): string {\n    return hex(uppercase)(this.engine, length);\n  }\n\n  /**\n   * Returns a random `Date` within the inclusive range of [`start`, `end`].\n   * @param start The minimum `Date`\n   * @param end The maximum `Date`\n   */\n  public date(start: Date, end: Date): Date {\n    return date(start, end)(this.engine);\n  }\n}\n", "import { INT32_SIZE } from \"./constants\";\n\n/**\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int32Array\n */\nconst I32Array: typeof Int32Array = (() => {\n  try {\n    const buffer = new ArrayBuffer(4);\n    const view = new Int32Array(buffer);\n    view[0] = INT32_SIZE;\n    if (view[0] === -INT32_SIZE) {\n      return Int32Array;\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  return (Array as unknown) as typeof Int32Array;\n})();\nexport { I32Array as Int32Array };\n", "import { Engine } from \"../types\";\nimport { Int32Array } from \"../utils/Int32Array\";\n\nlet data: Int32Array | null = null;\nconst COUNT = 128;\nlet index = COUNT;\n\n/**\n * An Engine that relies on the globally-available `crypto.getRandomValues`,\n * which is typically available in modern browsers.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues\n *\n * If unavailable or otherwise non-functioning, then `browserCrypto` will\n * likely `throw` on the first call to `next()`.\n */\nexport const browserCrypto: Engine = {\n  next() {\n    if (index >= COUNT) {\n      if (data === null) {\n        data = new Int32Array(COUNT);\n      }\n      crypto.getRandomValues(data);\n      index = 0;\n    }\n    return data![index++] | 0;\n  }\n};\n", "import { nativeMath } from \"../engine/nativeMath\";\nimport { Engine } from \"../types\";\n\n/**\n * Returns an array of random int32 values, based on current time\n * and a random number engine\n *\n * @param engine an Engine to pull random values from, default `nativeMath`\n * @param length the length of the Array, minimum 1, default 16\n */\nexport function createEntropy(\n  engine: Engine = nativeMath,\n  length: number = 16\n): number[] {\n  const array: number[] = [];\n  array.push(new Date().getTime() | 0);\n  for (let i = 1; i < length; ++i) {\n    array[i] = engine.next() | 0;\n  }\n  return array;\n}\n", "import { UINT32_MAX } from \"./constants\";\n\n/**\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul\n */\nexport const imul: (a: number, b: number) => number = (() => {\n  try {\n    if ((Math as any).imul(UINT32_MAX, 5) === -5) {\n      return (Math as any).imul;\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  const UINT16_MAX = 0xffff;\n  return (a: number, b: number) => {\n    const ah = (a >>> 16) & UINT16_MAX;\n    const al = a & UINT16_MAX;\n    const bh = (b >>> 16) & UINT16_MAX;\n    const bl = b & UINT16_MAX;\n    // the shift by 0 fixes the sign on the high part\n    // the final |0 converts the unsigned value into a signed value\n    return (al * bl + (((ah * bl + al * bh) << 16) >>> 0)) | 0;\n  };\n})();\n", "import { Engine } from \"../types\";\nimport { INT32_MAX, INT32_SIZE } from \"../utils/constants\";\nimport { createEntropy } from \"../utils/createEntropy\";\nimport { imul } from \"../utils/imul\";\nimport { Int32Array } from \"../utils/Int32Array\";\n\nconst ARRAY_SIZE = 624;\nconst ARRAY_MAX = ARRAY_SIZE - 1;\nconst M = 397;\nconst ARRAY_SIZE_MINUS_M = ARRAY_SIZE - M;\nconst A = 0x9908b0df;\n\n/**\n * An Engine that is a pseudorandom number generator using the Mersenne\n * Twister algorithm based on the prime 2**19937 − 1\n *\n * See http://en.wikipedia.org/wiki/Mersenne_twister\n */\nexport class MersenneTwister19937 implements Engine {\n  /**\n   * Returns a MersenneTwister19937 seeded with an initial int32 value\n   * @param initial the initial seed value\n   */\n  public static seed(initial: number): MersenneTwister19937 {\n    return new MersenneTwister19937().seed(initial);\n  }\n\n  /**\n   * Returns a MersenneTwister19937 seeded with zero or more int32 values\n   * @param source A series of int32 values\n   */\n  public static seedWithArray(source: ArrayLike<number>): MersenneTwister19937 {\n    return new MersenneTwister19937().seedWithArray(source);\n  }\n\n  /**\n   * Returns a MersenneTwister19937 seeded with the current time and\n   * a series of natively-generated random values\n   */\n  public static autoSeed(): MersenneTwister19937 {\n    return MersenneTwister19937.seedWithArray(createEntropy());\n  }\n\n  private readonly data = new Int32Array(ARRAY_SIZE);\n  private index = 0; // integer within [0, 624]\n  private uses = 0;\n\n  /**\n   * MersenneTwister19937 should not be instantiated directly.\n   * Instead, use the static methods `seed`, `seedWithArray`, or `autoSeed`.\n   */\n  private constructor() {}\n\n  /**\n   * Returns the next int32 value of the sequence\n   */\n  public next(): number {\n    if ((this.index | 0) >= ARRAY_SIZE) {\n      refreshData(this.data);\n      this.index = 0;\n    }\n\n    const value = this.data[this.index];\n    this.index = (this.index + 1) | 0;\n    this.uses += 1;\n    return temper(value) | 0;\n  }\n\n  /**\n   * Returns the number of times that the Engine has been used.\n   *\n   * This can be provided to an unused MersenneTwister19937 with the same\n   * seed, bringing it to the exact point that was left off.\n   */\n  public getUseCount(): number {\n    return this.uses;\n  }\n\n  /**\n   * Discards one or more items from the engine\n   * @param count The count of items to discard\n   */\n  public discard(count: number): this {\n    if (count <= 0) {\n      return this;\n    }\n    this.uses += count;\n    if ((this.index | 0) >= ARRAY_SIZE) {\n      refreshData(this.data);\n      this.index = 0;\n    }\n    while (count + this.index > ARRAY_SIZE) {\n      count -= ARRAY_SIZE - this.index;\n      refreshData(this.data);\n      this.index = 0;\n    }\n    this.index = (this.index + count) | 0;\n    return this;\n  }\n\n  private seed(initial: number): this {\n    let previous = 0;\n    this.data[0] = previous = initial | 0;\n\n    for (let i = 1; i < ARRAY_SIZE; i = (i + 1) | 0) {\n      this.data[i] = previous =\n        (imul(previous ^ (previous >>> 30), 0x6c078965) + i) | 0;\n    }\n    this.index = ARRAY_SIZE;\n    this.uses = 0;\n    return this;\n  }\n\n  private seedWithArray(source: ArrayLike<number>): this {\n    this.seed(0x012bd6aa);\n    seedWithArray(this.data, source);\n    return this;\n  }\n}\n\nfunction refreshData(data: Int32Array) {\n  let k = 0;\n  let tmp = 0;\n  for (; (k | 0) < ARRAY_SIZE_MINUS_M; k = (k + 1) | 0) {\n    tmp = (data[k] & INT32_SIZE) | (data[(k + 1) | 0] & INT32_MAX);\n    data[k] = data[(k + M) | 0] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n  }\n\n  for (; (k | 0) < ARRAY_MAX; k = (k + 1) | 0) {\n    tmp = (data[k] & INT32_SIZE) | (data[(k + 1) | 0] & INT32_MAX);\n    data[k] =\n      data[(k - ARRAY_SIZE_MINUS_M) | 0] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n  }\n\n  tmp = (data[ARRAY_MAX] & INT32_SIZE) | (data[0] & INT32_MAX);\n  data[ARRAY_MAX] = data[M - 1] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n}\n\nfunction temper(value: number) {\n  value ^= value >>> 11;\n  value ^= (value << 7) & 0x9d2c5680;\n  value ^= (value << 15) & 0xefc60000;\n  return value ^ (value >>> 18);\n}\n\nfunction seedWithArray(data: Int32Array, source: ArrayLike<number>) {\n  let i = 1;\n  let j = 0;\n  const sourceLength = source.length;\n  let k = Math.max(sourceLength, ARRAY_SIZE) | 0;\n  let previous = data[0] | 0;\n  for (; (k | 0) > 0; --k) {\n    data[i] = previous =\n      ((data[i] ^ imul(previous ^ (previous >>> 30), 0x0019660d)) +\n        (source[j] | 0) +\n        (j | 0)) |\n      0;\n    i = (i + 1) | 0;\n    ++j;\n    if ((i | 0) > ARRAY_MAX) {\n      data[0] = data[ARRAY_MAX];\n      i = 1;\n    }\n    if (j >= sourceLength) {\n      j = 0;\n    }\n  }\n  for (k = ARRAY_MAX; (k | 0) > 0; --k) {\n    data[i] = previous =\n      ((data[i] ^ imul(previous ^ (previous >>> 30), 0x5d588b65)) - i) | 0;\n    i = (i + 1) | 0;\n    if ((i | 0) > ARRAY_MAX) {\n      data[0] = data[ARRAY_MAX];\n      i = 1;\n    }\n  }\n  data[0] = INT32_SIZE;\n}\n", "import { Engine } from \"../types\";\n\nlet data: Int32Array | null = null;\nconst COUNT = 128;\nlet index = COUNT;\n\n/**\n * An Engine that relies on the node-available\n * `require('crypto').randomBytes`, which has been available since 0.58.\n *\n * See https://nodejs.org/api/crypto.html#crypto_crypto_randombytes_size_callback\n *\n * If unavailable or otherwise non-functioning, then `nodeCrypto` will\n * likely `throw` on the first call to `next()`.\n */\nexport const nodeCrypto: Engine = {\n  next() {\n    if (index >= COUNT) {\n      data = new Int32Array(\n        new Int8Array(require(\"crypto\").randomBytes(4 * COUNT)).buffer\n      );\n      index = 0;\n    }\n    return data![index++] | 0;\n  }\n};\n", "import { Distribution } from \"../types\";\nimport { sliceArray } from \"../utils/sliceArray\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution to random value within the provided `source`\n * within the sliced bounds of `begin` and `end`.\n * @param source an array of items to pick from\n * @param begin the beginning slice index (defaults to `0`)\n * @param end the ending slice index (defaults to `source.length`)\n */\nexport function picker<T>(\n  source: ArrayLike<T>,\n  begin?: number,\n  end?: number\n): Distribution<T> {\n  const clone = sliceArray.call(source, begin, end);\n  if (clone.length === 0) {\n    throw new RangeError(`Cannot pick from a source with no items`);\n  }\n  const distribution = integer(0, clone.length - 1);\n  return engine => clone[distribution(engine)];\n}\n"]}