{"version": 3, "file": "firebase-functions.js", "sources": ["../util/src/crypt.ts", "../util/src/defaults.ts", "../util/src/global.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../functions/src/serializer.ts", "../functions/src/constants.ts", "../functions/src/error.ts", "../functions/src/context.ts", "../functions/src/service.ts", "../functions/src/api.ts", "../functions/src/config.ts", "../functions/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  constructor(\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions();\n"], "names": ["base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "i", "length", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte1", "outByte2", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "str", "out", "p", "c", "charCodeAt", "stringToByteArray", "decodeString", "bytes", "pos", "c1", "String", "fromCharCode", "c2", "u", "c3", "byteArrayToString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "getDefaultsFromGlobal", "getGlobal", "self", "window", "global", "__FIREBASE_DEFAULTS__", "getDefaultsFromCookie", "document", "match", "cookie", "e", "decoded", "console", "error", "base64Decode", "JSON", "parse", "getDefaults", "process", "env", "defaultsJsonString", "getDefaultsFromEnvVariable", "info", "getDefaultEmulatorHostnameAndPort", "productName", "host", "_a", "_b", "emulatorHosts", "getDefaultEmulatorHost", "separatorIndex", "lastIndexOf", "port", "parseInt", "substring", "FirebaseError", "code", "message", "customData", "super", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "mapValues", "o", "f", "result", "hasOwnProperty", "encode", "Number", "valueOf", "isFinite", "toString", "call", "Date", "toISOString", "map", "x", "decode", "json", "isNaN", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "details", "_errorForResponse", "status", "bodyJSON", "codeForHTTPStatus", "description", "errorJSON", "undefined", "ContextProvider", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "getImmediate", "optional", "get", "then", "async", "token", "getToken", "accessToken", "Notification", "permission", "limitedUseAppCheckTokens", "getLimitedUseToken", "authToken", "getAuthToken", "messagingToken", "getMessagingToken", "appCheckToken", "getAppCheckToken", "responseLineRE", "FunctionsService", "app", "regionOrCustomDomain", "fetchImpl", "args", "fetch", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "Promise", "resolve", "deleteService", "url", "URL", "customDomain", "origin", "pathname", "region", "_delete", "_url", "projectId", "options", "httpsCallable", "functionsInstance", "callable", "callAtURL", "stream", "streamAtURL", "postJSON", "body", "headers", "response", "method", "stringify", "makeAuthHeaders", "context", "getContext", "failAfterHandle", "failAfter", "millis", "timer", "promise", "reject", "setTimeout", "cancel", "clearTimeout", "timeout", "race", "responseData", "resultResolver", "resultRejecter", "signal", "Symbol", "asyncIterator", "next", "resultPromise", "addEventListener", "rstream", "createResponseStream", "reader", "processLine", "line", "controller", "jsonData", "enqueue", "decoder", "TextDecoder", "ReadableStream", "start", "currentText", "pump", "aborted", "done", "read", "trim", "close", "lines", "split", "pop", "functionsError", "<PERSON><PERSON><PERSON><PERSON>", "rreader", "getFunctions", "getApp", "_get<PERSON><PERSON><PERSON>", "identifier", "emulator", "connectFunctionsEmulator", "_connectFunctionsEmulator", "_httpsCallable", "httpsCallableFromURL", "_httpsCallableFromURL", "registerFunctions", "variant", "_registerComponent", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "registerVersion"], "mappings": "iGAiBA,MA0FaA,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKEC,mBACF,OAAOC,KAAKF,kBAAoB,OAM9BG,2BACF,OAAOD,KAAKF,kBAAoB,OAUlCI,mBAAoC,mBAATC,KAW3BC,gBAAgBC,EAA8BC,GAC5C,IAAKC,MAAMC,QAAQH,GACjB,MAAMI,MAAM,iDAGdT,KAAKU,QAEL,MAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAEHkB,EAAS,GAEf,IAAK,IAAIC,EAAI,EAAGA,EAAIR,EAAMS,OAAQD,GAAK,EAAG,CACxC,MAAME,EAAQV,EAAMQ,GACdG,EAAYH,EAAI,EAAIR,EAAMS,OAC1BG,EAAQD,EAAYX,EAAMQ,EAAI,GAAK,EACnCK,EAAYL,EAAI,EAAIR,EAAMS,OAC1BK,EAAQD,EAAYb,EAAMQ,EAAI,GAAK,EAEnCO,EAAWL,GAAS,EACpBM,GAAqB,EAARN,IAAiB,EAAME,GAAS,EACnD,IAAIK,GAAqB,GAARL,IAAiB,EAAME,GAAS,EAC7CI,EAAmB,GAARJ,EAEVD,IACHK,EAAW,GAENP,IACHM,EAAW,KAIfV,EAAOY,KACLb,EAAcS,GACdT,EAAcU,GACdV,EAAcW,GACdX,EAAcY,IAIlB,OAAOX,EAAOa,KAAK,KAWrBC,aAAarB,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBqB,KAAKtB,GAEPL,KAAKI,gBAlNU,SAAUwB,GAElC,MAAMC,EAAgB,GACtB,IAAIC,EAAI,EACR,IAAK,IAAIjB,EAAI,EAAGA,EAAIe,EAAId,OAAQD,IAAK,CACnC,IAAIkB,EAAIH,EAAII,WAAWnB,GACnBkB,EAAI,IACNF,EAAIC,KAAOC,EACFA,EAAI,MACbF,EAAIC,KAAQC,GAAK,EAAK,IACtBF,EAAIC,KAAY,GAAJC,EAAU,KAEL,QAAZ,MAAJA,IACDlB,EAAI,EAAIe,EAAId,QACyB,QAAZ,MAAxBc,EAAII,WAAWnB,EAAI,KAGpBkB,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBH,EAAII,aAAanB,IACvDgB,EAAIC,KAAQC,GAAK,GAAM,IACvBF,EAAIC,KAASC,GAAK,GAAM,GAAM,IAC9BF,EAAIC,KAASC,GAAK,EAAK,GAAM,IAC7BF,EAAIC,KAAY,GAAJC,EAAU,MAEtBF,EAAIC,KAAQC,GAAK,GAAM,IACvBF,EAAIC,KAASC,GAAK,EAAK,GAAM,IAC7BF,EAAIC,KAAY,GAAJC,EAAU,KAG1B,OAAOF,EAsLuBI,CAAkB5B,GAAQC,IAWxD4B,aAAa7B,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBH,KAAKE,GA5LQ,SAAU8B,GAElC,MAAMN,EAAgB,GACtB,IAAIO,EAAM,EACRL,EAAI,EACN,KAAOK,EAAMD,EAAMrB,QAAQ,CACzB,MAAMuB,EAAKF,EAAMC,KACjB,GAAIC,EAAK,IACPR,EAAIE,KAAOO,OAAOC,aAAaF,QAC1B,GAAIA,EAAK,KAAOA,EAAK,IAAK,CAC/B,MAAMG,EAAKL,EAAMC,KACjBP,EAAIE,KAAOO,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALG,QAC9C,GAAIH,EAAK,KAAOA,EAAK,IAAK,CAE/B,MAGMI,IACI,EAALJ,IAAW,IAAa,GAJlBF,EAAMC,OAImB,IAAa,GAHtCD,EAAMC,OAGuC,EAAW,GAFxDD,EAAMC,MAGf,MACFP,EAAIE,KAAOO,OAAOC,aAAa,OAAUE,GAAK,KAC9CZ,EAAIE,KAAOO,OAAOC,aAAa,OAAc,KAAJE,QACpC,CACL,MAAMD,EAAKL,EAAMC,KACXM,EAAKP,EAAMC,KACjBP,EAAIE,KAAOO,OAAOC,cACT,GAALF,IAAY,IAAa,GAALG,IAAY,EAAW,GAALE,IAI9C,OAAOb,EAAIJ,KAAK,IAgKPkB,CAAkB3C,KAAK4C,wBAAwBvC,EAAOC,KAkB/DsC,wBAAwBvC,EAAeC,GACrCN,KAAKU,QAEL,MAAMmC,EAAgBvC,EAClBN,KAAKH,sBACLG,KAAKL,eAEHiB,EAAmB,GAEzB,IAAK,IAAIC,EAAI,EAAGA,EAAIR,EAAMS,QAAU,CAClC,MAAMC,EAAQ8B,EAAcxC,EAAMyC,OAAOjC,MAGnCI,EADYJ,EAAIR,EAAMS,OACF+B,EAAcxC,EAAMyC,OAAOjC,IAAM,IACzDA,EAEF,MACMM,EADYN,EAAIR,EAAMS,OACF+B,EAAcxC,EAAMyC,OAAOjC,IAAM,KACzDA,EAEF,MACMkC,EADYlC,EAAIR,EAAMS,OACF+B,EAAcxC,EAAMyC,OAAOjC,IAAM,GAG3D,KAFEA,EAEW,MAATE,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAAT4B,EACrD,MAAM,IAAIC,wBAGZ,MAAM5B,EAAYL,GAAS,EAAME,GAAS,EAG1C,GAFAL,EAAOY,KAAKJ,GAEE,KAAVD,EAAc,CAChB,MAAME,EAAaJ,GAAS,EAAK,IAASE,GAAS,EAGnD,GAFAP,EAAOY,KAAKH,GAEE,KAAV0B,EAAc,CAChB,MAAMzB,EAAaH,GAAS,EAAK,IAAQ4B,EACzCnC,EAAOY,KAAKF,KAKlB,OAAOV,GAQTF,QACE,IAAKV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAK,IAAIgB,EAAI,EAAGA,EAAIb,KAAKD,aAAae,OAAQD,IAC5Cb,KAAKN,eAAemB,GAAKb,KAAKD,aAAa+C,OAAOjC,GAClDb,KAAKL,eAAeK,KAAKN,eAAemB,IAAMA,EAC9Cb,KAAKJ,sBAAsBiB,GAAKb,KAAKC,qBAAqB6C,OAAOjC,GACjEb,KAAKH,sBAAsBG,KAAKJ,sBAAsBiB,IAAMA,EAGxDA,GAAKb,KAAKF,kBAAkBgB,SAC9Bd,KAAKL,eAAeK,KAAKC,qBAAqB6C,OAAOjC,IAAMA,EAC3Db,KAAKH,sBAAsBG,KAAKD,aAAa+C,OAAOjC,IAAMA,MAU9D,MAAOmC,gCAAgCvC,MAA7CwC,kCACWjD,KAAIkD,KAAG,2BC7RlB,MAAMC,sBAAwB,ICjCd,SAAAC,YACd,GAAoB,oBAATC,KACT,OAAOA,KAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,MAAM,IAAI9C,MAAM,mCDwBhB2C,GAAYI,sBAoBRC,sBAAwB,KAC5B,GAAwB,oBAAbC,SACT,OAEF,IAAIC,EACJ,IACEA,EAAQD,SAASE,OAAOD,MAAM,iCAC9B,MAAOE,GAGP,OAEF,MAAMC,EAAUH,GDyRU,SAAU/B,GACpC,IACE,OAAOnC,EAAOyC,aAAaN,GAAK,GAChC,MAAOiC,GACPE,QAAQC,MAAM,wBAAyBH,GAEzC,OAAO,KC/RkBI,CAAaN,EAAM,IAC5C,OAAOG,GAAWI,KAAKC,MAAML,IAUlBM,YAAc,KACzB,IACE,OACEjB,yBApC6B,MACjC,GAAuB,oBAAZkB,cAAkD,IAAhBA,QAAQC,IACnD,OAEF,MAAMC,EAAqBF,QAAQC,IAAId,sBACvC,OAAIe,EACKL,KAAKC,MAAMI,QADpB,GAgCIC,IACAf,wBAEF,MAAOI,GAQP,YADAE,QAAQU,KAAK,+CAA+CZ,OAqBnDa,kCACXC,IAEA,MAAMC,EAb8B,CACpCD,IACuB,IAAAE,EAAAC,EAAA,OAA4B,QAA5BA,EAAe,QAAfD,EAAAT,qBAAe,IAAAS,OAAA,EAAAA,EAAAE,qBAAa,IAAAD,OAAA,EAAAA,EAAGH,IAWzCK,CAAuBL,GACpC,IAAKC,EACH,OAEF,MAAMK,EAAiBL,EAAKM,YAAY,KACxC,GAAID,GAAkB,GAAKA,EAAiB,IAAML,EAAK9D,OACrD,MAAM,IAAIL,MAAM,gBAAgBmE,yCAGlC,MAAMO,EAAOC,SAASR,EAAKS,UAAUJ,EAAiB,GAAI,IAC1D,MAAgB,MAAZL,EAAK,GAEA,CAACA,EAAKS,UAAU,EAAGJ,EAAiB,GAAIE,GAExC,CAACP,EAAKS,UAAU,EAAGJ,GAAiBE,IE9EzC,MAAOG,sBAAsB7E,MAIjCwC,YAEWsC,EACTC,EAEOC,GAEPC,MAAMF,GALGxF,KAAIuF,KAAJA,EAGFvF,KAAUyF,WAAVA,EAPAzF,KAAIkD,KAdI,gBA6BfyC,OAAOC,eAAe5F,KAAMsF,cAAcO,WAItCpF,MAAMqF,mBACRrF,MAAMqF,kBAAkB9F,KAAM+F,aAAaF,UAAUG,SAK9C,MAAAD,aAIX9C,YACmBgD,EACAC,EACAC,GAFAnG,KAAOiG,QAAPA,EACAjG,KAAWkG,YAAXA,EACAlG,KAAMmG,OAANA,EAGnBH,OACET,KACGa,GAEH,MAAMX,EAAcW,EAAK,IAAoB,GACvCC,EAAW,GAAGrG,KAAKiG,WAAWV,IAC9Be,EAAWtG,KAAKmG,OAAOZ,GAEvBC,EAAUc,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBtE,OAAOsE,GAAS,IAAID,SAbhBJ,CAAgBD,EAAUb,GAAc,QAE7DoB,EAAc,GAAG7G,KAAKkG,gBAAgBV,MAAYa,MAIxD,OAFc,IAAIf,cAAce,EAAUQ,EAAapB,IAa3D,MAAMgB,EAAU,gBClHV,SAAUK,mBACdb,GAEA,OAAIA,GAAYA,EAA+Bc,UACrCd,EAA+Bc,UAEhCd,ECCE,MAAAe,UAiBX/D,YACWC,EACA+D,EACAC,GAFAlH,KAAIkD,KAAJA,EACAlD,KAAeiH,gBAAfA,EACAjH,KAAIkH,KAAJA,EAnBXlH,KAAiBmH,mBAAG,EAIpBnH,KAAYoH,aAAe,GAE3BpH,KAAAqH,kBAA2C,OAE3CrH,KAAiBsH,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADAxH,KAAKqH,kBAAoBG,EAClBxH,KAGTyH,qBAAqBN,GAEnB,OADAnH,KAAKmH,kBAAoBA,EAClBnH,KAGT0H,gBAAgBC,GAEd,OADA3H,KAAKoH,aAAeO,EACb3H,KAGT4H,2BAA2BC,GAEzB,OADA7H,KAAKsH,kBAAoBO,EAClB7H,MCjDX,SAAS8H,UAGPC,EACAC,GAEA,MAAMC,EAAqC,GAC3C,IAAK,MAAMtB,KAAOoB,EACZA,EAAEG,eAAevB,KACnBsB,EAAOtB,GAAOqB,EAAED,EAAEpB,KAGtB,OAAOsB,EASH,SAAUE,OAAO/B,GACrB,GAAY,MAARA,EACF,OAAO,KAKT,GAHIA,aAAgBgC,SAClBhC,EAAOA,EAAKiC,WAEM,iBAATjC,GAAqBkC,SAASlC,GAGvC,OAAOA,EAET,IAAa,IAATA,IAA0B,IAATA,EACnB,OAAOA,EAET,GAA6C,oBAAzCT,OAAOE,UAAU0C,SAASC,KAAKpC,GACjC,OAAOA,EAET,GAAIA,aAAgBqC,KAClB,OAAOrC,EAAKsC,cAEd,GAAInI,MAAMC,QAAQ4F,GAChB,OAAOA,EAAKuC,KAAIC,GAAKT,OAAOS,KAE9B,GAAoB,mBAATxC,GAAuC,iBAATA,EACvC,OAAO0B,UAAU1B,GAAOwC,GAAKT,OAAOS,KAGtC,MAAM,IAAInI,MAAM,mCAAqC2F,GASjD,SAAUyC,OAAOC,GACrB,GAAY,MAARA,EACF,OAAOA,EAET,GAAKA,EAAoC,SACvC,OAASA,EAAoC,UAC3C,IAnEY,iDAqEZ,IApEqB,kDAoEI,CAIvB,MAAMlC,EAAQwB,OAAQU,EAA2C,OACjE,GAAIC,MAAMnC,GACR,MAAM,IAAInG,MAAM,qCAAuCqI,GAEzD,OAAOlC,EAET,QACE,MAAM,IAAInG,MAAM,qCAAuCqI,GAI7D,OAAIvI,MAAMC,QAAQsI,GACTA,EAAKH,KAAIC,GAAKC,OAAOD,KAEV,mBAATE,GAAuC,iBAATA,EAChChB,UAAUgB,GAAOF,GAAKC,OAAOD,KAG/BE,ECvFF,MCUDE,EAAuD,CAC3DC,GAAI,KACJC,UAAW,YACXC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,UAAW,YACXC,eAAgB,iBAChBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,QAAS,UACTC,aAAc,eACdC,cAAe,gBACfC,SAAU,WACVC,YAAa,cACbC,UAAW,aAUP,MAAOC,uBAAuB5E,cAIlCrC,YAKEsC,EACAC,EAIS2E,GAETzE,MAAM,aAAqBH,IAAQC,GAAW,IAFrCxF,KAAOmK,QAAPA,EAMTxE,OAAOC,eAAe5F,KAAMkK,eAAerE,YAmD/B,SAAAuE,kBACdC,EACAC,GAEA,IAKIH,EALA5E,EA3CN,SAASgF,kBAAkBF,GAEzB,GAAIA,GAAU,KAAOA,EAAS,IAC5B,MAAO,KAET,OAAQA,GACN,KAAK,EAiBL,KAAK,IACH,MAAO,WAfT,KAAK,IACH,MAAO,mBACT,KAAK,IACH,MAAO,kBACT,KAAK,IACH,MAAO,oBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,UACT,KAAK,IACH,MAAO,qBACT,KAAK,IACH,MAAO,YAGT,KAAK,IACH,MAAO,gBACT,KAAK,IACH,MAAO,cACT,KAAK,IACH,MAAO,oBAGX,MAAO,UAUIE,CAAkBF,GAGzBG,EAAsBjF,EAK1B,IACE,MAAMkF,EAAYH,GAAYA,EAAStG,MACvC,GAAIyG,EAAW,CACb,MAAMJ,EAASI,EAAUJ,OACzB,GAAsB,iBAAXA,EAAqB,CAC9B,IAAKrB,EAAaqB,GAEhB,OAAO,IAAIH,eAAe,WAAY,YAExC3E,EAAOyD,EAAaqB,GAIpBG,EAAcH,EAGhB,MAAM7E,EAAUiF,EAAUjF,QACH,iBAAZA,IACTgF,EAAchF,GAGhB2E,EAAUM,EAAUN,aACJO,IAAZP,IACFA,EAAUtB,OAAOsB,KAGrB,MAAOtG,IAIT,MAAa,OAAT0B,EAIK,KAGF,IAAI2E,eAAe3E,EAAMiF,EAAaL,GCpIlC,MAAAQ,gBAIX1H,YACE2H,EACAC,EACAC,GANM9K,KAAI+K,KAAgC,KACpC/K,KAASgL,UAA6B,KACtChL,KAAQiL,SAAoC,KAMlDjL,KAAK+K,KAAOH,EAAaM,aAAa,CAAEC,UAAU,IAClDnL,KAAKgL,UAAYH,EAAkBK,aAAa,CAC9CC,UAAU,IAGPnL,KAAK+K,MACRH,EAAaQ,MAAMC,MACjBN,GAAS/K,KAAK+K,KAAOA,IACrB,SAMC/K,KAAKgL,WACRH,EAAkBO,MAAMC,MACtBL,GAAchL,KAAKgL,UAAYA,IAC/B,SAMChL,KAAKiL,UACRH,EAAiBM,MAAMC,MACrBJ,GAAajL,KAAKiL,SAAWA,IAC7B,SAONK,qBACE,GAAKtL,KAAK+K,KAIV,IACE,MAAMQ,QAAcvL,KAAK+K,KAAKS,WAC9B,OAAOD,MAAAA,OAAA,EAAAA,EAAOE,YACd,MAAO5H,GAEP,QAIJyH,0BACE,GACGtL,KAAKgL,WACJ,iBAAkB3H,MACQ,YAA5BqI,aAAaC,WAKf,IACE,aAAa3L,KAAKgL,UAAUQ,WAC5B,MAAO3H,GAKP,QAIJyH,uBACEM,GAEA,GAAI5L,KAAKiL,SAAU,CACjB,MAAMhD,EAAS2D,QACL5L,KAAKiL,SAASY,2BACd7L,KAAKiL,SAASO,WACxB,OAAIvD,EAAOjE,MAIF,KAEFiE,EAAOsD,MAEhB,OAAO,KAGTD,iBAAiBM,GAIf,MAAO,CAAEE,gBAHe9L,KAAK+L,eAGTC,qBAFShM,KAAKiM,oBAEEC,oBADRlM,KAAKmM,iBAAiBP,KC7G/C,MAEDQ,EAAiB,uBA0DV,MAAAC,iBAYXpJ,YACWqJ,EACT1B,EACAC,EACAC,EACAyB,EA7E0B,cA8EjBC,EAA0B,KAAIC,IAASC,SAASD,KALhDzM,KAAGsM,IAAHA,EAKAtM,KAASwM,UAATA,EAhBXxM,KAAc2M,eAAkB,KAkB9B3M,KAAK4M,gBAAkB,IAAIjC,gBACzBC,EACAC,EACAC,GAGF9K,KAAK6M,kBAAoB,IAAIC,SAAQC,IACnC/M,KAAKgN,cAAgB,IACZF,QAAQC,QAAQA,QAK3B,IACE,MAAME,EAAM,IAAIC,IAAIX,GACpBvM,KAAKmN,aACHF,EAAIG,QAA2B,MAAjBH,EAAII,SAAmB,GAAKJ,EAAII,UAChDrN,KAAKsN,OAjGmB,cAkGxB,MAAOzJ,GACP7D,KAAKmN,aAAe,KACpBnN,KAAKsN,OAASf,GAIlBgB,UACE,OAAOvN,KAAKgN,gBAQdQ,KAAKtK,GACH,MAAMuK,EAAYzN,KAAKsM,IAAIoB,QAAQD,UACnC,GAA4B,OAAxBzN,KAAK2M,eAAyB,CAEhC,MAAO,GADQ3M,KAAK2M,kBACAc,KAAazN,KAAKsN,UAAUpK,IAGlD,OAA0B,OAAtBlD,KAAKmN,aACA,GAAGnN,KAAKmN,gBAAgBjK,IAG1B,WAAWlD,KAAKsN,UAAUG,wBAAgCvK,KA0BrDyK,SAAAA,gBACdC,EACA1K,EACAwK,GAEA,MAAMG,SACJzH,GAuHJ,SAASoC,KACPoF,EACA1K,EACAkD,EACAsH,GAEA,MAAMT,EAAMW,EAAkBJ,KAAKtK,GACnC,OAAO4K,UAAUF,EAAmBX,EAAK7G,EAAMsH,GA5HtClF,CAAKoF,EAAmB1K,EAAMkD,EAAMsH,GAAW,IAUxD,OAPAG,SAASE,OAAS,CAChB3H,EACAsH,IAkMJ,SAASK,OACPH,EACA1K,EACAkD,EACAsH,GAEA,MAAMT,EAAMW,EAAkBJ,KAAKtK,GACnC,OAAO8K,YAAYJ,EAAmBX,EAAK7G,EAAMsH,GAAW,IAvMnDK,CAAOH,EAAmB1K,EAAMkD,EAAMsH,GAGxCG,SAuCTvC,eAAe2C,SACbhB,EACAiB,EACAC,EACA3B,GAIA,IAAI4B,EAFJD,EAAQ,gBAAkB,mBAG1B,IACEC,QAAiB5B,EAAUS,EAAK,CAC9BoB,OAAQ,OACRH,KAAMhK,KAAKoK,UAAUJ,GACrBC,QAAAA,IAEF,MAAOtK,GAKP,MAAO,CACLwG,OAAQ,EACRvB,KAAM,MAGV,IAAIA,EAAgC,KACpC,IACEA,QAAasF,EAAStF,OACtB,MAAOjF,IAGT,MAAO,CACLwG,OAAQ+D,EAAS/D,OACjBvB,KAAAA,GAUJwC,eAAeiD,gBACbX,EACAF,GAEA,MAAMS,EAAkC,GAClCK,QAAgBZ,EAAkBhB,gBAAgB6B,WACtDf,EAAQ9B,0BAWV,OATI4C,EAAQ1C,YACVqC,EAAuB,cAAI,UAAYK,EAAQ1C,WAE7C0C,EAAQxC,iBACVmC,EAAQ,8BAAgCK,EAAQxC,gBAEpB,OAA1BwC,EAAQtC,gBACViC,EAAQ,uBAAyBK,EAAQtC,eAEpCiC,EAuBT7C,eAAewC,UACbF,EACAX,EACA7G,EACAsH,GAIA,MAAMQ,EAAO,CAAE9H,KADfA,EAAO+B,OAAO/B,IAIR+H,QAAgBI,gBAAgBX,EAAmBF,GAKnDgB,EA7QR,SAASC,UAAUC,GAIjB,IAAIC,EAAoB,KACxB,MAAO,CACLC,QAAS,IAAIhC,SAAQ,CAACpG,EAAGqI,KACvBF,EAAQG,YAAW,KACjBD,EAAO,IAAI7E,eAAe,oBAAqB,wBAC9C0E,MAELK,OAAQ,KACFJ,GACFK,aAAaL,KAgQKF,CAFRjB,EAAQyB,SAAW,KAG7Bf,QAAiBtB,QAAQsC,KAAK,CAClCnB,SAAShB,EAAKiB,EAAMC,EAASP,EAAkBpB,WAC/CkC,EAAgBI,QAChBlB,EAAkBf,oBAOpB,GAHA6B,EAAgBO,UAGXb,EACH,MAAM,IAAIlE,eACR,YACA,4CAKJ,MAAMlG,EAAQoG,kBAAkBgE,EAAS/D,OAAQ+D,EAAStF,MAC1D,GAAI9E,EACF,MAAMA,EAGR,IAAKoK,EAAStF,KACZ,MAAM,IAAIoB,eAAe,WAAY,sCAGvC,IAAImF,EAAejB,EAAStF,KAAK1C,KAMjC,QAH4B,IAAjBiJ,IACTA,EAAejB,EAAStF,KAAKb,aAEH,IAAjBoH,EAET,MAAM,IAAInF,eAAe,WAAY,mCAMvC,MAAO,CAAE9D,KAFWyC,OAAOwG,IA2B7B/D,eAAe0C,YACbJ,EACAX,EACA7G,EACAsH,SAIA,MAAMQ,EAAO,CAAE9H,KADfA,EAAO+B,OAAO/B,IAIR+H,QAAgBI,gBAAgBX,EAAmBF,GAIzD,IAAIU,EA2CAkB,EACAC,EA/CJpB,EAAQ,gBAAkB,mBAC1BA,EAAgB,OAAI,oBAGpB,IACEC,QAAiBR,EAAkBpB,UAAUS,EAAK,CAChDoB,OAAQ,OACRH,KAAMhK,KAAKoK,UAAUJ,GACrBC,QAAAA,EACAqB,OAAQ9B,MAAAA,OAAA,EAAAA,EAAS8B,SAEnB,MAAO3L,GACP,GAAIA,aAAapD,OAAoB,eAAXoD,EAAEX,KAAuB,CACjD,MAAMc,EAAQ,IAAIkG,eAAe,YAAa,0BAC9C,MAAO,CACL9D,KAAM0G,QAAQiC,OAAO/K,GACrB+J,OAAQ,CACN,CAAC0B,OAAOC,eAAc,KACb,CACLC,KAAI,IACK7C,QAAQiC,OAAO/K,OAWlC,MAAMA,EAAQoG,kBAAkB,EAAG,MACnC,MAAO,CACLhE,KAAM0G,QAAQiC,OAAO/K,GAErB+J,OAAQ,CACN,CAAC0B,OAAOC,eAAc,KACb,CACLC,KAAI,IACK7C,QAAQiC,OAAO/K,OASlC,MAAM4L,EAAgB,IAAI9C,SAAiB,CAACC,EAASgC,KACnDO,EAAiBvC,EACjBwC,EAAiBR,KAEJ,QAAflK,EAAA6I,MAAAA,OAAO,EAAPA,EAAS8B,cAAM,IAAA3K,GAAAA,EAAEgL,iBAAiB,SAAS,KACzC,MAAM7L,EAAQ,IAAIkG,eAAe,YAAa,0BAC9CqF,EAAevL,MAEjB,MACM8L,EAyCR,SAASC,qBACPC,EACAV,EACAC,EACAC,GAEA,MAAMS,YAAc,CAClBC,EACAC,KAEA,MAAMxM,EAAQuM,EAAKvM,MAAMyI,GAEzB,IAAKzI,EACH,OAEF,MAAMyC,EAAOzC,EAAM,GACnB,IACE,MAAMyM,EAAWlM,KAAKC,MAAMiC,GAC5B,GAAI,WAAYgK,EAEd,YADAd,EAAezG,OAAOuH,EAASnI,SAGjC,GAAI,YAAamI,EAEf,YADAD,EAAWE,QAAQxH,OAAOuH,EAAS5K,UAGrC,GAAI,UAAW4K,EAAU,CACvB,MAAMpM,EAAQoG,kBAAkB,EAAGgG,GAGnC,OAFAD,EAAWnM,MAAMA,QACjBuL,EAAevL,IAGjB,MAAOA,GACP,GAAIA,aAAiBkG,eAGnB,OAFAiG,EAAWnM,MAAMA,QACjBuL,EAAevL,KAOfsM,EAAU,IAAIC,YACpB,OAAO,IAAIC,eAAe,CACxBC,MAAMN,GACJ,IAAIO,EAAc,GAClB,OAAOC,OACPrF,eAAeqF,OACb,GAAInB,MAAAA,OAAM,EAANA,EAAQoB,QAAS,CACnB,MAAM5M,EAAQ,IAAIkG,eAChB,YACA,yBAIF,OAFAiG,EAAWnM,MAAMA,GACjBuL,EAAevL,GACR8I,QAAQC,UAEjB,IACE,MAAMnG,MAAEA,EAAKiK,KAAEA,SAAeb,EAAOc,OACrC,GAAID,EAKF,OAJIH,EAAYK,QACdd,YAAYS,EAAYK,OAAQZ,QAElCA,EAAWa,QAGb,GAAIxB,MAAAA,OAAM,EAANA,EAAQoB,QAAS,CACnB,MAAM5M,EAAQ,IAAIkG,eAChB,YACA,yBAKF,OAHAiG,EAAWnM,MAAMA,GACjBuL,EAAevL,cACTgM,EAAOf,SAGfyB,GAAeJ,EAAQzH,OAAOjC,EAAO,CAAEmH,QAAQ,IAC/C,MAAMkD,EAAQP,EAAYQ,MAAM,MAChCR,EAAcO,EAAME,OAAS,GAC7B,IAAK,MAAMjB,KAAQe,EACbf,EAAKa,QACPd,YAAYC,EAAKa,OAAQZ,GAG7B,OAAOQ,OACP,MAAO3M,GACP,MAAMoN,EACJpN,aAAiBkG,eACblG,EACAoG,kBAAkB,EAAG,MAC3B+F,EAAWnM,MAAMoN,GACjB7B,EAAe6B,MAIrBnC,OAAM,IACGe,EAAOf,WAzIFc,CADD3B,EAASF,KAAMmD,YAG5B/B,EACAC,EACA7B,MAAAA,OAAO,EAAPA,EAAS8B,QAEX,MAAO,CACLzB,OAAQ,CACN,CAAC0B,OAAOC,iBACN,MAAM4B,EAAUxB,EAAQuB,YACxB,MAAO,CACL/F,aACE,MAAM1E,MAAEA,EAAKiK,KAAEA,SAAeS,EAAQR,OACtC,MAAO,CAAElK,MAAOA,EAAkBiK,KAAAA,IAEpCvF,OAAY,gBACJgG,EAAQrC,SACP,CAAE4B,MAAM,EAAMjK,WAAO8D,OAKpCtE,KAAMwJ,iCCpcJ,SAAU2B,aACdjF,EAAmBkF,IACnBjF,EDd4B,eCiB5B,MAIMqB,EAJ2C6D,aAC/C3K,mBAAmBwF,GJ/BO,aIkCgBpB,aAAa,CACvDwG,WAAYnF,IAERoF,EAAWjN,kCAAkC,aAInD,OAHIiN,GACFC,yBAAyBhE,KAAsB+D,GAE1C/D,EAYO,SAAAgE,yBACdhE,EACAhJ,EACAO,ID8FcyM,SAAAA,2BACdhE,EACAhJ,EACAO,GAEAyI,EAAkBjB,eAAiB,UAAU/H,KAAQO,ICjGrD0M,CACE/K,mBAAqC8G,GACrChJ,EACAO,GASY,SAAAwI,cAKdC,EACA1K,EACAwK,GAEA,OAAOoE,gBACLhL,mBAAqC8G,GACrC1K,EACAwK,GASY,SAAAqE,qBAKdnE,EACAX,EACAS,GAEA,ODyFcqE,SAAAA,uBAKdnE,EACAX,EACAS,GAEA,MAAMG,SACJzH,GAEO0H,UAAUF,EAAmBX,EAAK7G,EAAMsH,GAAW,IAS5D,OANAG,SAASE,OAAS,CAChB3H,EACAsH,IAEOM,YAAYJ,EAAmBX,EAAK7G,EAAMsH,GAAW,IAEvDG,SC9GAmE,CACLlL,mBAAqC8G,GACrCX,EACAS,ICtFE,SAAUuE,kBAAkBC,GAqBhCC,EACE,IAAInL,ULvCsB,aKkBkB,CAC5CoL,GACEC,mBAAoB9F,MAGtB,MAAMD,EAAM8F,EAAUE,YAAY,OAAOpH,eACnCN,EAAewH,EAAUE,YAbkB,iBAc3CzH,EAAoBuH,EAAUE,YAVtC,sBAWQxH,EAAmBsH,EAAUE,YAbrC,sBAgBE,OAAO,IAAIjG,iBACTC,EACA1B,EACAC,EACAC,EACAyB,KASD,UAAC9E,sBAAqB,IAGzB8K,EAAgBrP,WAAegP,GAE/BK,EAAgBrP,WAAe,WCzCjC+O", "preExistingComment": "firebase-functions.js.map"}