{"version": 3, "file": "firebase-vertexai.js", "sources": ["../util/src/errors.ts", "../component/src/component.ts", "../logger/src/logger.ts", "../../node_modules/tslib/tslib.es6.js", "../vertexai/src/service.ts", "../vertexai/src/constants.ts", "../vertexai/src/errors.ts", "../vertexai/src/logger.ts", "../vertexai/src/requests/request.ts", "../vertexai/src/types/enums.ts", "../vertexai/src/types/schema.ts", "../vertexai/src/requests/response-helpers.ts", "../vertexai/src/requests/stream-reader.ts", "../vertexai/src/methods/generate-content.ts", "../vertexai/src/requests/request-helpers.ts", "../vertexai/src/methods/chat-session-helpers.ts", "../vertexai/src/methods/chat-session.ts", "../vertexai/src/models/generative-model.ts", "../vertexai/src/methods/count-tokens.ts", "../vertexai/src/requests/schema-builder.ts", "../vertexai/src/api.ts", "../util/src/compat.ts", "../vertexai/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { VertexAI, VertexAIOptions } from './public-types';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\nimport { DEFAULT_LOCATION } from './constants';\n\nexport class VertexAIService implements VertexAI, _FirebaseService {\n  auth: FirebaseAuthInternal | null;\n  appCheck: FirebaseAppCheckInternal | null;\n  location: string;\n\n  constructor(\n    public app: FirebaseApp,\n    authProvider?: Provider<FirebaseAuthInternalName>,\n    appCheckProvider?: Provider<AppCheckInternalComponentName>,\n    public options?: VertexAIOptions\n  ) {\n    const appCheck = appCheckProvider?.getImmediate({ optional: true });\n    const auth = authProvider?.getImmediate({ optional: true });\n    this.auth = auth || null;\n    this.appCheck = appCheck || null;\n    this.location = this.options?.location || DEFAULT_LOCATION;\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const VERTEX_TYPE = 'vertexAI';\n\nexport const DEFAULT_LOCATION = 'us-central1';\n\nexport const DEFAULT_BASE_URL = 'https://firebasevertexai.googleapis.com';\n\nexport const DEFAULT_API_VERSION = 'v1beta';\n\nexport const PACKAGE_VERSION = version;\n\nexport const LANGUAGE_TAG = 'gl-js';\n\nexport const DEFAULT_FETCH_TIMEOUT_MS = 180 * 1000;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { VertexAIErrorCode, CustomErrorData } from './types';\nimport { VERTEX_TYPE } from './constants';\n\n/**\n * Error class for the Vertex AI in Firebase SDK.\n *\n * @public\n */\nexport class VertexAIError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `VertexAIError` class.\n   *\n   * @param code - The error code from <code>{@link VertexAIErrorCode}</code>.\n   * @param message - A human-readable message describing the error.\n   * @param customErrorData - Optional error data.\n   */\n  constructor(\n    readonly code: VertexAIErrorCode,\n    message: string,\n    readonly customErrorData?: CustomErrorData\n  ) {\n    // Match error format used by FirebaseError from ErrorFactory\n    const service = VERTEX_TYPE;\n    const serviceName = 'VertexAI';\n    const fullCode = `${service}/${code}`;\n    const fullMessage = `${serviceName}: ${message} (${fullCode})`;\n    super(code, fullMessage);\n\n    // FirebaseError initializes a stack trace, but it assumes the error is created from the error\n    // factory. Since we break this assumption, we set the stack trace to be originating from this\n    // constructor.\n    // This is only supported in V8.\n    if (Error.captureStackTrace) {\n      // Allows us to initialize the stack trace without including the constructor itself at the\n      // top level of the stack trace.\n      Error.captureStackTrace(this, VertexAIError);\n    }\n\n    // Allows instanceof VertexAIError in ES5/ES6\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, VertexAIError.prototype);\n\n    // Since Error is an interface, we don't inherit toString and so we define it ourselves.\n    this.toString = () => fullMessage;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/vertexai');\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorDetails, RequestOptions, VertexAIErrorCode } from '../types';\nimport { VertexAIError } from '../errors';\nimport { ApiSettings } from '../types/internal';\nimport {\n  DEFAULT_API_VERSION,\n  DEFAULT_BASE_URL,\n  DEFAULT_FETCH_TIMEOUT_MS,\n  LANGUAGE_TAG,\n  PACKAGE_VERSION\n} from '../constants';\nimport { logger } from '../logger';\n\nexport enum Task {\n  GENERATE_CONTENT = 'generateContent',\n  STREAM_GENERATE_CONTENT = 'streamGenerateContent',\n  COUNT_TOKENS = 'countTokens'\n}\n\nexport class RequestUrl {\n  constructor(\n    public model: string,\n    public task: Task,\n    public apiSettings: ApiSettings,\n    public stream: boolean,\n    public requestOptions?: RequestOptions\n  ) {}\n  toString(): string {\n    // TODO: allow user-set option if that feature becomes available\n    const apiVersion = DEFAULT_API_VERSION;\n    const baseUrl = this.requestOptions?.baseUrl || DEFAULT_BASE_URL;\n    let url = `${baseUrl}/${apiVersion}`;\n    url += `/projects/${this.apiSettings.project}`;\n    url += `/locations/${this.apiSettings.location}`;\n    url += `/${this.model}`;\n    url += `:${this.task}`;\n    if (this.stream) {\n      url += '?alt=sse';\n    }\n    return url;\n  }\n\n  /**\n   * If the model needs to be passed to the backend, it needs to\n   * include project and location path.\n   */\n  get fullModelString(): string {\n    let modelString = `projects/${this.apiSettings.project}`;\n    modelString += `/locations/${this.apiSettings.location}`;\n    modelString += `/${this.model}`;\n    return modelString;\n  }\n}\n\n/**\n * Log language and \"fire/version\" to x-goog-api-client\n */\nfunction getClientHeaders(): string {\n  const loggingTags = [];\n  loggingTags.push(`${LANGUAGE_TAG}/${PACKAGE_VERSION}`);\n  loggingTags.push(`fire/${PACKAGE_VERSION}`);\n  return loggingTags.join(' ');\n}\n\nexport async function getHeaders(url: RequestUrl): Promise<Headers> {\n  const headers = new Headers();\n  headers.append('Content-Type', 'application/json');\n  headers.append('x-goog-api-client', getClientHeaders());\n  headers.append('x-goog-api-key', url.apiSettings.apiKey);\n  if (url.apiSettings.getAppCheckToken) {\n    const appCheckToken = await url.apiSettings.getAppCheckToken();\n    if (appCheckToken) {\n      headers.append('X-Firebase-AppCheck', appCheckToken.token);\n      if (appCheckToken.error) {\n        logger.warn(\n          `Unable to obtain a valid App Check token: ${appCheckToken.error.message}`\n        );\n      }\n    }\n  }\n\n  if (url.apiSettings.getAuthToken) {\n    const authToken = await url.apiSettings.getAuthToken();\n    if (authToken) {\n      headers.append('Authorization', `Firebase ${authToken.accessToken}`);\n    }\n  }\n\n  return headers;\n}\n\nexport async function constructRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<{ url: string; fetchOptions: RequestInit }> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  return {\n    url: url.toString(),\n    fetchOptions: {\n      method: 'POST',\n      headers: await getHeaders(url),\n      body\n    }\n  };\n}\n\nexport async function makeRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<Response> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  let response;\n  let fetchTimeoutId: string | number | NodeJS.Timeout | undefined;\n  try {\n    const request = await constructRequest(\n      model,\n      task,\n      apiSettings,\n      stream,\n      body,\n      requestOptions\n    );\n    // Timeout is 180s by default\n    const timeoutMillis =\n      requestOptions?.timeout != null && requestOptions.timeout >= 0\n        ? requestOptions.timeout\n        : DEFAULT_FETCH_TIMEOUT_MS;\n    const abortController = new AbortController();\n    fetchTimeoutId = setTimeout(() => abortController.abort(), timeoutMillis);\n    request.fetchOptions.signal = abortController.signal;\n\n    response = await fetch(request.url, request.fetchOptions);\n    if (!response.ok) {\n      let message = '';\n      let errorDetails;\n      try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n          message += ` ${JSON.stringify(json.error.details)}`;\n          errorDetails = json.error.details;\n        }\n      } catch (e) {\n        // ignored\n      }\n      if (\n        response.status === 403 &&\n        errorDetails.some(\n          (detail: ErrorDetails) => detail.reason === 'SERVICE_DISABLED'\n        ) &&\n        errorDetails.some((detail: ErrorDetails) =>\n          (\n            detail.links as Array<Record<string, string>>\n          )?.[0]?.description.includes(\n            'Google developers console API activation'\n          )\n        )\n      ) {\n        throw new VertexAIError(\n          VertexAIErrorCode.API_NOT_ENABLED,\n          `The Vertex AI in Firebase SDK requires the Vertex AI in Firebase ` +\n            `API ('firebasevertexai.googleapis.com') to be enabled in your ` +\n            `Firebase project. Enable this API by visiting the Firebase Console ` +\n            `at https://console.firebase.google.com/project/${url.apiSettings.project}/genai/ ` +\n            `and clicking \"Get started\". If you enabled this API recently, ` +\n            `wait a few minutes for the action to propagate to our systems and ` +\n            `then retry.`,\n          {\n            status: response.status,\n            statusText: response.statusText,\n            errorDetails\n          }\n        );\n      }\n      throw new VertexAIError(\n        VertexAIErrorCode.FETCH_ERROR,\n        `Error fetching from ${url}: [${response.status} ${response.statusText}] ${message}`,\n        {\n          status: response.status,\n          statusText: response.statusText,\n          errorDetails\n        }\n      );\n    }\n  } catch (e) {\n    let err = e as Error;\n    if (\n      (e as VertexAIError).code !== VertexAIErrorCode.FETCH_ERROR &&\n      (e as VertexAIError).code !== VertexAIErrorCode.API_NOT_ENABLED &&\n      e instanceof Error\n    ) {\n      err = new VertexAIError(\n        VertexAIErrorCode.ERROR,\n        `Error fetching from ${url.toString()}: ${e.message}`\n      );\n      err.stack = e.stack;\n    }\n\n    throw err;\n  } finally {\n    if (fetchTimeoutId) {\n      clearTimeout(fetchTimeoutId);\n    }\n  }\n  return response;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Role is the producer of the content.\n * @public\n */\nexport type Role = (typeof POSSIBLE_ROLES)[number];\n\n/**\n * Possible roles.\n * @public\n */\nexport const POSSIBLE_ROLES = ['user', 'model', 'function', 'system'] as const;\n\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nexport enum HarmCategory {\n  HARM_CATEGORY_HATE_SPEECH = 'HARM_CATEGORY_HATE_SPEECH',\n  HARM_CATEGORY_SEXUALLY_EXPLICIT = 'HARM_CATEGORY_SEXUALLY_EXPLICIT',\n  HARM_CATEGORY_HARASSMENT = 'HARM_CATEGORY_HARASSMENT',\n  HARM_CATEGORY_DANGEROUS_CONTENT = 'HARM_CATEGORY_DANGEROUS_CONTENT'\n}\n\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nexport enum HarmBlockThreshold {\n  // Content with NEGLIGIBLE will be allowed.\n  BLOCK_LOW_AND_ABOVE = 'BLOCK_LOW_AND_ABOVE',\n  // Content with NEGLIGIBLE and LOW will be allowed.\n  BLOCK_MEDIUM_AND_ABOVE = 'BLOCK_MEDIUM_AND_ABOVE',\n  // Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed.\n  BLOCK_ONLY_HIGH = 'BLOCK_ONLY_HIGH',\n  // All content will be allowed.\n  BLOCK_NONE = 'BLOCK_NONE'\n}\n\n/**\n * @public\n */\nexport enum HarmBlockMethod {\n  // The harm block method uses both probability and severity scores.\n  SEVERITY = 'SEVERITY',\n  // The harm block method uses the probability score.\n  PROBABILITY = 'PROBABILITY'\n}\n\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nexport enum HarmProbability {\n  // Content has a negligible chance of being unsafe.\n  NEGLIGIBLE = 'NEGLIGIBLE',\n  // Content has a low chance of being unsafe.\n  LOW = 'LOW',\n  // Content has a medium chance of being unsafe.\n  MEDIUM = 'MEDIUM',\n  // Content has a high chance of being unsafe.\n  HIGH = 'HIGH'\n}\n\n/**\n * Harm severity levels.\n * @public\n */\nexport enum HarmSeverity {\n  // Negligible level of harm severity.\n  HARM_SEVERITY_NEGLIGIBLE = 'HARM_SEVERITY_NEGLIGIBLE',\n  // Low level of harm severity.\n  HARM_SEVERITY_LOW = 'HARM_SEVERITY_LOW',\n  // Medium level of harm severity.\n  HARM_SEVERITY_MEDIUM = 'HARM_SEVERITY_MEDIUM',\n  // High level of harm severity.\n  HARM_SEVERITY_HIGH = 'HARM_SEVERITY_HIGH'\n}\n\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nexport enum BlockReason {\n  // Content was blocked by safety settings.\n  SAFETY = 'SAFETY',\n  // Content was blocked, but the reason is uncategorized.\n  OTHER = 'OTHER'\n}\n\n/**\n * Reason that a candidate finished.\n * @public\n */\nexport enum FinishReason {\n  // Natural stop point of the model or provided stop sequence.\n  STOP = 'STOP',\n  // The maximum number of tokens as specified in the request was reached.\n  MAX_TOKENS = 'MAX_TOKENS',\n  // The candidate content was flagged for safety reasons.\n  SAFETY = 'SAFETY',\n  // The candidate content was flagged for recitation reasons.\n  RECITATION = 'RECITATION',\n  // Unknown reason.\n  OTHER = 'OTHER'\n}\n\n/**\n * @public\n */\nexport enum FunctionCallingMode {\n  // Default model behavior, model decides to predict either a function call\n  // or a natural language response.\n  AUTO = 'AUTO',\n  // Model is constrained to always predicting a function call only.\n  // If \"allowed_function_names\" is set, the predicted function call will be\n  // limited to any one of \"allowed_function_names\", else the predicted\n  // function call will be any one of the provided \"function_declarations\".\n  ANY = 'ANY',\n  // Model will not predict any function call. Model behavior is same as when\n  // not passing any function declarations.\n  NONE = 'NONE'\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Contains the list of OpenAPI data types\n * as defined by the\n * {@link https://swagger.io/docs/specification/data-models/data-types/ | OpenAPI specification}\n * @public\n */\nexport enum SchemaType {\n  /** String type. */\n  STRING = 'string',\n  /** Number type. */\n  NUMBER = 'number',\n  /** Integer type. */\n  INTEGER = 'integer',\n  /** Boolean type. */\n  BOOLEAN = 'boolean',\n  /** Array type. */\n  ARRAY = 'array',\n  /** Object type. */\n  OBJECT = 'object'\n}\n\n/**\n * Basic <code>{@link Schema}</code> properties shared across several Schema-related\n * types.\n * @public\n */\nexport interface SchemaShared<T> {\n  /** Optional. The format of the property. */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /** Optional. The items of the property. */\n  items?: T;\n  /** Optional. Map of `Schema` objects. */\n  properties?: {\n    [k: string]: T;\n  };\n  /** Optional. The enum of the property. */\n  enum?: string[];\n  /** Optional. The example of the property. */\n  example?: unknown;\n  /** Optional. Whether the property is nullable. */\n  nullable?: boolean;\n  [key: string]: unknown;\n}\n\n/**\n * Params passed to <code>{@link Schema}</code> static methods to create specific\n * <code>{@link Schema}</code> classes.\n * @public\n */\nexport interface SchemaParams extends SchemaShared<SchemaInterface> {}\n\n/**\n * Final format for <code>{@link Schema}</code> params passed to backend requests.\n * @public\n */\nexport interface SchemaRequest extends SchemaShared<SchemaRequest> {\n  /**\n   * The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n  /** Optional. Array of required property. */\n  required?: string[];\n}\n\n/**\n * Interface for <code>{@link Schema}</code> class.\n * @public\n */\nexport interface SchemaInterface extends SchemaShared<SchemaInterface> {\n  /**\n   * The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n}\n\n/**\n * Interface for <code>{@link ObjectSchema}</code> class.\n * @public\n */\nexport interface ObjectSchemaInterface extends SchemaInterface {\n  type: SchemaType.OBJECT;\n  optionalProperties?: string[];\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  FinishReason,\n  FunctionCall,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\nimport { logger } from '../logger';\n\n/**\n * Creates an EnhancedGenerateContentResponse object that has helper functions and\n * other modifications that improve usability.\n */\nexport function createEnhancedContentResponse(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  /**\n   * The Vertex AI backend omits default values.\n   * This causes the `index` property to be omitted from the first candidate in the\n   * response, since it has index 0, and 0 is a default value.\n   * See: https://github.com/firebase/firebase-js-sdk/issues/8566\n   */\n  if (response.candidates && !response.candidates[0].hasOwnProperty('index')) {\n    response.candidates[0].index = 0;\n  }\n\n  const responseWithHelpers = addHelpers(response);\n  return responseWithHelpers;\n}\n\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nexport function addHelpers(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  (response as EnhancedGenerateContentResponse).text = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning text from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new VertexAIError(\n          VertexAIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getText(response);\n    } else if (response.promptFeedback) {\n      throw new VertexAIError(\n        VertexAIErrorCode.RESPONSE_ERROR,\n        `Text not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return '';\n  };\n  (response as EnhancedGenerateContentResponse).functionCalls = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning function calls from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new VertexAIError(\n          VertexAIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getFunctionCalls(response);\n    } else if (response.promptFeedback) {\n      throw new VertexAIError(\n        VertexAIErrorCode.RESPONSE_ERROR,\n        `Function call not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return undefined;\n  };\n  return response as EnhancedGenerateContentResponse;\n}\n\n/**\n * Returns all text found in all parts of first candidate.\n */\nexport function getText(response: GenerateContentResponse): string {\n  const textStrings = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.text) {\n        textStrings.push(part.text);\n      }\n    }\n  }\n  if (textStrings.length > 0) {\n    return textStrings.join('');\n  } else {\n    return '';\n  }\n}\n\n/**\n * Returns <code>{@link FunctionCall}</code>s associated with first candidate.\n */\nexport function getFunctionCalls(\n  response: GenerateContentResponse\n): FunctionCall[] | undefined {\n  const functionCalls: FunctionCall[] = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.functionCall) {\n        functionCalls.push(part.functionCall);\n      }\n    }\n  }\n  if (functionCalls.length > 0) {\n    return functionCalls;\n  } else {\n    return undefined;\n  }\n}\n\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY];\n\nfunction hadBadFinishReason(candidate: GenerateContentCandidate): boolean {\n  return (\n    !!candidate.finishReason &&\n    badFinishReasons.includes(candidate.finishReason)\n  );\n}\n\nexport function formatBlockErrorMessage(\n  response: GenerateContentResponse\n): string {\n  let message = '';\n  if (\n    (!response.candidates || response.candidates.length === 0) &&\n    response.promptFeedback\n  ) {\n    message += 'Response was blocked';\n    if (response.promptFeedback?.blockReason) {\n      message += ` due to ${response.promptFeedback.blockReason}`;\n    }\n    if (response.promptFeedback?.blockReasonMessage) {\n      message += `: ${response.promptFeedback.blockReasonMessage}`;\n    }\n  } else if (response.candidates?.[0]) {\n    const firstCandidate = response.candidates[0];\n    if (hadBadFinishReason(firstCandidate)) {\n      message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n      if (firstCandidate.finishMessage) {\n        message += `: ${firstCandidate.finishMessage}`;\n      }\n    }\n  }\n  return message;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  GenerateContentStreamResult,\n  Part,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\nimport { createEnhancedContentResponse } from './response-helpers';\n\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nexport function processStream(response: Response): GenerateContentStreamResult {\n  const inputStream = response.body!.pipeThrough(\n    new TextDecoderStream('utf8', { fatal: true })\n  );\n  const responseStream =\n    getResponseStream<GenerateContentResponse>(inputStream);\n  const [stream1, stream2] = responseStream.tee();\n  return {\n    stream: generateResponseSequence(stream1),\n    response: getResponsePromise(stream2)\n  };\n}\n\nasync function getResponsePromise(\n  stream: ReadableStream<GenerateContentResponse>\n): Promise<EnhancedGenerateContentResponse> {\n  const allResponses: GenerateContentResponse[] = [];\n  const reader = stream.getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      const enhancedResponse = createEnhancedContentResponse(\n        aggregateResponses(allResponses)\n      );\n      return enhancedResponse;\n    }\n    allResponses.push(value);\n  }\n}\n\nasync function* generateResponseSequence(\n  stream: ReadableStream<GenerateContentResponse>\n): AsyncGenerator<EnhancedGenerateContentResponse> {\n  const reader = stream.getReader();\n  while (true) {\n    const { value, done } = await reader.read();\n    if (done) {\n      break;\n    }\n\n    const enhancedResponse = createEnhancedContentResponse(value);\n    yield enhancedResponse;\n  }\n}\n\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nexport function getResponseStream<T>(\n  inputStream: ReadableStream<string>\n): ReadableStream<T> {\n  const reader = inputStream.getReader();\n  const stream = new ReadableStream<T>({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      function pump(): Promise<(() => Promise<void>) | undefined> {\n        return reader.read().then(({ value, done }) => {\n          if (done) {\n            if (currentText.trim()) {\n              controller.error(\n                new VertexAIError(\n                  VertexAIErrorCode.PARSE_FAILED,\n                  'Failed to parse stream'\n                )\n              );\n              return;\n            }\n            controller.close();\n            return;\n          }\n\n          currentText += value;\n          let match = currentText.match(responseLineRE);\n          let parsedResponse: T;\n          while (match) {\n            try {\n              parsedResponse = JSON.parse(match[1]);\n            } catch (e) {\n              controller.error(\n                new VertexAIError(\n                  VertexAIErrorCode.PARSE_FAILED,\n                  `Error parsing JSON response: \"${match[1]}`\n                )\n              );\n              return;\n            }\n            controller.enqueue(parsedResponse);\n            currentText = currentText.substring(match[0].length);\n            match = currentText.match(responseLineRE);\n          }\n          return pump();\n        });\n      }\n    }\n  });\n  return stream;\n}\n\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nexport function aggregateResponses(\n  responses: GenerateContentResponse[]\n): GenerateContentResponse {\n  const lastResponse = responses[responses.length - 1];\n  const aggregatedResponse: GenerateContentResponse = {\n    promptFeedback: lastResponse?.promptFeedback\n  };\n  for (const response of responses) {\n    if (response.candidates) {\n      for (const candidate of response.candidates) {\n        // Index will be undefined if it's the first index (0), so we should use 0 if it's undefined.\n        // See: https://github.com/firebase/firebase-js-sdk/issues/8566\n        const i = candidate.index || 0;\n        if (!aggregatedResponse.candidates) {\n          aggregatedResponse.candidates = [];\n        }\n        if (!aggregatedResponse.candidates[i]) {\n          aggregatedResponse.candidates[i] = {\n            index: candidate.index\n          } as GenerateContentCandidate;\n        }\n        // Keep overwriting, the last one will be final\n        aggregatedResponse.candidates[i].citationMetadata =\n          candidate.citationMetadata;\n        aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n        aggregatedResponse.candidates[i].finishMessage =\n          candidate.finishMessage;\n        aggregatedResponse.candidates[i].safetyRatings =\n          candidate.safetyRatings;\n\n        /**\n         * Candidates should always have content and parts, but this handles\n         * possible malformed responses.\n         */\n        if (candidate.content && candidate.content.parts) {\n          if (!aggregatedResponse.candidates[i].content) {\n            aggregatedResponse.candidates[i].content = {\n              role: candidate.content.role || 'user',\n              parts: []\n            };\n          }\n          const newPart: Partial<Part> = {};\n          for (const part of candidate.content.parts) {\n            if (part.text) {\n              newPart.text = part.text;\n            }\n            if (part.functionCall) {\n              newPart.functionCall = part.functionCall;\n            }\n            if (Object.keys(newPart).length === 0) {\n              newPart.text = '';\n            }\n            aggregatedResponse.candidates[i].content.parts.push(\n              newPart as Part\n            );\n          }\n        }\n      }\n    }\n  }\n  return aggregatedResponse;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenerateContentRequest,\n  GenerateContentResponse,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { createEnhancedContentResponse } from '../requests/response-helpers';\nimport { processStream } from '../requests/stream-reader';\nimport { ApiSettings } from '../types/internal';\n\nexport async function generateContentStream(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentStreamResult> {\n  const response = await makeRequest(\n    model,\n    Task.STREAM_GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ true,\n    JSON.stringify(params),\n    requestOptions\n  );\n  return processStream(response);\n}\n\nexport async function generateContent(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentResult> {\n  const response = await makeRequest(\n    model,\n    Task.GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ false,\n    JSON.stringify(params),\n    requestOptions\n  );\n  const responseJson: GenerateContentResponse = await response.json();\n  const enhancedResponse = createEnhancedContentResponse(responseJson);\n  return {\n    response: enhancedResponse\n  };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  GenerateContentRequest,\n  Part,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\n\nexport function formatSystemInstruction(\n  input?: string | Part | Content\n): Content | undefined {\n  // null or undefined\n  if (input == null) {\n    return undefined;\n  } else if (typeof input === 'string') {\n    return { role: 'system', parts: [{ text: input }] } as Content;\n  } else if ((input as Part).text) {\n    return { role: 'system', parts: [input as Part] };\n  } else if ((input as Content).parts) {\n    if (!(input as Content).role) {\n      return { role: 'system', parts: (input as Content).parts };\n    } else {\n      return input as Content;\n    }\n  }\n}\n\nexport function formatNewContent(\n  request: string | Array<string | Part>\n): Content {\n  let newParts: Part[] = [];\n  if (typeof request === 'string') {\n    newParts = [{ text: request }];\n  } else {\n    for (const partOrString of request) {\n      if (typeof partOrString === 'string') {\n        newParts.push({ text: partOrString });\n      } else {\n        newParts.push(partOrString);\n      }\n    }\n  }\n  return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(\n  parts: Part[]\n): Content {\n  const userContent: Content = { role: 'user', parts: [] };\n  const functionContent: Content = { role: 'function', parts: [] };\n  let hasUserContent = false;\n  let hasFunctionContent = false;\n  for (const part of parts) {\n    if ('functionResponse' in part) {\n      functionContent.parts.push(part);\n      hasFunctionContent = true;\n    } else {\n      userContent.parts.push(part);\n      hasUserContent = true;\n    }\n  }\n\n  if (hasUserContent && hasFunctionContent) {\n    throw new VertexAIError(\n      VertexAIErrorCode.INVALID_CONTENT,\n      'Within a single message, FunctionResponse cannot be mixed with other type of Part in the request for sending chat message.'\n    );\n  }\n\n  if (!hasUserContent && !hasFunctionContent) {\n    throw new VertexAIError(\n      VertexAIErrorCode.INVALID_CONTENT,\n      'No Content is provided for sending chat message.'\n    );\n  }\n\n  if (hasUserContent) {\n    return userContent;\n  }\n\n  return functionContent;\n}\n\nexport function formatGenerateContentInput(\n  params: GenerateContentRequest | string | Array<string | Part>\n): GenerateContentRequest {\n  let formattedRequest: GenerateContentRequest;\n  if ((params as GenerateContentRequest).contents) {\n    formattedRequest = params as GenerateContentRequest;\n  } else {\n    // Array or string\n    const content = formatNewContent(params as string | Array<string | Part>);\n    formattedRequest = { contents: [content] };\n  }\n  if ((params as GenerateContentRequest).systemInstruction) {\n    formattedRequest.systemInstruction = formatSystemInstruction(\n      (params as GenerateContentRequest).systemInstruction\n    );\n  }\n  return formattedRequest;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  POSSIBLE_ROLES,\n  Part,\n  Role,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\n\n// https://ai.google.dev/api/rest/v1beta/Content#part\n\nconst VALID_PART_FIELDS: Array<keyof Part> = [\n  'text',\n  'inlineData',\n  'functionCall',\n  'functionResponse'\n];\n\nconst VALID_PARTS_PER_ROLE: { [key in Role]: Array<keyof Part> } = {\n  user: ['text', 'inlineData'],\n  function: ['functionResponse'],\n  model: ['text', 'functionCall'],\n  // System instructions shouldn't be in history anyway.\n  system: ['text']\n};\n\nconst VALID_PREVIOUS_CONTENT_ROLES: { [key in Role]: Role[] } = {\n  user: ['model'],\n  function: ['model'],\n  model: ['user', 'function'],\n  // System instructions shouldn't be in history.\n  system: []\n};\n\nexport function validateChatHistory(history: Content[]): void {\n  let prevContent: Content | null = null;\n  for (const currContent of history) {\n    const { role, parts } = currContent;\n    if (!prevContent && role !== 'user') {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `First Content should be with role 'user', got ${role}`\n      );\n    }\n    if (!POSSIBLE_ROLES.includes(role)) {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(\n          POSSIBLE_ROLES\n        )}`\n      );\n    }\n\n    if (!Array.isArray(parts)) {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `Content should have 'parts' but property with an array of Parts`\n      );\n    }\n\n    if (parts.length === 0) {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `Each Content should have at least one part`\n      );\n    }\n\n    const countFields: Record<keyof Part, number> = {\n      text: 0,\n      inlineData: 0,\n      functionCall: 0,\n      functionResponse: 0\n    };\n\n    for (const part of parts) {\n      for (const key of VALID_PART_FIELDS) {\n        if (key in part) {\n          countFields[key] += 1;\n        }\n      }\n    }\n    const validParts = VALID_PARTS_PER_ROLE[role];\n    for (const key of VALID_PART_FIELDS) {\n      if (!validParts.includes(key) && countFields[key] > 0) {\n        throw new VertexAIError(\n          VertexAIErrorCode.INVALID_CONTENT,\n          `Content with role '${role}' can't contain '${key}' part`\n        );\n      }\n    }\n\n    if (prevContent) {\n      const validPreviousContentRoles = VALID_PREVIOUS_CONTENT_ROLES[role];\n      if (!validPreviousContentRoles.includes(prevContent.role)) {\n        throw new VertexAIError(\n          VertexAIErrorCode.INVALID_CONTENT,\n          `Content with role '${role} can't follow '${\n            prevContent.role\n          }'. Valid previous roles: ${JSON.stringify(\n            VALID_PREVIOUS_CONTENT_ROLES\n          )}`\n        );\n      }\n    }\n    prevContent = currContent;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  Part,\n  RequestOptions,\n  StartChatParams\n} from '../types';\nimport { formatNewContent } from '../requests/request-helpers';\nimport { formatBlockErrorMessage } from '../requests/response-helpers';\nimport { validateChatHistory } from './chat-session-helpers';\nimport { generateContent, generateContentStream } from './generate-content';\nimport { ApiSettings } from '../types/internal';\nimport { logger } from '../logger';\n\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = 'SILENT_ERROR';\n\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nexport class ChatSession {\n  private _apiSettings: ApiSettings;\n  private _history: Content[] = [];\n  private _sendPromise: Promise<void> = Promise.resolve();\n\n  constructor(\n    apiSettings: ApiSettings,\n    public model: string,\n    public params?: StartChatParams,\n    public requestOptions?: RequestOptions\n  ) {\n    this._apiSettings = apiSettings;\n    if (params?.history) {\n      validateChatHistory(params.history);\n      this._history = params.history;\n    }\n  }\n\n  /**\n   * Gets the chat history so far. Blocked prompts are not added to history.\n   * Neither blocked candidates nor the prompts that generated them are added\n   * to history.\n   */\n  async getHistory(): Promise<Content[]> {\n    await this._sendPromise;\n    return this._history;\n  }\n\n  /**\n   * Sends a chat message and receives a non-streaming\n   * <code>{@link GenerateContentResult}</code>\n   */\n  async sendMessage(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    let finalResult = {} as GenerateContentResult;\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() =>\n        generateContent(\n          this._apiSettings,\n          this.model,\n          generateContentRequest,\n          this.requestOptions\n        )\n      )\n      .then(result => {\n        if (\n          result.response.candidates &&\n          result.response.candidates.length > 0\n        ) {\n          this._history.push(newContent);\n          const responseContent: Content = {\n            parts: result.response.candidates?.[0].content.parts || [],\n            // Response seems to come back without a role set.\n            role: result.response.candidates?.[0].content.role || 'model'\n          };\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(result.response);\n          if (blockErrorMessage) {\n            logger.warn(\n              `sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n        finalResult = result;\n      });\n    await this._sendPromise;\n    return finalResult;\n  }\n\n  /**\n   * Sends a chat message and receives the response as a\n   * <code>{@link GenerateContentStreamResult}</code> containing an iterable stream\n   * and a response promise.\n   */\n  async sendMessageStream(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    const streamPromise = generateContentStream(\n      this._apiSettings,\n      this.model,\n      generateContentRequest,\n      this.requestOptions\n    );\n\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() => streamPromise)\n      // This must be handled to avoid unhandled rejection, but jump\n      // to the final catch block with a label to not log this error.\n      .catch(_ignored => {\n        throw new Error(SILENT_ERROR);\n      })\n      .then(streamResult => streamResult.response)\n      .then(response => {\n        if (response.candidates && response.candidates.length > 0) {\n          this._history.push(newContent);\n          const responseContent = { ...response.candidates[0].content };\n          // Response seems to come back without a role set.\n          if (!responseContent.role) {\n            responseContent.role = 'model';\n          }\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(response);\n          if (blockErrorMessage) {\n            logger.warn(\n              `sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n      })\n      .catch(e => {\n        // Errors in streamPromise are already catchable by the user as\n        // streamPromise is returned.\n        // Avoid duplicating the error message in logs.\n        if (e.message !== SILENT_ERROR) {\n          // Users do not have access to _sendPromise to catch errors\n          // downstream from streamPromise, so they should not throw.\n          logger.error(e);\n        }\n      });\n    return streamPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  generateContent,\n  generateContentStream\n} from '../methods/generate-content';\nimport {\n  Content,\n  CountTokensRequest,\n  CountTokensResponse,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  GenerationConfig,\n  ModelParams,\n  Part,\n  RequestOptions,\n  SafetySetting,\n  StartChatParams,\n  Tool,\n  ToolConfig,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\nimport { ChatSession } from '../methods/chat-session';\nimport { countTokens } from '../methods/count-tokens';\nimport {\n  formatGenerateContentInput,\n  formatSystemInstruction\n} from '../requests/request-helpers';\nimport { VertexAI } from '../public-types';\nimport { ApiSettings } from '../types/internal';\nimport { VertexAIService } from '../service';\n\n/**\n * Class for generative model APIs.\n * @public\n */\nexport class GenerativeModel {\n  private _apiSettings: ApiSettings;\n  model: string;\n  generationConfig: GenerationConfig;\n  safetySettings: SafetySetting[];\n  requestOptions?: RequestOptions;\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: Content;\n\n  constructor(\n    vertexAI: VertexAI,\n    modelParams: ModelParams,\n    requestOptions?: RequestOptions\n  ) {\n    if (!vertexAI.app?.options?.apiKey) {\n      throw new VertexAIError(\n        VertexAIErrorCode.NO_API_KEY,\n        `The \"apiKey\" field is empty in the local Firebase config. Firebase VertexAI requires this field to contain a valid API key.`\n      );\n    } else if (!vertexAI.app?.options?.projectId) {\n      throw new VertexAIError(\n        VertexAIErrorCode.NO_PROJECT_ID,\n        `The \"projectId\" field is empty in the local Firebase config. Firebase VertexAI requires this field to contain a valid project ID.`\n      );\n    } else {\n      this._apiSettings = {\n        apiKey: vertexAI.app.options.apiKey,\n        project: vertexAI.app.options.projectId,\n        location: vertexAI.location\n      };\n      if ((vertexAI as VertexAIService).appCheck) {\n        this._apiSettings.getAppCheckToken = () =>\n          (vertexAI as VertexAIService).appCheck!.getToken();\n      }\n\n      if ((vertexAI as VertexAIService).auth) {\n        this._apiSettings.getAuthToken = () =>\n          (vertexAI as VertexAIService).auth!.getToken();\n      }\n    }\n    if (modelParams.model.includes('/')) {\n      if (modelParams.model.startsWith('models/')) {\n        // Add \"publishers/google\" if the user is only passing in 'models/model-name'.\n        this.model = `publishers/google/${modelParams.model}`;\n      } else {\n        // Any other custom format (e.g. tuned models) must be passed in correctly.\n        this.model = modelParams.model;\n      }\n    } else {\n      // If path is not included, assume it's a non-tuned model.\n      this.model = `publishers/google/models/${modelParams.model}`;\n    }\n    this.generationConfig = modelParams.generationConfig || {};\n    this.safetySettings = modelParams.safetySettings || [];\n    this.tools = modelParams.tools;\n    this.toolConfig = modelParams.toolConfig;\n    this.systemInstruction = formatSystemInstruction(\n      modelParams.systemInstruction\n    );\n    this.requestOptions = requestOptions || {};\n  }\n\n  /**\n   * Makes a single non-streaming call to the model\n   * and returns an object containing a single <code>{@link GenerateContentResponse}</code>.\n   */\n  async generateContent(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContent(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Makes a single streaming call to the model\n   * and returns an object containing an iterable stream that iterates\n   * over all chunks in the streaming response as well as\n   * a promise that returns the final aggregated response.\n   */\n  async generateContentStream(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContentStream(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Gets a new <code>{@link ChatSession}</code> instance which can be used for\n   * multi-turn chats.\n   */\n  startChat(startChatParams?: StartChatParams): ChatSession {\n    return new ChatSession(\n      this._apiSettings,\n      this.model,\n      {\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...startChatParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Counts the tokens in the provided request.\n   */\n  async countTokens(\n    request: CountTokensRequest | string | Array<string | Part>\n  ): Promise<CountTokensResponse> {\n    const formattedParams = formatGenerateContentInput(request);\n    return countTokens(this._apiSettings, this.model, formattedParams);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CountTokensRequest,\n  CountTokensResponse,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { ApiSettings } from '../types/internal';\n\nexport async function countTokens(\n  apiSettings: ApiSettings,\n  model: string,\n  params: CountTokensRequest,\n  requestOptions?: RequestOptions\n): Promise<CountTokensResponse> {\n  const response = await makeRequest(\n    model,\n    Task.COUNT_TOKENS,\n    apiSettings,\n    false,\n    JSON.stringify(params),\n    requestOptions\n  );\n  return response.json();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VertexAIError } from '../errors';\nimport { VertexAIErrorCode } from '../types';\nimport {\n  SchemaInterface,\n  SchemaType,\n  SchemaParams,\n  SchemaRequest,\n  ObjectSchemaInterface\n} from '../types/schema';\n\n/**\n * Parent class encompassing all Schema types, with static methods that\n * allow building specific Schema types. This class can be converted with\n * `JSON.stringify()` into a JSON string accepted by Vertex AI REST endpoints.\n * (This string conversion is automatically done when calling SDK methods.)\n * @public\n */\nexport abstract class Schema implements SchemaInterface {\n  /**\n   * Optional. The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n  /** Optional. The format of the property.\n   * Supported formats:<br/>\n   * <ul>\n   *  <li>for NUMBER type: \"float\", \"double\"</li>\n   *  <li>for INTEGER type: \"int32\", \"int64\"</li>\n   *  <li>for STRING type: \"email\", \"byte\", etc</li>\n   * </ul>\n   */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /** Optional. Whether the property is nullable. Defaults to false. */\n  nullable: boolean;\n  /** Optional. The example of the property. */\n  example?: unknown;\n  /**\n   * Allows user to add other schema properties that have not yet\n   * been officially added to the SDK.\n   */\n  [key: string]: unknown;\n\n  constructor(schemaParams: SchemaInterface) {\n    // eslint-disable-next-line guard-for-in\n    for (const paramKey in schemaParams) {\n      this[paramKey] = schemaParams[paramKey];\n    }\n    // Ensure these are explicitly set to avoid TS errors.\n    this.type = schemaParams.type;\n    this.nullable = schemaParams.hasOwnProperty('nullable')\n      ? !!schemaParams.nullable\n      : false;\n  }\n\n  /**\n   * Defines how this Schema should be serialized as JSON.\n   * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#tojson_behavior\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj: { type: SchemaType; [key: string]: unknown } = {\n      type: this.type\n    };\n    for (const prop in this) {\n      if (this.hasOwnProperty(prop) && this[prop] !== undefined) {\n        if (prop !== 'required' || this.type === SchemaType.OBJECT) {\n          obj[prop] = this[prop];\n        }\n      }\n    }\n    return obj as SchemaRequest;\n  }\n\n  static array(arrayParams: SchemaParams & { items: Schema }): ArraySchema {\n    return new ArraySchema(arrayParams, arrayParams.items);\n  }\n\n  static object(\n    objectParams: SchemaParams & {\n      properties: {\n        [k: string]: Schema;\n      };\n      optionalProperties?: string[];\n    }\n  ): ObjectSchema {\n    return new ObjectSchema(\n      objectParams,\n      objectParams.properties,\n      objectParams.optionalProperties\n    );\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static string(stringParams?: SchemaParams): StringSchema {\n    return new StringSchema(stringParams);\n  }\n\n  static enumString(\n    stringParams: SchemaParams & { enum: string[] }\n  ): StringSchema {\n    return new StringSchema(stringParams, stringParams.enum);\n  }\n\n  static integer(integerParams?: SchemaParams): IntegerSchema {\n    return new IntegerSchema(integerParams);\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static number(numberParams?: SchemaParams): NumberSchema {\n    return new NumberSchema(numberParams);\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static boolean(booleanParams?: SchemaParams): BooleanSchema {\n    return new BooleanSchema(booleanParams);\n  }\n}\n\n/**\n * A type that includes all specific Schema types.\n * @public\n */\nexport type TypedSchema =\n  | IntegerSchema\n  | NumberSchema\n  | StringSchema\n  | BooleanSchema\n  | ObjectSchema\n  | ArraySchema;\n\n/**\n * Schema class for \"integer\" types.\n * @public\n */\nexport class IntegerSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.INTEGER,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"number\" types.\n * @public\n */\nexport class NumberSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.NUMBER,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"boolean\" types.\n * @public\n */\nexport class BooleanSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.BOOLEAN,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"string\" types. Can be used with or without\n * enum values.\n * @public\n */\nexport class StringSchema extends Schema {\n  enum?: string[];\n  constructor(schemaParams?: SchemaParams, enumValues?: string[]) {\n    super({\n      type: SchemaType.STRING,\n      ...schemaParams\n    });\n    this.enum = enumValues;\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    if (this.enum) {\n      obj['enum'] = this.enum;\n    }\n    return obj as SchemaRequest;\n  }\n}\n\n/**\n * Schema class for \"array\" types.\n * The `items` param should refer to the type of item that can be a member\n * of the array.\n * @public\n */\nexport class ArraySchema extends Schema {\n  constructor(schemaParams: SchemaParams, public items: TypedSchema) {\n    super({\n      type: SchemaType.ARRAY,\n      ...schemaParams\n    });\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    obj.items = this.items.toJSON();\n    return obj;\n  }\n}\n\n/**\n * Schema class for \"object\" types.\n * The `properties` param must be a map of `Schema` objects.\n * @public\n */\nexport class ObjectSchema extends Schema {\n  constructor(\n    schemaParams: SchemaParams,\n    public properties: {\n      [k: string]: TypedSchema;\n    },\n    public optionalProperties: string[] = []\n  ) {\n    super({\n      type: SchemaType.OBJECT,\n      ...schemaParams\n    });\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    obj.properties = { ...this.properties };\n    const required = [];\n    if (this.optionalProperties) {\n      for (const propertyKey of this.optionalProperties) {\n        if (!this.properties.hasOwnProperty(propertyKey)) {\n          throw new VertexAIError(\n            VertexAIErrorCode.INVALID_SCHEMA,\n            `Property \"${propertyKey}\" specified in \"optionalProperties\" does not exist.`\n          );\n        }\n      }\n    }\n    for (const propertyKey in this.properties) {\n      if (this.properties.hasOwnProperty(propertyKey)) {\n        obj.properties[propertyKey] = this.properties[\n          propertyKey\n        ].toJSON() as SchemaRequest;\n        if (!this.optionalProperties.includes(propertyKey)) {\n          required.push(propertyKey);\n        }\n      }\n    }\n    if (required.length > 0) {\n      obj.required = required;\n    }\n    delete (obj as ObjectSchemaInterface).optionalProperties;\n    return obj as SchemaRequest;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Provider } from '@firebase/component';\nimport { getModularInstance } from '@firebase/util';\nimport { DEFAULT_LOCATION, VERTEX_TYPE } from './constants';\nimport { VertexAIService } from './service';\nimport { VertexAI, VertexAIOptions } from './public-types';\nimport { ModelParams, RequestOptions, VertexAIErrorCode } from './types';\nimport { VertexAIError } from './errors';\nimport { GenerativeModel } from './models/generative-model';\n\nexport { ChatSession } from './methods/chat-session';\nexport * from './requests/schema-builder';\n\nexport { GenerativeModel };\n\nexport { VertexAIError };\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [VERTEX_TYPE]: VertexAIService;\n  }\n}\n\n/**\n * Returns a <code>{@link VertexAI}</code> instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getVertexAI(\n  app: FirebaseApp = getApp(),\n  options?: VertexAIOptions\n): VertexAI {\n  app = getModularInstance(app);\n  // Dependencies\n  const vertexProvider: Provider<'vertexAI'> = _getProvider(app, VERTEX_TYPE);\n\n  return vertexProvider.getImmediate({\n    identifier: options?.location || DEFAULT_LOCATION\n  });\n}\n\n/**\n * Returns a <code>{@link GenerativeModel}</code> class with methods for inference\n * and other functionality.\n *\n * @public\n */\nexport function getGenerativeModel(\n  vertexAI: VertexAI,\n  modelParams: ModelParams,\n  requestOptions?: RequestOptions\n): GenerativeModel {\n  if (!modelParams.model) {\n    throw new VertexAIError(\n      VertexAIErrorCode.NO_MODEL,\n      `Must provide a model name. Example: getGenerativeModel({ model: 'my-model-name' })`\n    );\n  }\n  return new GenerativeModel(vertexAI, modelParams, requestOptions);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * The Vertex AI in Firebase Web SDK.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { VertexAIService } from './service';\nimport { VERTEX_TYPE } from './constants';\nimport { Component, ComponentType } from '@firebase/component';\nimport { name, version } from '../package.json';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerVertex(): void {\n  _registerComponent(\n    new Component(\n      VERTEX_TYPE,\n      (container, { instanceIdentifier: location }) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const auth = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        return new VertexAIService(app, auth, appCheckProvider, { location });\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterVertex();\n\nexport * from './api';\nexport * from './public-types';\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "__await", "v", "__asyncGenerator", "thisArg", "_arguments", "generator", "Symbol", "asyncIterator", "TypeError", "i", "g", "apply", "q", "verb", "n", "Promise", "a", "b", "push", "resume", "step", "r", "resolve", "then", "fulfill", "reject", "settle", "e", "f", "shift", "length", "VertexAIService", "app", "authProvider", "appCheckProvider", "options", "appCheck", "getImmediate", "optional", "auth", "location", "_a", "_delete", "VertexAIError", "customErrorData", "toString", "logger", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "Task", "RequestUrl", "model", "task", "apiSettings", "stream", "requestOptions", "url", "baseUrl", "project", "fullModelString", "modelString", "async", "getHeaders", "headers", "Headers", "append", "getClientHeaders", "loggingTags", "join", "<PERSON><PERSON><PERSON><PERSON>", "getAppCheckToken", "appCheckToken", "token", "getAuthToken", "authToken", "accessToken", "makeRequest", "body", "response", "fetchTimeoutId", "request", "constructRequest", "fetchOptions", "timeout<PERSON><PERSON><PERSON>", "timeout", "abortController", "AbortController", "setTimeout", "abort", "signal", "fetch", "ok", "errorDetails", "json", "details", "JSON", "stringify", "status", "some", "detail", "reason", "_b", "links", "description", "includes", "statusText", "err", "stack", "clearTimeout", "POSSIBLE_ROLES", "HarmCategory", "HarmBlockThreshold", "HarmBlockMethod", "HarmProbability", "HarmSeverity", "BlockReason", "FinishReason", "FunctionCallingMode", "SchemaType", "createEnhancedContentResponse", "candidates", "hasOwnProperty", "index", "responseWithHelpers", "addHelpers", "text", "hadBadFinishReason", "formatBlockErrorMessage", "getText", "textStrings", "content", "parts", "part", "_d", "_c", "promptFeedback", "functionCalls", "getFunctionCalls", "functionCall", "badFinishReasons", "RECITATION", "SAFETY", "candidate", "finishReason", "firstCandidate", "finishMessage", "blockReason", "blockReasonMessage", "responseLineRE", "processStream", "responseStream", "getResponseStream", "inputStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "start", "controller", "currentText", "pump", "read", "done", "trim", "close", "parsedResponse", "match", "parse", "enqueue", "substring", "pipeThrough", "TextDecoderStream", "fatal", "stream1", "stream2", "tee", "generateResponseSequence", "getResponsePromise", "allResponses", "aggregateResponses", "enhancedResponse", "responses", "lastResponse", "aggregatedResponse", "citationMetadata", "safetyRatings", "role", "newPart", "keys", "generateContentStream", "params", "STREAM_GENERATE_CONTENT", "generateContent", "GENERATE_CONTENT", "formatSystemInstruction", "input", "formatNewContent", "newParts", "partOrString", "assignRoleToPartsAndValidateSendMessageRequest", "userContent", "functionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasFunctionContent", "formatGenerateContentInput", "formattedRequest", "contents", "systemInstruction", "VALID_PART_FIELDS", "VALID_PARTS_PER_ROLE", "user", "function", "system", "VALID_PREVIOUS_CONTENT_ROLES", "ChatSession", "_history", "_sendPromise", "_apiSettings", "history", "validateChatHistory", "prevContent", "currContent", "Array", "isArray", "countFields", "inlineData", "functionResponse", "validParts", "newContent", "generateContentRequest", "safetySettings", "generationConfig", "tools", "toolConfig", "_e", "finalResult", "result", "responseContent", "blockErrorMessage", "streamPromise", "catch", "_ignored", "streamResult", "assign", "GenerativeModel", "vertexAI", "modelParams", "projectId", "getToken", "startsWith", "formattedParams", "startChat", "startChatParams", "countTokens", "COUNT_TOKENS", "<PERSON><PERSON><PERSON>", "schemaParams", "<PERSON><PERSON><PERSON><PERSON>", "nullable", "toJSON", "obj", "prop", "undefined", "OBJECT", "static", "arrayParams", "ArraySchema", "items", "objectParams", "ObjectSchema", "properties", "optionalProperties", "stringParams", "StringSchema", "enum", "integerParams", "IntegerSchema", "numberParams", "NumberSchema", "booleanParams", "BooleanSchema", "INTEGER", "NUMBER", "BOOLEAN", "enum<PERSON><PERSON><PERSON>", "STRING", "ARRAY", "required", "propertyKey", "getVertexAI", "getApp", "getModularInstance", "_delegate", "_get<PERSON><PERSON><PERSON>", "identifier", "getGenerativeModel", "registerVertex", "_registerComponent", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "registerVersion"], "mappings": "iGAyEM,MAAOA,sBAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,SAK9C,MAAAD,aAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,EAGnBH,OACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,GACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,SAbhBJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,IAa3D,MAAMkB,EAAU,gBC3GH,MAAAM,UAiBX3B,YACWM,EACAsB,EACAC,GAFAxB,KAAIC,KAAJA,EACAD,KAAeuB,gBAAfA,EACAvB,KAAIwB,KAAJA,EAnBXxB,KAAiByB,mBAAG,EAIpBzB,KAAY0B,aAAe,GAE3B1B,KAAA2B,kBAA2C,OAE3C3B,KAAiB4B,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADA9B,KAAK2B,kBAAoBG,EAClB9B,KAGT+B,qBAAqBN,GAEnB,OADAzB,KAAKyB,kBAAoBA,EAClBzB,KAGTgC,gBAAgBC,GAEd,OADAjC,KAAK0B,aAAeO,EACbjC,KAGTkC,2BAA2BC,GAEzB,OADAnC,KAAK4B,kBAAoBO,EAClBnC,UCdCoC,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SANF,CAAYA,IAAAA,EAOX,KAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAIlE,MACR,8DAA8D4D,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAASpD,WACnBsD,IC0DH,SAAUO,QAAQC,GACpB,OAAO/D,gBAAgB8D,SAAW9D,KAAK+D,EAAIA,EAAG/D,MAAQ,IAAI8D,QAAQC,YAGtDC,iBAAiBC,EAASC,EAAYC,GAClD,IAAKC,OAAOC,cAAe,MAAM,IAAIC,UAAU,wCAC/C,IAAoDC,EAAhDC,EAAIL,EAAUM,MAAMR,EAASC,GAAc,IAAQQ,EAAI,GAC3D,OAAOH,EAAI,GAAII,KAAK,QAASA,KAAK,SAAUA,KAAK,UAAWJ,EAAEH,OAAOC,eAAiB,WAAc,OAAOrE,MAASuE,EACpH,SAASI,KAAKC,GAASJ,EAAEI,KAAIL,EAAEK,GAAK,SAAUb,GAAK,OAAO,IAAIc,SAAQ,SAAUC,EAAGC,GAAKL,EAAEM,KAAK,CAACJ,EAAGb,EAAGe,EAAGC,IAAM,GAAKE,OAAOL,EAAGb,QAC9H,SAASkB,OAAOL,EAAGb,GAAK,KACxB,SAASmB,KAAKC,GAAKA,EAAEhE,iBAAiB2C,QAAUe,QAAQO,QAAQD,EAAEhE,MAAM4C,GAAGsB,KAAKC,QAASC,QAAUC,OAAOd,EAAE,GAAG,GAAIS,GADrFD,CAAKV,EAAEI,GAAGb,IAAO,MAAO0B,GAAKD,OAAOd,EAAE,GAAG,GAAIe,IAE3E,SAASH,QAAQnE,GAAS8D,OAAO,OAAQ9D,GACzC,SAASoE,OAAOpE,GAAS8D,OAAO,QAAS9D,GACzC,SAASqE,OAAOE,EAAG3B,GAAS2B,EAAE3B,GAAIW,EAAEiB,QAASjB,EAAEkB,QAAQX,OAAOP,EAAE,GAAG,GAAIA,EAAE,GAAG,gCC7JnE,MAAAmB,gBAKXlG,YACSmG,EACPC,EACAC,EACOC,SAHAjG,KAAG8F,IAAHA,EAGA9F,KAAOiG,QAAPA,EAEP,MAAMC,EAAWF,MAAAA,OAAgB,EAAhBA,EAAkBG,aAAa,CAAEC,UAAU,IACtDC,EAAON,MAAAA,OAAY,EAAZA,EAAcI,aAAa,CAAEC,UAAU,IACpDpG,KAAKqG,KAAOA,GAAQ,KACpBrG,KAAKkG,SAAWA,GAAY,KAC5BlG,KAAKsG,UAAyB,QAAdC,EAAAvG,KAAKiG,eAAS,IAAAM,OAAA,EAAAA,EAAAD,WCxBF,cD2B9BE,UACE,OAAO3B,QAAQO,WEvBb,MAAOqB,sBAAsBhH,cAQjCE,YACWC,EACTC,EACS6G,GAGT,MAGMrF,EAAc,aAAmBxB,MADtB,YAAcD,OAE/BG,MAAMH,EAAMyB,GATHrB,KAAIJ,KAAJA,EAEAI,KAAe0G,gBAAfA,EAaLhH,MAAMW,mBAGRX,MAAMW,kBAAkBL,KAAMyG,eAOhCvG,OAAOC,eAAeH,KAAMyG,cAAcrG,WAG1CJ,KAAK2G,SAAW,IAAMtF,GC5CnB,MAAMuF,EAAS,IL0GT,MAAAC,OAOXlH,YAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAAS8G,UAAG5D,EAsBZlD,KAAW+G,YAAe3D,kBAc1BpD,KAAegH,gBAAsB,KAlCzCxD,eACF,OAAOxD,KAAK8G,UAGVtD,aAASyD,GACX,KAAMA,KAAO7E,GACX,MAAM,IAAIkC,UAAU,kBAAkB2C,+BAExCjH,KAAK8G,UAAYG,EAInBC,YAAYD,GACVjH,KAAK8G,UAA2B,iBAARG,EAAmB5E,EAAkB4E,GAAOA,EAQlEE,iBACF,OAAOnH,KAAK+G,YAEVI,eAAWF,GACb,GAAmB,mBAARA,EACT,MAAM,IAAI3C,UAAU,qDAEtBtE,KAAK+G,YAAcE,EAOjBG,qBACF,OAAOpH,KAAKgH,gBAEVI,mBAAeH,GACjBjH,KAAKgH,gBAAkBC,EAOzB3E,SAASiB,GACPvD,KAAKgH,iBAAmBhH,KAAKgH,gBAAgBhH,KAAMoC,EAASG,SAAUgB,GACtEvD,KAAK+G,YAAY/G,KAAMoC,EAASG,SAAUgB,GAE5C8D,OAAO9D,GACLvD,KAAKgH,iBACHhH,KAAKgH,gBAAgBhH,KAAMoC,EAASK,WAAYc,GAClDvD,KAAK+G,YAAY/G,KAAMoC,EAASK,WAAYc,GAE9Cb,QAAQa,GACNvD,KAAKgH,iBAAmBhH,KAAKgH,gBAAgBhH,KAAMoC,EAASO,QAASY,GACrEvD,KAAK+G,YAAY/G,KAAMoC,EAASO,QAASY,GAE3CX,QAAQW,GACNvD,KAAKgH,iBAAmBhH,KAAKgH,gBAAgBhH,KAAMoC,EAASS,QAASU,GACrEvD,KAAK+G,YAAY/G,KAAMoC,EAASS,QAASU,GAE3CT,SAASS,GACPvD,KAAKgH,iBAAmBhH,KAAKgH,gBAAgBhH,KAAMoC,EAASW,SAAUQ,GACtEvD,KAAK+G,YAAY/G,KAAMoC,EAASW,SAAUQ,KK9Lb,sBCUjC,IAAY+D,GAAZ,SAAYA,GACVA,EAAA,iBAAA,kBACAA,EAAA,wBAAA,wBACAA,EAAA,aAAA,cAHF,CAAYA,IAAAA,EAIX,KAEY,MAAAC,WACX5H,YACS6H,EACAC,EACAC,EACAC,EACAC,GAJA5H,KAAKwH,MAALA,EACAxH,KAAIyH,KAAJA,EACAzH,KAAW0H,YAAXA,EACA1H,KAAM2H,OAANA,EACA3H,KAAc4H,eAAdA,EAETjB,iBAIE,IAAIkB,EAAM,IAD2B,QAArBtB,EAAAvG,KAAK4H,sBAAgB,IAAArB,OAAA,EAAAA,EAAAuB,UHvBT,mDGgC5B,OAPAD,GAAO,aAAa7H,KAAK0H,YAAYK,UACrCF,GAAO,cAAc7H,KAAK0H,YAAYpB,WACtCuB,GAAO,IAAI7H,KAAKwH,QAChBK,GAAO,IAAI7H,KAAKyH,OACZzH,KAAK2H,SACPE,GAAO,YAEFA,EAOLG,sBACF,IAAIC,EAAc,YAAYjI,KAAK0H,YAAYK,UAG/C,OAFAE,GAAe,cAAcjI,KAAK0H,YAAYpB,WAC9C2B,GAAe,IAAIjI,KAAKwH,QACjBS,GAcJC,eAAeC,WAAWN,GAC/B,MAAMO,EAAU,IAAIC,QAIpB,GAHAD,EAAQE,OAAO,eAAgB,oBAC/BF,EAAQE,OAAO,oBAVjB,SAASC,mBACP,MAAMC,EAAc,GAGpB,OAFAA,EAAYxD,KAAK,eACjBwD,EAAYxD,KAAK,cACVwD,EAAYC,KAAK,KAMYF,IACpCH,EAAQE,OAAO,iBAAkBT,EAAIH,YAAYgB,QAC7Cb,EAAIH,YAAYiB,iBAAkB,CACpC,MAAMC,QAAsBf,EAAIH,YAAYiB,mBACxCC,IACFR,EAAQE,OAAO,sBAAuBM,EAAcC,OAChDD,EAAc9F,OAChB8D,EAAOhE,KACL,6CAA6CgG,EAAc9F,MAAMjD,YAMzE,GAAIgI,EAAIH,YAAYoB,aAAc,CAChC,MAAMC,QAAkBlB,EAAIH,YAAYoB,eACpCC,GACFX,EAAQE,OAAO,gBAAiB,YAAYS,EAAUC,eAI1D,OAAOZ,EAsBFF,eAAee,YACpBzB,EACAC,EACAC,EACAC,EACAuB,EACAtB,GAEA,MAAMC,EAAM,IAAIN,WAAWC,EAAOC,EAAMC,EAAaC,EAAQC,GAC7D,IAAIuB,EACAC,EACJ,IACE,MAAMC,QA/BHnB,eAAeoB,iBACpB9B,EACAC,EACAC,EACAC,EACAuB,EACAtB,GAEA,MAAMC,EAAM,IAAIN,WAAWC,EAAOC,EAAMC,EAAaC,EAAQC,GAC7D,MAAO,CACLC,IAAKA,EAAIlB,WACT4C,aAAc,CACZ3F,OAAQ,OACRwE,cAAeD,WAAWN,GAC1BqB,KAAAA,IAiBoBI,CACpB9B,EACAC,EACAC,EACAC,EACAuB,EACAtB,GAGI4B,EACuB,OAA3B5B,MAAAA,OAAA,EAAAA,EAAgB6B,UAAmB7B,EAAe6B,SAAW,EACzD7B,EAAe6B,QHtHe,KGwH9BC,EAAkB,IAAIC,gBAK5B,GAJAP,EAAiBQ,YAAW,IAAMF,EAAgBG,SAASL,GAC3DH,EAAQE,aAAaO,OAASJ,EAAgBI,OAE9CX,QAAiBY,MAAMV,EAAQxB,IAAKwB,EAAQE,eACvCJ,EAASa,GAAI,CAChB,IACIC,EADApK,EAAU,GAEd,IACE,MAAMqK,QAAaf,EAASe,OAC5BrK,EAAUqK,EAAKpH,MAAMjD,QACjBqK,EAAKpH,MAAMqH,UACbtK,GAAW,IAAIuK,KAAKC,UAAUH,EAAKpH,MAAMqH,WACzCF,EAAeC,EAAKpH,MAAMqH,SAE5B,MAAO1E,IAGT,GACsB,MAApB0D,EAASmB,QACTL,EAAaM,MACVC,GAA2C,qBAAlBA,EAAOC,UAEnCR,EAAaM,MAAMC,YACjB,OAEM,QAFNE,EAEI,QAFJnE,EACEiE,EAAOG,aACL,IAAApE,OAAA,EAAAA,EAAA,UAAE,IAAAmE,OAAA,EAAAA,EAAEE,YAAYC,SAClB,+CAIJ,MAAM,IAAIpE,cAAa,kBAKnB,oPAAkDoB,EAAIH,YAAYK,6JAIpE,CACEuC,OAAQnB,EAASmB,OACjBQ,WAAY3B,EAAS2B,WACrBb,aAAAA,IAIN,MAAM,IAAIxD,cAAa,cAErB,uBAAuBoB,OAASsB,EAASmB,UAAUnB,EAAS2B,eAAejL,IAC3E,CACEyK,OAAQnB,EAASmB,OACjBQ,WAAY3B,EAAS2B,WACrBb,aAAAA,KAIN,MAAOxE,GACP,IAAIsF,EAAMtF,EAaV,KAX6D,gBAA1DA,EAAoB7F,MAC0C,oBAA9D6F,EAAoB7F,MACrB6F,aAAa/F,QAEbqL,EAAM,IAAItE,cAAa,QAErB,uBAAuBoB,EAAIlB,eAAelB,EAAE5F,WAE9CkL,EAAIC,MAAQvF,EAAEuF,OAGVD,EACE,QACJ3B,GACF6B,aAAa7B,GAGjB,OAAOD,ECzMI,MAAA+B,EAAiB,CAAC,OAAQ,QAAS,WAAY,cAMhDC,EAWAC,EAcAC,EAWAC,EAeAC,EAeAC,EAWAC,EAgBAC,ECvGAC,ECSN,SAAUC,8BACdzC,GAQIA,EAAS0C,aAAe1C,EAAS0C,WAAW,GAAGC,eAAe,WAChE3C,EAAS0C,WAAW,GAAGE,MAAQ,GAGjC,MAAMC,EAQF,SAAUC,WACd9C,GAkEA,OAhECA,EAA6C+C,KAAO,KACnD,GAAI/C,EAAS0C,YAAc1C,EAAS0C,WAAWjG,OAAS,EAAG,CAQzD,GAPIuD,EAAS0C,WAAWjG,OAAS,GAC/BgB,EAAOhE,KACL,qBAAqBuG,EAAS0C,WAAWjG,qIAKzCuG,mBAAmBhD,EAAS0C,WAAW,IACzC,MAAM,IAAIpF,cAER,iBAAA,mBAAmB2F,wBACjBjD,6CAEF,CACEA,SAAAA,IAIN,OAkDA,SAAUkD,QAAQlD,eACtB,MAAMmD,EAAc,GACpB,GAAoC,QAAhC5B,EAAmB,QAAnBnE,EAAA4C,EAAS0C,kBAAU,IAAAtF,OAAA,EAAAA,EAAG,GAAGgG,eAAO,IAAA7B,OAAA,EAAAA,EAAE8B,MACpC,IAAK,MAAMC,KAA0C,QAAlCC,EAAmB,QAAnBC,EAAAxD,EAAS0C,kBAAU,IAAAc,OAAA,EAAAA,EAAG,GAAGJ,eAAS,IAAAG,OAAA,EAAAA,EAAAF,MAC/CC,EAAKP,MACPI,EAAYtH,KAAKyH,EAAKP,MAI5B,OAAII,EAAY1G,OAAS,EAChB0G,EAAY7D,KAAK,IAEjB,GA9DE4D,CAAQlD,GACV,GAAIA,EAASyD,eAClB,MAAM,IAAInG,cAER,iBAAA,uBAAuB2F,wBAAwBjD,KAC/C,CACEA,SAAAA,IAIN,MAAO,IAERA,EAA6C0D,cAAgB,KAC5D,GAAI1D,EAAS0C,YAAc1C,EAAS0C,WAAWjG,OAAS,EAAG,CAQzD,GAPIuD,EAAS0C,WAAWjG,OAAS,GAC/BgB,EAAOhE,KACL,qBAAqBuG,EAAS0C,WAAWjG,+IAKzCuG,mBAAmBhD,EAAS0C,WAAW,IACzC,MAAM,IAAIpF,cAER,iBAAA,mBAAmB2F,wBACjBjD,6CAEF,CACEA,SAAAA,IAIN,OAqCA,SAAU2D,iBACd3D,eAEA,MAAM0D,EAAgC,GACtC,GAAoC,QAAhCnC,EAAmB,QAAnBnE,EAAA4C,EAAS0C,kBAAU,IAAAtF,OAAA,EAAAA,EAAG,GAAGgG,eAAO,IAAA7B,OAAA,EAAAA,EAAE8B,MACpC,IAAK,MAAMC,KAA0C,QAAlCC,EAAmB,QAAnBC,EAAAxD,EAAS0C,kBAAU,IAAAc,OAAA,EAAAA,EAAG,GAAGJ,eAAS,IAAAG,OAAA,EAAAA,EAAAF,MAC/CC,EAAKM,cACPF,EAAc7H,KAAKyH,EAAKM,cAI9B,OAAIF,EAAcjH,OAAS,EAClBiH,OAEP,EAnDSC,CAAiB3D,GACnB,GAAIA,EAASyD,eAClB,MAAM,IAAInG,cAER,iBAAA,gCAAgC2F,wBAAwBjD,KACxD,CACEA,SAAAA,KAMDA,EA3EqB8C,CAAW9C,GACvC,OAAO6C,GFbT,SAAYb,GACVA,EAAA,0BAAA,4BACAA,EAAA,gCAAA,kCACAA,EAAA,yBAAA,2BACAA,EAAA,gCAAA,kCAJF,CAAYA,IAAAA,EAKX,KAMD,SAAYC,GAEVA,EAAA,oBAAA,sBAEAA,EAAA,uBAAA,yBAEAA,EAAA,gBAAA,kBAEAA,EAAA,WAAA,aARF,CAAYA,IAAAA,EASX,KAKD,SAAYC,GAEVA,EAAA,SAAA,WAEAA,EAAA,YAAA,cAJF,CAAYA,IAAAA,EAKX,KAMD,SAAYC,GAEVA,EAAA,WAAA,aAEAA,EAAA,IAAA,MAEAA,EAAA,OAAA,SAEAA,EAAA,KAAA,OARF,CAAYA,IAAAA,EASX,KAMD,SAAYC,GAEVA,EAAA,yBAAA,2BAEAA,EAAA,kBAAA,oBAEAA,EAAA,qBAAA,uBAEAA,EAAA,mBAAA,qBARF,CAAYA,IAAAA,EASX,KAMD,SAAYC,GAEVA,EAAA,OAAA,SAEAA,EAAA,MAAA,QAJF,CAAYA,IAAAA,EAKX,KAMD,SAAYC,GAEVA,EAAA,KAAA,OAEAA,EAAA,WAAA,aAEAA,EAAA,OAAA,SAEAA,EAAA,WAAA,aAEAA,EAAA,MAAA,QAVF,CAAYA,IAAAA,EAWX,KAKD,SAAYC,GAGVA,EAAA,KAAA,OAKAA,EAAA,IAAA,MAGAA,EAAA,KAAA,OAXF,CAAYA,IAAAA,EAYX,KCnHD,SAAYC,GAEVA,EAAA,OAAA,SAEAA,EAAA,OAAA,SAEAA,EAAA,QAAA,UAEAA,EAAA,QAAA,UAEAA,EAAA,MAAA,QAEAA,EAAA,OAAA,SAZF,CAAYA,IAAAA,EAaX,KC+HD,MAAMqB,EAAmB,CAACvB,EAAawB,WAAYxB,EAAayB,QAEhE,SAASf,mBAAmBgB,GAC1B,QACIA,EAAUC,cACZJ,EAAiBnC,SAASsC,EAAUC,cAIlC,SAAUhB,wBACdjD,aAEA,IAAItJ,EAAU,GACd,GACIsJ,EAAS0C,YAA6C,IAA/B1C,EAAS0C,WAAWjG,SAC7CuD,EAASyD,gBASJ,GAAuB,QAAnBD,EAAAxD,EAAS0C,kBAAU,IAAAc,OAAA,EAAAA,EAAG,GAAI,CACnC,MAAMU,EAAiBlE,EAAS0C,WAAW,GACvCM,mBAAmBkB,KACrBxN,GAAW,gCAAgCwN,EAAeD,eACtDC,EAAeC,gBACjBzN,GAAW,KAAKwN,EAAeC,wBAZnCzN,GAAW,wBACkB,QAAzB0G,EAAA4C,EAASyD,sBAAgB,IAAArG,OAAA,EAAAA,EAAAgH,eAC3B1N,GAAW,WAAWsJ,EAASyD,eAAeW,gBAEnB,QAAzB7C,EAAAvB,EAASyD,sBAAgB,IAAAlC,OAAA,EAAAA,EAAA8C,sBAC3B3N,GAAW,KAAKsJ,EAASyD,eAAeY,sBAW5C,OAAO3N,ECxKT,MAAM4N,EAAiB,qCAUjB,SAAUC,cAAcvE,GAC5B,MAGMwE,EA8CF,SAAUC,kBACdC,GAEA,MAAMC,EAASD,EAAYE,YA6C3B,OA5Ce,IAAIC,eAAkB,CACnCC,MAAMC,GACJ,IAAIC,EAAc,GAClB,OAAOC,OACP,SAASA,OACP,OAAON,EAAOO,OAAOhJ,MAAK,EAAGlE,MAAAA,EAAOmN,KAAAA,MAClC,GAAIA,EACF,OAAIH,EAAYI,YACdL,EAAWpL,MACT,IAAI2D,cAEF,eAAA,gCAKNyH,EAAWM,QAIbL,GAAehN,EACf,IACIsN,EADAC,EAAQP,EAAYO,MAAMjB,GAE9B,KAAOiB,GAAO,CACZ,IACED,EAAiBrE,KAAKuE,MAAMD,EAAM,IAClC,MAAOjJ,GAOP,YANAyI,EAAWpL,MACT,IAAI2D,cAEF,eAAA,iCAAiCiI,EAAM,OAK7CR,EAAWU,QAAQH,GACnBN,EAAcA,EAAYU,UAAUH,EAAM,GAAG9I,QAC7C8I,EAAQP,EAAYO,MAAMjB,GAE5B,OAAOW,cAxFbR,CAJkBzE,EAASD,KAAM4F,YACjC,IAAIC,kBAAkB,OAAQ,CAAEC,OAAO,OAIlCC,EAASC,GAAWvB,EAAewB,MAC1C,MAAO,CACLxH,OAAQyH,yBAAyBH,GACjC9F,SAAUkG,mBAAmBH,IAIjChH,eAAemH,mBACb1H,GAEA,MAAM2H,EAA0C,GAC1CxB,EAASnG,EAAOoG,YACtB,OAAa,CACX,MAAMO,KAAEA,EAAInN,MAAEA,SAAgB2M,EAAOO,OACrC,GAAIC,EAAM,CAIR,OAHyB1C,8BACvB2D,mBAAmBD,IAIvBA,EAAatK,KAAK7D,IAItB,SAAgBiO,yBACdzH,iFAEA,MAAMmG,EAASnG,EAAOoG,YACtB,OAAa,CACX,MAAM5M,MAAEA,EAAKmN,KAAEA,SAAexK,QAAAgK,EAAOO,QACrC,GAAIC,EACF,MAGF,MAAMkB,EAAmB5D,8BAA8BzK,eACjD2C,QAAA0L,OAgEJ,SAAUD,mBACdE,GAEA,MAAMC,EAAeD,EAAUA,EAAU7J,OAAS,GAC5C+J,EAA8C,CAClD/C,eAAgB8C,MAAAA,OAAA,EAAAA,EAAc9C,gBAEhC,IAAK,MAAMzD,KAAYsG,EACrB,GAAItG,EAAS0C,WACX,IAAK,MAAMsB,KAAahE,EAAS0C,WAAY,CAG3C,MAAMtH,EAAI4I,EAAUpB,OAAS,EAsB7B,GArBK4D,EAAmB9D,aACtB8D,EAAmB9D,WAAa,IAE7B8D,EAAmB9D,WAAWtH,KACjCoL,EAAmB9D,WAAWtH,GAAK,CACjCwH,MAAOoB,EAAUpB,QAIrB4D,EAAmB9D,WAAWtH,GAAGqL,iBAC/BzC,EAAUyC,iBACZD,EAAmB9D,WAAWtH,GAAG6I,aAAeD,EAAUC,aAC1DuC,EAAmB9D,WAAWtH,GAAG+I,cAC/BH,EAAUG,cACZqC,EAAmB9D,WAAWtH,GAAGsL,cAC/B1C,EAAU0C,cAMR1C,EAAUZ,SAAWY,EAAUZ,QAAQC,MAAO,CAC3CmD,EAAmB9D,WAAWtH,GAAGgI,UACpCoD,EAAmB9D,WAAWtH,GAAGgI,QAAU,CACzCuD,KAAM3C,EAAUZ,QAAQuD,MAAQ,OAChCtD,MAAO,KAGX,MAAMuD,EAAyB,GAC/B,IAAK,MAAMtD,KAAQU,EAAUZ,QAAQC,MAC/BC,EAAKP,OACP6D,EAAQ7D,KAAOO,EAAKP,MAElBO,EAAKM,eACPgD,EAAQhD,aAAeN,EAAKM,cAEM,IAAhC7M,OAAO8P,KAAKD,GAASnK,SACvBmK,EAAQ7D,KAAO,IAEjByD,EAAmB9D,WAAWtH,GAAGgI,QAAQC,MAAMxH,KAC7C+K,IAOZ,OAAOJ,EC9KFzH,eAAe+H,sBACpBvI,EACAF,EACA0I,EACAtI,GAUA,OAAO8F,oBARgBzE,YACrBzB,EACAF,EAAK6I,wBACLzI,GACa,EACb0C,KAAKC,UAAU6F,GACftI,IAKGM,eAAekI,gBACpB1I,EACAF,EACA0I,EACAtI,GAEA,MAAMuB,QAAiBF,YACrBzB,EACAF,EAAK+I,iBACL3I,GACa,EACb0C,KAAKC,UAAU6F,GACftI,GAIF,MAAO,CACLuB,SAFuByC,oCAD2BzC,EAASe,SCnCzD,SAAUoG,wBACdC,GAGA,GAAa,MAATA,EAEG,MAAqB,iBAAVA,EACT,CAAET,KAAM,SAAUtD,MAAO,CAAC,CAAEN,KAAMqE,KAC/BA,EAAerE,KAClB,CAAE4D,KAAM,SAAUtD,MAAO,CAAC+D,IACvBA,EAAkB/D,MACtB+D,EAAkBT,KAGfS,EAFA,CAAET,KAAM,SAAUtD,MAAQ+D,EAAkB/D,YAFhD,EASH,SAAUgE,iBACdnH,GAEA,IAAIoH,EAAmB,GACvB,GAAuB,iBAAZpH,EACToH,EAAW,CAAC,CAAEvE,KAAM7C,SAEpB,IAAK,MAAMqH,KAAgBrH,EACG,iBAAjBqH,EACTD,EAASzL,KAAK,CAAEkH,KAAMwE,IAEtBD,EAASzL,KAAK0L,GAIpB,OAWF,SAASC,+CACPnE,GAEA,MAAMoE,EAAuB,CAAEd,KAAM,OAAQtD,MAAO,IAC9CqE,EAA2B,CAAEf,KAAM,WAAYtD,MAAO,IAC5D,IAAIsE,GAAiB,EACjBC,GAAqB,EACzB,IAAK,MAAMtE,KAAQD,EACb,qBAAsBC,GACxBoE,EAAgBrE,MAAMxH,KAAKyH,GAC3BsE,GAAqB,IAErBH,EAAYpE,MAAMxH,KAAKyH,GACvBqE,GAAiB,GAIrB,GAAIA,GAAkBC,EACpB,MAAM,IAAItK,cAER,kBAAA,8HAIJ,IAAKqK,IAAmBC,EACtB,MAAM,IAAItK,cAER,kBAAA,oDAIJ,GAAIqK,EACF,OAAOF,EAGT,OAAOC,EA9CAF,CAA+CF,GAiDlD,SAAUO,2BACdd,GAEA,IAAIe,EACJ,GAAKf,EAAkCgB,SACrCD,EAAmBf,MACd,CAGLe,EAAmB,CAAEC,SAAU,CADfV,iBAAiBN,KAQnC,OALKA,EAAkCiB,oBACrCF,EAAiBE,kBAAoBb,wBAClCJ,EAAkCiB,oBAGhCF,EChGT,MAAMG,EAAuC,CAC3C,OACA,aACA,eACA,oBAGIC,EAA6D,CACjEC,KAAM,CAAC,OAAQ,cACfC,SAAU,CAAC,oBACX/J,MAAO,CAAC,OAAQ,gBAEhBgK,OAAQ,CAAC,SAGLC,EAA0D,CAC9DH,KAAM,CAAC,SACPC,SAAU,CAAC,SACX/J,MAAO,CAAC,OAAQ,YAEhBgK,OAAQ,ICJG,MAAAE,YAKX/R,YACE+H,EACOF,EACA0I,EACAtI,GAFA5H,KAAKwH,MAALA,EACAxH,KAAMkQ,OAANA,EACAlQ,KAAc4H,eAAdA,EAPD5H,KAAQ2R,SAAc,GACtB3R,KAAA4R,aAA8B/M,QAAQO,UAQ5CpF,KAAK6R,aAAenK,GAChBwI,MAAAA,OAAM,EAANA,EAAQ4B,YDLV,SAAUC,oBAAoBD,GAClC,IAAIE,EAA8B,KAClC,IAAK,MAAMC,KAAeH,EAAS,CACjC,MAAMhC,KAAEA,EAAItD,MAAEA,GAAUyF,EACxB,IAAKD,GAAwB,SAATlC,EAClB,MAAM,IAAIrJ,cAAa,kBAErB,iDAAiDqJ,KAGrD,IAAK5E,EAAeL,SAASiF,GAC3B,MAAM,IAAIrJ,cAER,kBAAA,4CAA4CqJ,0BAA6B1F,KAAKC,UAC5Ea,MAKN,IAAKgH,MAAMC,QAAQ3F,GACjB,MAAM,IAAI/F,cAER,kBAAA,mEAIJ,GAAqB,IAAjB+F,EAAM5G,OACR,MAAM,IAAIa,cAER,kBAAA,8CAIJ,MAAM2L,EAA0C,CAC9ClG,KAAM,EACNmG,WAAY,EACZtF,aAAc,EACduF,iBAAkB,GAGpB,IAAK,MAAM7F,KAAQD,EACjB,IAAK,MAAMtL,KAAOkQ,EACZlQ,KAAOuL,IACT2F,EAAYlR,IAAQ,GAI1B,MAAMqR,EAAalB,EAAqBvB,GACxC,IAAK,MAAM5O,KAAOkQ,EAChB,IAAKmB,EAAW1H,SAAS3J,IAAQkR,EAAYlR,GAAO,EAClD,MAAM,IAAIuF,cAER,kBAAA,sBAAsBqJ,qBAAwB5O,WAKpD,GAAI8Q,IACgCP,EAA6B3B,GAChCjF,SAASmH,EAAYlC,MAClD,MAAM,IAAIrJ,cAAa,kBAErB,sBAAsBqJ,mBACpBkC,EAAYlC,gCACc1F,KAAKC,UAC/BoH,MAKRO,EAAcC,GChEZF,CAAoB7B,EAAO4B,SAC3B9R,KAAK2R,SAAWzB,EAAO4B,SAS3B5J,mBAEE,aADMlI,KAAK4R,aACJ5R,KAAK2R,SAOdzJ,kBACEmB,uBAEMrJ,KAAK4R,aACX,MAAMY,EAAahC,iBAAiBnH,GAC9BoJ,EAAiD,CACrDC,eAA2B,QAAXnM,EAAAvG,KAAKkQ,cAAM,IAAA3J,OAAA,EAAAA,EAAEmM,eAC7BC,iBAA6B,QAAXjI,EAAA1K,KAAKkQ,cAAM,IAAAxF,OAAA,EAAAA,EAAEiI,iBAC/BC,MAAkB,QAAXjG,EAAA3M,KAAKkQ,cAAM,IAAAvD,OAAA,EAAAA,EAAEiG,MACpBC,WAAuB,QAAXnG,EAAA1M,KAAKkQ,cAAM,IAAAxD,OAAA,EAAAA,EAAEmG,WACzB1B,kBAA8B,QAAX2B,EAAA9S,KAAKkQ,cAAM,IAAA4C,OAAA,EAAAA,EAAE3B,kBAChCD,SAAU,IAAIlR,KAAK2R,SAAUa,IAE/B,IAAIO,EAAc,GAkClB,OAhCA/S,KAAK4R,aAAe5R,KAAK4R,aACtBvM,MAAK,IACJ+K,gBACEpQ,KAAK6R,aACL7R,KAAKwH,MACLiL,EACAzS,KAAK4H,kBAGRvC,MAAK2N,YACJ,GACEA,EAAO7J,SAAS0C,YAChBmH,EAAO7J,SAAS0C,WAAWjG,OAAS,EACpC,CACA5F,KAAK2R,SAAS3M,KAAKwN,GACnB,MAAMS,EAA2B,CAC/BzG,OAAiC,QAA1BjG,EAAAyM,EAAO7J,SAAS0C,kBAAU,IAAAtF,OAAA,EAAAA,EAAG,GAAGgG,QAAQC,QAAS,GAExDsD,MAAgC,QAA1BpF,EAAAsI,EAAO7J,SAAS0C,kBAAU,IAAAnB,OAAA,EAAAA,EAAG,GAAG6B,QAAQuD,OAAQ,SAExD9P,KAAK2R,SAAS3M,KAAKiO,OACd,CACL,MAAMC,EAAoB9G,wBAAwB4G,EAAO7J,UACrD+J,GACFtM,EAAOhE,KACL,mCAAmCsQ,2CAIzCH,EAAcC,WAEZhT,KAAK4R,aACJmB,EAQT7K,wBACEmB,uBAEMrJ,KAAK4R,aACX,MAAMY,EAAahC,iBAAiBnH,GAC9BoJ,EAAiD,CACrDC,eAA2B,QAAXnM,EAAAvG,KAAKkQ,cAAM,IAAA3J,OAAA,EAAAA,EAAEmM,eAC7BC,iBAA6B,QAAXjI,EAAA1K,KAAKkQ,cAAM,IAAAxF,OAAA,EAAAA,EAAEiI,iBAC/BC,MAAkB,QAAXjG,EAAA3M,KAAKkQ,cAAM,IAAAvD,OAAA,EAAAA,EAAEiG,MACpBC,WAAuB,QAAXnG,EAAA1M,KAAKkQ,cAAM,IAAAxD,OAAA,EAAAA,EAAEmG,WACzB1B,kBAA8B,QAAX2B,EAAA9S,KAAKkQ,cAAM,IAAA4C,OAAA,EAAAA,EAAE3B,kBAChCD,SAAU,IAAIlR,KAAK2R,SAAUa,IAEzBW,EAAgBlD,sBACpBjQ,KAAK6R,aACL7R,KAAKwH,MACLiL,EACAzS,KAAK4H,gBAwCP,OApCA5H,KAAK4R,aAAe5R,KAAK4R,aACtBvM,MAAK,IAAM8N,IAGXC,OAAMC,IACL,MAAM,IAAI3T,MAzHG,mBA2Hd2F,MAAKiO,GAAgBA,EAAanK,WAClC9D,MAAK8D,IACJ,GAAIA,EAAS0C,YAAc1C,EAAS0C,WAAWjG,OAAS,EAAG,CACzD5F,KAAK2R,SAAS3M,KAAKwN,GACnB,MAAMS,EAAuB/S,OAAAqT,OAAA,GAAApK,EAAS0C,WAAW,GAAGU,SAE/C0G,EAAgBnD,OACnBmD,EAAgBnD,KAAO,SAEzB9P,KAAK2R,SAAS3M,KAAKiO,OACd,CACL,MAAMC,EAAoB9G,wBAAwBjD,GAC9C+J,GACFtM,EAAOhE,KACL,yCAAyCsQ,+CAKhDE,OAAM3N,IA9IQ,iBAkJTA,EAAE5F,SAGJ+G,EAAO9D,MAAM2C,MAGZ0N,GCvIE,MAAAK,gBAUX7T,YACE8T,EACAC,EACA9L,eAEA,KAA0B,QAArB8C,EAAY,QAAZnE,EAAAkN,EAAS3N,WAAG,IAAAS,OAAA,EAAAA,EAAEN,eAAO,IAAAyE,OAAA,EAAAA,EAAEhC,QAC1B,MAAM,IAAIjC,cAER,aAAA,+HAEG,KAA0B,QAArBiG,EAAY,QAAZC,EAAA8G,EAAS3N,WAAG,IAAA6G,OAAA,EAAAA,EAAE1G,eAAO,IAAAyG,OAAA,EAAAA,EAAEiH,WACjC,MAAM,IAAIlN,cAER,gBAAA,qIAGFzG,KAAK6R,aAAe,CAClBnJ,OAAQ+K,EAAS3N,IAAIG,QAAQyC,OAC7BX,QAAS0L,EAAS3N,IAAIG,QAAQ0N,UAC9BrN,SAAUmN,EAASnN,UAEhBmN,EAA6BvN,WAChClG,KAAK6R,aAAalJ,iBAAmB,IAClC8K,EAA6BvN,SAAU0N,YAGvCH,EAA6BpN,OAChCrG,KAAK6R,aAAa/I,aAAe,IAC9B2K,EAA6BpN,KAAMuN,YAGtCF,EAAYlM,MAAMqD,SAAS,KACzB6I,EAAYlM,MAAMqM,WAAW,WAE/B7T,KAAKwH,MAAQ,qBAAqBkM,EAAYlM,QAG9CxH,KAAKwH,MAAQkM,EAAYlM,MAI3BxH,KAAKwH,MAAQ,4BAA4BkM,EAAYlM,QAEvDxH,KAAK2S,iBAAmBe,EAAYf,kBAAoB,GACxD3S,KAAK0S,eAAiBgB,EAAYhB,gBAAkB,GACpD1S,KAAK4S,MAAQc,EAAYd,MACzB5S,KAAK6S,WAAaa,EAAYb,WAC9B7S,KAAKmR,kBAAoBb,wBACvBoD,EAAYvC,mBAEdnR,KAAK4H,eAAiBA,GAAkB,GAO1CM,sBACEmB,GAEA,MAAMyK,EAAkB9C,2BAA2B3H,GACnD,OAAO+G,gBACLpQ,KAAK6R,aACL7R,KAAKwH,MAAKtH,OAAAqT,OAAA,CAERZ,iBAAkB3S,KAAK2S,iBACvBD,eAAgB1S,KAAK0S,eACrBE,MAAO5S,KAAK4S,MACZC,WAAY7S,KAAK6S,WACjB1B,kBAAmBnR,KAAKmR,mBACrB2C,GAEL9T,KAAK4H,gBAUTM,4BACEmB,GAEA,MAAMyK,EAAkB9C,2BAA2B3H,GACnD,OAAO4G,sBACLjQ,KAAK6R,aACL7R,KAAKwH,MAAKtH,OAAAqT,OAAA,CAERZ,iBAAkB3S,KAAK2S,iBACvBD,eAAgB1S,KAAK0S,eACrBE,MAAO5S,KAAK4S,MACZC,WAAY7S,KAAK6S,WACjB1B,kBAAmBnR,KAAKmR,mBACrB2C,GAEL9T,KAAK4H,gBAQTmM,UAAUC,GACR,OAAO,IAAItC,YACT1R,KAAK6R,aACL7R,KAAKwH,MAEHtH,OAAAqT,OAAA,CAAAX,MAAO5S,KAAK4S,MACZC,WAAY7S,KAAK6S,WACjB1B,kBAAmBnR,KAAKmR,mBACrB6C,GAELhU,KAAK4H,gBAOTM,kBACEmB,GAEA,MAAMyK,EAAkB9C,2BAA2B3H,GACnD,OCpKGnB,eAAe+L,YACpBvM,EACAF,EACA0I,EACAtI,GAUA,aARuBqB,YACrBzB,EACAF,EAAK4M,aACLxM,GACA,EACA0C,KAAKC,UAAU6F,GACftI,IAEcsC,ODsJP+J,CAAYjU,KAAK6R,aAAc7R,KAAKwH,MAAOsM,IE3JhC,MAAAK,OA2BpBxU,YAAYyU,GAEV,IAAK,MAAMC,KAAYD,EACrBpU,KAAKqU,GAAYD,EAAaC,GAGhCrU,KAAKwB,KAAO4S,EAAa5S,KACzBxB,KAAKsU,WAAWF,EAAatI,eAAe,eACtCsI,EAAaE,SASrBC,SACE,MAAMC,EAAoD,CACxDhT,KAAMxB,KAAKwB,MAEb,IAAK,MAAMiT,KAAQzU,KACbA,KAAK8L,eAAe2I,SAAwBC,IAAf1U,KAAKyU,KACvB,aAATA,GAAuBzU,KAAKwB,OAASmK,EAAWgJ,SAClDH,EAAIC,GAAQzU,KAAKyU,KAIvB,OAAOD,EAGTI,aAAaC,GACX,OAAO,IAAIC,YAAYD,EAAaA,EAAYE,OAGlDH,cACEI,GAOA,OAAO,IAAIC,aACTD,EACAA,EAAaE,WACbF,EAAaG,oBAKjBP,cAAcQ,GACZ,OAAO,IAAIC,aAAaD,GAG1BR,kBACEQ,GAEA,OAAO,IAAIC,aAAaD,EAAcA,EAAaE,MAGrDV,eAAeW,GACb,OAAO,IAAIC,cAAcD,GAI3BX,cAAca,GACZ,OAAO,IAAIC,aAAaD,GAI1Bb,eAAee,GACb,OAAO,IAAIC,cAAcD,IAoBvB,MAAOH,sBAAsBrB,OACjCxU,YAAYyU,GACVrU,MAAKG,OAAAqT,OAAA,CACH/R,KAAMmK,EAAWkK,SACdzB,KASH,MAAOsB,qBAAqBvB,OAChCxU,YAAYyU,GACVrU,MAAKG,OAAAqT,OAAA,CACH/R,KAAMmK,EAAWmK,QACd1B,KASH,MAAOwB,sBAAsBzB,OACjCxU,YAAYyU,GACVrU,MAAKG,OAAAqT,OAAA,CACH/R,KAAMmK,EAAWoK,SACd3B,KAUH,MAAOiB,qBAAqBlB,OAEhCxU,YAAYyU,EAA6B4B,GACvCjW,MAAKG,OAAAqT,OAAA,CACH/R,KAAMmK,EAAWsK,QACd7B,IAELpU,KAAKsV,KAAOU,EAMdzB,SACE,MAAMC,EAAMzU,MAAMwU,SAIlB,OAHIvU,KAAKsV,OACPd,EAAU,KAAIxU,KAAKsV,MAEdd,GAUL,MAAOM,oBAAoBX,OAC/BxU,YAAYyU,EAAmCW,GAC7ChV,MAAKG,OAAAqT,OAAA,CACH/R,KAAMmK,EAAWuK,OACd9B,IAHwCpU,KAAK+U,MAALA,EAU/CR,SACE,MAAMC,EAAMzU,MAAMwU,SAElB,OADAC,EAAIO,MAAQ/U,KAAK+U,MAAMR,SAChBC,GASL,MAAOS,qBAAqBd,OAChCxU,YACEyU,EACOc,EAGAC,EAA+B,IAEtCpV,MAAKG,OAAAqT,OAAA,CACH/R,KAAMmK,EAAWgJ,QACdP,IAPEpU,KAAUkV,WAAVA,EAGAlV,KAAkBmV,mBAAlBA,EAWTZ,SACE,MAAMC,EAAMzU,MAAMwU,SAClBC,EAAIU,WAAUhV,OAAAqT,OAAA,GAAQvT,KAAKkV,YAC3B,MAAMiB,EAAW,GACjB,GAAInW,KAAKmV,mBACP,IAAK,MAAMiB,KAAepW,KAAKmV,mBAC7B,IAAKnV,KAAKkV,WAAWpJ,eAAesK,GAClC,MAAM,IAAI3P,cAAa,iBAErB,aAAa2P,wDAKrB,IAAK,MAAMA,KAAepW,KAAKkV,WACzBlV,KAAKkV,WAAWpJ,eAAesK,KACjC5B,EAAIU,WAAWkB,GAAepW,KAAKkV,WACjCkB,GACA7B,SACGvU,KAAKmV,mBAAmBtK,SAASuL,IACpCD,EAASnR,KAAKoR,IAQpB,OAJID,EAASvQ,OAAS,IACpB4O,EAAI2B,SAAWA,UAET3B,EAA8BW,mBAC/BX,GClPK,SAAA6B,YACdvQ,EAAmBwQ,IACnBrQ,GAEAH,EC9BI,SAAUyQ,mBACd/V,GAEA,OAAIA,GAAYA,EAA+BgW,UACrChW,EAA+BgW,UAEhChW,EDwBH+V,CAAmBzQ,GAIzB,OAF6C2Q,aAAa3Q,EflCjC,YeoCHK,aAAa,CACjCuQ,YAAYzQ,MAAAA,OAAO,EAAPA,EAASK,WfnCO,gBe6ChB,SAAAqQ,mBACdlD,EACAC,EACA9L,GAEA,IAAK8L,EAAYlM,MACf,MAAM,IAAIf,cAER,WAAA,sFAGJ,OAAO,IAAI+M,gBAAgBC,EAAUC,EAAa9L,IE1CpD,SAASgP,iBACPC,EACE,IAAIvV,UjBlBmB,YiBoBrB,CAACwV,GAAaC,mBAAoBzQ,MAEhC,MAAMR,EAAMgR,EAAUE,YAAY,OAAO7Q,eACnCE,EAAOyQ,EAAUE,YAAY,iBAC7BhR,EAAmB8Q,EAAUE,YAAY,sBAC/C,OAAO,IAAInR,gBAAgBC,EAAKO,EAAML,EAAkB,CAAEM,SAAAA,gBAG5DvE,sBAAqB,IAGzBkV,EAAgBhX,WAEhBgX,EAAgBhX,UAAe,WAGjC2W", "preExistingComment": "firebase-vertexai.js.map"}