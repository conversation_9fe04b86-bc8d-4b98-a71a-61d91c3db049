{"version": 3, "file": "firebase-functions-compat.js", "sources": ["../functions/src/config.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../functions/src/serializer.ts", "../functions/src/constants.ts", "../functions/src/error.ts", "../functions/src/context.ts", "../functions/src/service.ts", "../functions/src/api.ts", "../functions-compat/src/register.ts", "../functions-compat/src/service.ts", "../functions-compat/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  constructor(\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nconst DEFAULT_REGION = 'us-central1';\n\nconst factory: InstanceFactory<'functions-compat'> = (\n  container: ComponentContainer,\n  { instanceIdentifier: regionOrCustomDomain }: InstanceFactoryOptions\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const functionsServiceExp = container.getProvider('functions').getImmediate({\n    identifier: regionOrCustomDomain ?? DEFAULT_REGION\n  });\n\n  return new FunctionsService(app, functionsServiceExp);\n};\n\nexport function registerFunctions(): void {\n  const namespaceExports = {\n    Functions: FunctionsService\n  };\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component('functions-compat', factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseFunctions, HttpsCallable } from '@firebase/functions-types';\nimport {\n  httpsCallable as httpsCallableExp,\n  httpsCallableFromURL as httpsCallableFromURLExp,\n  connectFunctionsEmulator as useFunctionsEmulatorExp,\n  HttpsCallableOptions,\n  Functions as FunctionsServiceExp\n} from '@firebase/functions';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport { FirebaseError } from '@firebase/util';\n\nexport class FunctionsService implements FirebaseFunctions, _FirebaseService {\n  /**\n   * For testing.\n   * @internal\n   */\n  _region: string;\n  /**\n   * For testing.\n   * @internal\n   */\n  _customDomain: string | null;\n\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FunctionsServiceExp\n  ) {\n    this._region = this._delegate.region;\n    this._customDomain = this._delegate.customDomain;\n  }\n  httpsCallable(name: string, options?: HttpsCallableOptions): HttpsCallable {\n    return httpsCallableExp(this._delegate, name, options);\n  }\n  httpsCallableFromURL(\n    url: string,\n    options?: HttpsCallableOptions\n  ): HttpsCallable {\n    return httpsCallableFromURLExp(this._delegate, url, options);\n  }\n  /**\n   * Deprecated in pre-modularized repo, does not exist in modularized\n   * functions package, need to convert to \"host\" and \"port\" args that\n   * `useFunctionsEmulatorExp` takes.\n   * @deprecated\n   */\n  useFunctionsEmulator(origin: string): void {\n    const match = origin.match('[a-zA-Z]+://([a-zA-Z0-9.-]+)(?::([0-9]+))?');\n    if (match == null) {\n      throw new FirebaseError(\n        'functions',\n        'No origin provided to useFunctionsEmulator()'\n      );\n    }\n    if (match[2] == null) {\n      throw new FirebaseError(\n        'functions',\n        'Port missing in origin provided to useFunctionsEmulator()'\n      );\n    }\n    return useFunctionsEmulatorExp(this._delegate, match[1], Number(match[2]));\n  }\n  useEmulator(host: string, port: number): void {\n    return useFunctionsEmulatorExp(this._delegate, host, port);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../package.json';\nimport { registerFunctions } from './register';\nimport * as types from '@firebase/functions-types';\n\nregisterFunctions();\nfirebase.registerVersion(name, version);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    functions: {\n      (app?: FirebaseApp): types.FirebaseFunctions;\n      Functions: typeof types.FirebaseFunctions;\n    };\n  }\n  interface FirebaseApp {\n    functions(regionOrCustomDomain?: string): types.FirebaseFunctions;\n  }\n}\n"], "names": ["variant", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LONG_TYPE", "UNSIGNED_LONG_TYPE", "mapValues", "o", "f", "result", "hasOwnProperty", "encode", "Number", "valueOf", "isFinite", "toString", "call", "Date", "toISOString", "Array", "isArray", "map", "x", "decode", "json", "isNaN", "FUNCTIONS_TYPE", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "details", "_errorForResponse", "status", "bodyJSON", "codeForHTTPStatus", "description", "undefined", "errorJSON", "error", "e", "ContextProvider", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "getImmediate", "optional", "get", "then", "getAuthToken", "token", "getToken", "accessToken", "getMessagingToken", "self", "Notification", "permission", "getAppCheckToken", "limitedUseAppCheckTokens", "getLimitedUseToken", "getContext", "authToken", "messagingToken", "appCheckToken", "DEFAULT_REGION", "responseLineRE", "FunctionsService", "app", "regionOrCustomDomain", "fetchImpl", "args", "fetch", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "Promise", "resolve", "deleteService", "url", "URL", "customDomain", "origin", "pathname", "region", "_delete", "_url", "projectId", "options", "httpsCallable", "functionsInstance", "callable", "callAtURL", "stream", "streamAtURL", "async", "makeAuthHeaders", "headers", "context", "body", "failAfterHandle", "millis", "timer", "promise", "reject", "setTimeout", "cancel", "clearTimeout", "failAfter", "timeout", "response", "race", "method", "JSON", "stringify", "postJSON", "responseData", "signal", "Symbol", "asyncIterator", "next", "resultResolver", "resultRejecter", "resultPromise", "_a", "addEventListener", "rstream", "reader", "processLine", "line", "controller", "match", "jsonData", "parse", "enqueue", "decoder", "TextDecoder", "ReadableStream", "start", "currentText", "pump", "aborted", "done", "read", "trim", "close", "lines", "split", "pop", "functionsError", "createResponseStream", "<PERSON><PERSON><PERSON><PERSON>", "rreader", "return", "connectFunctionsEmulator", "host", "port", "httpsCallableFromURL", "_registerComponent", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "registerVersion", "version", "namespaceExports", "_region", "_customDomain", "_httpsCallable", "httpsCallableFromURLExp", "useFunctionsEmulator", "useFunctionsEmulatorExp", "useEmulator", "factory", "functionsServiceExp", "identifier", "Functions", "firebase", "registerComponent"], "mappings": "ybAqCkCA,eCoCrBC,UAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,EAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,EAAaF,UAAUG,eAK9CD,EAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,EAGnBH,OACEX,KACGe,GAEH,IAcuCA,EAdjCb,EAAca,EAAK,IAAoB,GACvCC,KAAcZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,GAUuBF,EAVcb,EAAVe,EAW7BC,QAAQC,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQP,EAAKM,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,OAAaD,SAbwB,QAE7DG,KAAiBpB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,EAAcmB,EAAUQ,EAAatB,IAa3D,MAAMiB,EAAU,gBClHV,SAAUM,EACdb,GAEA,OAAIA,GAAYA,EAA+Bc,UACrCd,EAA+Bc,UAEhCd,QCCEe,EAiBX5B,YACWM,EACAuB,EACAC,GAFAzB,KAAIC,KAAJA,EACAD,KAAewB,gBAAfA,EACAxB,KAAIyB,KAAJA,EAnBXzB,KAAiB0B,mBAAG,EAIpB1B,KAAY2B,aAAe,GAE3B3B,KAAA4B,kBAA2C,OAE3C5B,KAAiB6B,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADA/B,KAAK4B,kBAAoBG,EAClB/B,KAGTgC,qBAAqBN,GAEnB,OADA1B,KAAK0B,kBAAoBA,EAClB1B,KAGTiC,gBAAgBC,GAEd,OADAlC,KAAK2B,aAAeO,EACblC,KAGTmC,2BAA2BC,GAEzB,OADApC,KAAK6B,kBAAoBO,EAClBpC,MCpDX,MAAMqC,EAAY,iDACZC,EAAqB,kDAE3B,SAASC,EAGPC,EACAC,GAEA,MAAMC,EAAqC,GAC3C,IAAK,MAAMzB,KAAOuB,EACZA,EAAEG,eAAe1B,KACnByB,EAAOzB,GAAOwB,EAAED,EAAEvB,KAGtB,OAAOyB,EASH,SAAUE,EAAOjC,GACrB,GAAY,MAARA,EACF,OAAO,KAKT,GAAoB,iBAFlBA,EADEA,aAAgBkC,OACXlC,EAAKmC,UAEHnC,IAAqBoC,SAASpC,GAGvC,OAAOA,EAET,IAAa,IAATA,IAA0B,IAATA,EACnB,OAAOA,EAET,GAA6C,oBAAzCT,OAAOE,UAAU4C,SAASC,KAAKtC,GACjC,OAAOA,EAET,GAAIA,aAAgBuC,KAClB,OAAOvC,EAAKwC,cAEd,GAAIC,MAAMC,QAAQ1C,GAChB,OAAOA,EAAK2C,IAAIC,GAAKX,EAAOW,IAE9B,GAAoB,mBAAT5C,GAAuC,iBAATA,EACvC,OAAO4B,EAAU5B,EAAO4C,GAAKX,EAAOW,IAGtC,MAAM,IAAI7D,MAAM,mCAAqCiB,GASjD,SAAU6C,EAAOC,GACrB,GAAY,MAARA,EACF,OAAOA,EAET,GAAKA,EAAoC,SACvC,OAASA,EAAoC,UAC3C,KAAKpB,EAEL,KAAKC,EAIH,IAAMpB,EAAQ2B,OAAQY,EAA2C,OACjE,GAAIC,MAAMxC,GACR,MAAM,IAAIxB,MAAM,qCAAuC+D,GAEzD,OAAOvC,EAET,QACE,MAAM,IAAIxB,MAAM,qCAAuC+D,GAI7D,OAAIL,MAAMC,QAAQI,GACTA,EAAKH,IAAIC,GAAKC,EAAOD,IAEV,mBAATE,GAAuC,iBAATA,EAChClB,EAAUkB,EAAOF,GAAKC,EAAOD,IAG/BE,ECvFF,MAAME,EAAiB,YCUxBC,EAAuD,CAC3DC,GAAI,KACJC,UAAW,YACXC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,UAAW,YACXC,eAAgB,iBAChBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,QAAS,UACTC,aAAc,eACdC,cAAe,gBACfC,SAAU,WACVC,YAAa,cACbC,UAAW,mBAUAC,UAAuBrF,EAIlCE,YAKEC,EACAC,EAISkF,GAEThF,SAAS4D,KAAkB/D,IAAQC,GAAW,IAFrCG,KAAO+E,QAAPA,EAMT7E,OAAOC,eAAeH,KAAM8E,EAAe1E,YAmD/B,SAAA4E,EACdC,EACAC,GAEA,IAAItF,EA3CN,SAA2BqF,GAEzB,GAAc,KAAVA,GAAiBA,EAAS,IAC5B,MAAO,KAET,OAAQA,GACN,KAAK,EAEH,MAAO,WACT,KAAK,IACH,MAAO,mBACT,KAAK,IACH,MAAO,kBACT,KAAK,IACH,MAAO,oBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,UACT,KAAK,IACH,MAAO,qBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,WACT,KAAK,IACH,MAAO,gBACT,KAAK,IACH,MAAO,cACT,KAAK,IACH,MAAO,oBAGX,MAAO,UAUIE,CAAkBF,GAGzBG,EAAsBxF,EAEtBmF,OAAmBM,EAGvB,IACE,IAAMC,EAAYJ,GAAYA,EAASK,MACvC,GAAID,EAAW,CACb,MAAML,EAASK,EAAUL,OACzB,GAAsB,iBAAXA,EAAqB,CAC9B,IAAKrB,EAAaqB,GAEhB,OAAO,IAAIH,EAAe,WAAY,YAExClF,EAAOgE,EAAaqB,GAIpBG,EAAcH,EAGhB,IAAMpF,EAAUyF,EAAUzF,QACH,iBAAZA,IACTuF,EAAcvF,GAGhBkF,EAAUO,EAAUP,aACJM,IAAZN,IACFA,EAAUvB,EAAOuB,KAGrB,MAAOS,IAIT,MAAa,OAAT5F,EAIK,KAGF,IAAIkF,EAAelF,EAAMwF,EAAaL,SCpIlCU,EAIX9F,YACE+F,EACAC,EACAC,GANM5F,KAAI6F,KAAgC,KACpC7F,KAAS8F,UAA6B,KACtC9F,KAAQ+F,SAAoC,KAMlD/F,KAAK6F,KAAOH,EAAaM,aAAa,CAAEC,UAAU,IAClDjG,KAAK8F,UAAYH,EAAkBK,aAAa,CAC9CC,UAAU,IAGPjG,KAAK6F,MACRH,EAAaQ,MAAMC,KACjBN,GAAS7F,KAAK6F,KAAOA,EACrB,QAMC7F,KAAK8F,WACRH,EAAkBO,MAAMC,KACtBL,GAAc9F,KAAK8F,UAAYA,EAC/B,QAMC9F,KAAK+F,UACRH,EAAiBM,MAAMC,KACrBJ,GAAa/F,KAAK+F,SAAWA,EAC7B,QAONK,qBACE,GAAKpG,KAAK6F,KAIV,IACE,IAAMQ,QAAcrG,KAAK6F,KAAKS,WAC9B,OAAOD,MAAAA,OAAA,EAAAA,EAAOE,YACd,MAAOf,GAEP,QAIJgB,0BACE,GACGxG,KAAK8F,WACJ,iBAAkBW,MACQ,YAA5BC,aAAaC,WAKf,IACE,OAAa3G,KAAK8F,UAAUQ,WAC5B,MAAOd,GAKP,QAIJoB,uBACEC,GAEA,GAAI7G,KAAK+F,SAAU,CACjB,IAAMrD,EAASmE,QACL7G,KAAK+F,SAASe,2BACd9G,KAAK+F,SAASO,WACxB,OAAI5D,EAAO6C,MAIF,KAEF7C,EAAO2D,MAEhB,OAAO,KAGTU,iBAAiBF,GAIf,MAAO,CAAEG,gBAHehH,KAAKoG,eAGTa,qBAFSjH,KAAKwG,oBAEEU,oBADRlH,KAAK4G,iBAAiBC,KC7G/C,MAAMM,EAAiB,cAExBC,EAAiB,6BA0DVC,EAYX1H,YACW2H,EACT5B,EACAC,EACAC,EACA2B,EAA+BJ,EACtBK,EAA0B,IAAIC,IAASC,SAASD,IALhDzH,KAAGsH,IAAHA,EAKAtH,KAASwH,UAATA,EAhBXxH,KAAc2H,eAAkB,KAkB9B3H,KAAK4H,gBAAkB,IAAInC,EACzBC,EACAC,EACAC,GAGF5F,KAAK6H,kBAAoB,IAAIC,QAAQC,IACnC/H,KAAKgI,cAAgB,IACZF,QAAQC,QAAQA,OAK3B,IACE,IAAME,EAAM,IAAIC,IAAIX,GACpBvH,KAAKmI,aACHF,EAAIG,QAA2B,MAAjBH,EAAII,SAAmB,GAAKJ,EAAII,UAChDrI,KAAKsI,OAASnB,EACd,MAAO3B,GACPxF,KAAKmI,aAAe,KACpBnI,KAAKsI,OAASf,GAIlBgB,UACE,OAAOvI,KAAKgI,gBAQdQ,KAAKvI,GACH,IAAMwI,EAAYzI,KAAKsH,IAAIoB,QAAQD,UACnC,OAA4B,OAAxBzI,KAAK2H,eAKiB,OAAtB3H,KAAKmI,gBACGnI,KAAKmI,gBAAgBlI,eAGfD,KAAKsI,UAAUG,wBAAgCxI,OARhDD,KAAK2H,kBACAc,KAAazI,KAAKsI,UAAUrI,KAiCtC0I,SAAAA,EACdC,EACA3I,EACAyI,GAEA,IAAMG,EAAW,IAGf,OAwHFlI,EAxHuCA,EAyHvC+H,EAzH6CA,GAAW,GA2HlDT,GALNW,EAtHcA,GA2HgBJ,KA3HGvI,GA4H1B6I,EAAUF,EAAmBX,EAAKtH,EAAM+H,GAPjD,IAGE/H,EACA+H,EAEMT,GAjHN,OAPAY,EAASE,OAAS,CAChBpI,EACA+H,KAEA,OAmMF/H,EAnMyCA,EAoMzC+H,EApM+CA,EAsMzCT,GALNW,EAjMgBA,GAsMcJ,KAtMKvI,GAuM5B+I,EAAYJ,EAAmBX,EAAKtH,EAAM+H,GAAW,IAP9D,IAGE/H,EAGMsH,GAnMCY,EAkFTI,eAAeC,EACbN,EACAF,GAEA,MAAMS,EAAkC,GACxC,IAAMC,QAAgBR,EAAkBhB,gBAAgBb,WACtD2B,EAAQ7B,0BAWV,OATIuC,EAAQpC,YACVmC,EAAuB,cAAI,UAAYC,EAAQpC,WAE7CoC,EAAQnC,iBACVkC,EAAQ,8BAAgCC,EAAQnC,gBAEpB,OAA1BmC,EAAQlC,gBACViC,EAAQ,uBAAyBC,EAAQlC,eAEpCiC,EAuBTF,eAAeH,EACbF,EACAX,EACAtH,EACA+H,GAIA,IAAMW,EAAO,CAAE1I,KADfA,EAAOiC,EAAOjC,IAIRwI,QAAgBD,EAAgBN,EAAmBF,GAKzD,MAAMY,EA7QR,SAAmBC,GAIjB,IAAIC,EAAoB,KACxB,MAAO,CACLC,QAAS,IAAI3B,QAAQ,CAAC9G,EAAG0I,KACvBF,EAAQG,WAAW,KACjBD,EAAO,IAAI5E,EAAe,oBAAqB,uBAC9CyE,KAELK,OAAQ,KACFJ,GACFK,aAAaL,KAgQKM,CAFRpB,EAAQqB,SAAW,KAG7BC,QAAiBlC,QAAQmC,KAAK,CApGtChB,eACEhB,EACAoB,EACAF,EACA3B,GAEA2B,EAAQ,gBAAkB,mBAE1B,IAAIa,EACJ,IACEA,QAAiBxC,EAAUS,EAAK,CAC9BiC,OAAQ,OACRb,KAAMc,KAAKC,UAAUf,GACrBF,QAAAA,IAEF,MAAO3D,GAKP,MAAO,CACLP,OAAQ,EACRxB,KAAM,MAGV,IAAIA,EAAgC,KACpC,IACEA,QAAauG,EAASvG,OACtB,MAAO+B,IAGT,MAAO,CACLP,OAAQ+E,EAAS/E,OACjBxB,KAAAA,GAoEA4G,CAASpC,EAAKoB,EAAMF,EAASP,EAAkBpB,WAC/C8B,EAAgBG,QAChBb,EAAkBf,oBAOpB,GAHAyB,EAAgBM,UAGXI,EACH,MAAM,IAAIlF,EACR,YACA,4CAKES,EAAQP,EAAkBgF,EAAS/E,OAAQ+E,EAASvG,MAC1D,GAAI8B,EACF,MAAMA,EAGR,IAAKyE,EAASvG,KACZ,MAAM,IAAIqB,EAAe,WAAY,sCAGvC,IAAIwF,EAAeN,EAASvG,KAAK9C,KAMjC,QAH4B,IAAjB2J,IACTA,EAAeN,EAASvG,KAAKf,aAEH,IAAjB4H,EAET,MAAM,IAAIxF,EAAe,WAAY,mCAMvC,MAAO,CAAEnE,KAFW6C,EAAO8G,IA2B7BrB,eAAeD,EACbJ,EACAX,EACAtH,EACA+H,OAIMW,EAAO,CAAE1I,KADfA,EAAOiC,EAAOjC,IAId,MAAMwI,QAAgBD,EAAgBN,EAAmBF,GACzDS,EAAQ,gBAAkB,mBAC1BA,EAAgB,OAAI,oBAEpB,IAAIa,EACJ,IACEA,QAAiBpB,EAAkBpB,UAAUS,EAAK,CAChDiC,OAAQ,OACRb,KAAMc,KAAKC,UAAUf,GACrBF,QAAAA,EACAoB,OAAQ7B,MAAAA,OAAA,EAAAA,EAAS6B,SAEnB,MAAO/E,GACP,GAAIA,aAAa9F,OAAoB,eAAX8F,EAAEvF,KAAuB,CACjD,MAAMsF,EAAQ,IAAIT,EAAe,YAAa,0BAC9C,MAAO,CACLnE,KAAMmH,QAAQ4B,OAAOnE,GACrBwD,OAAQ,EACLyB,OAAOC,iBACN,MAAO,CACLC,OACE,OAAO5C,QAAQ4B,OAAOnE,QAWlC,MAAMA,EAAQP,EAAkB,EAAG,MACnC,MAAO,CACLrE,KAAMmH,QAAQ4B,OAAOnE,GAErBwD,OAAQ,EACLyB,OAAOC,iBACN,MAAO,CACLC,OACE,OAAO5C,QAAQ4B,OAAOnE,QAOlC,IAAIoF,EACAC,EACJ,IAAMC,EAAgB,IAAI/C,QAAiB,CAACC,EAAS2B,KACnDiB,EAAiB5C,EACjB6C,EAAiBlB,IAEJ,QAAfoB,EAAApC,MAAAA,OAAO,EAAPA,EAAS6B,cAAM,IAAAO,GAAAA,EAAEC,iBAAiB,QAAS,KACzC,IAAMxF,EAAQ,IAAIT,EAAe,YAAa,0BAC9C8F,EAAerF,KAGjB,MAAMyF,EAyCR,SACEC,EACAN,EACAC,EACAL,GAEA,MAAMW,EAAc,CAClBC,EACAC,KAEA,IAAMC,EAAQF,EAAKE,MAAMjE,GAEzB,GAAKiE,EAAL,CAGM1K,EAAO0K,EAAM,GACnB,IACE,IAAMC,EAAWnB,KAAKoB,MAAM5K,GAC5B,GAAI,WAAY2K,EAEd,YADAX,EAAenH,EAAO8H,EAAS5I,SAGjC,GAAI,YAAa4I,EAEf,YADAF,EAAWI,QAAQhI,EAAO8H,EAASzL,UAGrC,GAAI,UAAWyL,EAAU,CACvB,IAAM/F,EAAQP,EAAkB,EAAGsG,GAGnC,OAFAF,EAAW7F,MAAMA,QACjBqF,EAAerF,IAGjB,MAAOA,GACP,GAAIA,aAAiBT,EAGnB,OAFAsG,EAAW7F,MAAMA,QACjBqF,EAAerF,MAOfkG,EAAU,IAAIC,YACpB,OAAO,IAAIC,eAAe,CACxBC,MAAMR,GACJ,IAAIS,EAAc,GAClB,OACA5C,eAAe6C,IACb,GAAIvB,MAAAA,GAAAA,EAAQwB,QAAS,CACnB,MAAMxG,EAAQ,IAAIT,EAChB,YACA,yBAIF,OAFAsG,EAAW7F,MAAMA,GACjBqF,EAAerF,GACRuC,QAAQC,UAEjB,IACE,KAAM,CAAE7G,MAAAA,EAAO8K,KAAAA,SAAef,EAAOgB,OACrC,GAAID,EAKF,OAJIH,EAAYK,QACdhB,EAAYW,EAAYK,OAAQd,QAElCA,EAAWe,QAGb,GAAI5B,MAAAA,GAAAA,EAAQwB,QAAS,CACnB,MAAMxG,EAAQ,IAAIT,EAChB,YACA,yBAKF,OAHAsG,EAAW7F,MAAMA,GACjBqF,EAAerF,cACT0F,EAAOrB,SAGfiC,GAAeJ,EAAQjI,OAAOtC,EAAO,CAAE6H,QAAQ,IAC/C,MAAMqD,EAAQP,EAAYQ,MAAM,MAChCR,EAAcO,EAAME,OAAS,GAC7B,IAAK,MAAMnB,KAAQiB,EACbjB,EAAKe,QACPhB,EAAYC,EAAKe,OAAQd,GAG7B,OAAOU,IACP,MAAOvG,GACP,MAAMgH,EACJhH,aAAiBT,EACbS,EACAP,EAAkB,EAAG,MAC3BoG,EAAW7F,MAAMgH,GACjB3B,EAAe2B,IA7CZT,IAiDTlC,SACE,OAAOqB,EAAOrB,YAzIF4C,CADDxC,EAASX,KAAMoD,YAG5B9B,EACAC,EACAlC,MAAAA,OAAO,EAAPA,EAAS6B,QAEX,MAAO,CACLxB,OAAQ,EACLyB,OAAOC,iBACN,MAAMiC,EAAU1B,EAAQyB,YACxB,MAAO,CACL/B,aACE,GAAM,CAAExJ,MAAAA,EAAO8K,KAAAA,SAAeU,EAAQT,OACtC,MAAO,CAAE/K,MAAOA,EAAkB8K,KAAAA,IAEpCW,eAEE,aADMD,EAAQ9C,SACP,CAAEoC,MAAM,EAAM9K,WAAOmE,OAKpC1E,KAAMkK,4CCxaM,SAAA+B,EACdhE,EACAiE,EACAC,GAGEzL,EAAqCuH,GDgGrBjB,yBC/FhBkF,KACAC,IA8BY,SAAAC,EAKdnE,EACAX,EACAS,GAEA,OD8FAE,EC7FEvH,EAAqCuH,GD8FvCX,EC7FEA,ED8FFS,EC7FEA,GD+FIG,EAAW,GAGRC,EAAUF,EAAmBX,EAAKtH,EAAM+H,GAAW,KAGnDK,OAAS,CAChBpI,EACA+H,IAEOM,EAAYJ,EAAmBX,EAAKtH,EAAM+H,GAAW,IAEvDG,EArBOkE,IAKdnE,EACAX,EACAS,EAEMG,ERhKNmE,qBACE,IAAIzL,EACFoC,EAtB0C,CAC5CsJ,EACA,CAAEC,mBAAoB3F,MAGtB,IAAMD,EAAM2F,EAAUE,YAAY,OAAOnH,eACnCN,EAAeuH,EAAUE,YAbkB,iBAc3CxH,EAAoBsH,EAAUE,YAVtC,sBAWQvH,EAAmBqH,EAAUE,YAbrC,sBAgBE,OAAO,IAAI9F,EACTC,EACA5B,EACAC,EACAC,EACA2B,IASD,UAACvF,sBAAqB,IAGzBoL,EAAAA,gBAAgBnN,EAAMoN,EAAS7N,GAE/B4N,EAAAA,gBAAgBnN,EAAMoN,EAAS,eUzBzBC,QCfKjG,EAYX1H,YACS2H,EACEhG,GADFtB,KAAGsH,IAAHA,EACEtH,KAASsB,UAATA,EAETtB,KAAKuN,QAAUvN,KAAKsB,UAAUgH,OAC9BtI,KAAKwN,cAAgBxN,KAAKsB,UAAU6G,aAEtCQ,cAAc1I,EAAcyI,GAC1B,OFmDK+E,EACLpM,EEpDwBrB,KAAKsB,WAAWrB,EAAMyI,GAEhDqE,qBACE9E,EACAS,GAEA,OAAOgF,EAAwB1N,KAAKsB,UAAW2G,EAAKS,GAQtDiF,qBAAqBvF,GACnB,IAAMiD,EAAQjD,EAAOiD,MAAM,8CAC3B,GAAa,MAATA,EACF,MAAM,IAAI5L,EACR,YACA,gDAGJ,GAAgB,MAAZ4L,EAAM,GACR,MAAM,IAAI5L,EACR,YACA,6DAGJ,OAAOmO,EAAwB5N,KAAKsB,UAAW+J,EAAM,GAAIxI,OAAOwI,EAAM,KAExEwC,YAAYhB,EAAcC,GACxB,OAAOc,EAAwB5N,KAAKsB,UAAWuL,EAAMC,IDpDzD,MAAM3F,EAAiB,cAEjB2G,EAA+C,CACnDb,EACA,CAAEC,mBAAoB3F,MAGtB,IAAMD,EAAM2F,EAAUE,YAAY,cAAcnH,eAC1C+H,EAAsBd,EAAUE,YAAY,aAAanH,aAAa,CAC1EgI,WAAYzG,MAAAA,EAAAA,EAAwBJ,IAGtC,OAAO,IAAIE,EAAiBC,EAAKyG,IAI3BT,EAAmB,CACvBW,UAAW5G,GAEZ6G,EAA+B,QAACvJ,SAASwJ,kBACxC,IAAI5M,EAAU,mBAAoBuM,EAA8B,UAC7D7L,gBAAgBqL,GAChBtL,sBAAqB,IE1B5BkM,EAAAA,QAASd"}