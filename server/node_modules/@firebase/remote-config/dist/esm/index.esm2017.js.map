{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/client/remote_config_fetch_client.ts", "../../src/constants.ts", "../../src/errors.ts", "../../src/value.ts", "../../src/api.ts", "../../src/client/caching_client.ts", "../../src/language.ts", "../../src/client/rest_client.ts", "../../src/client/retrying_client.ts", "../../src/remote_config.ts", "../../src/storage/storage.ts", "../../src/storage/storage_cache.ts", "../../src/register.ts", "../../src/api2.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Defines a client, as in https://en.wikipedia.org/wiki/Client%E2%80%93server_model, for the\n * Remote Config server (https://firebase.google.com/docs/reference/remote-config/rest).\n *\n * <p>Abstracts throttle, response cache and network implementation details.\n *\n * <p>Modeled after the native {@link GlobalFetch} interface, which is relatively modern and\n * convenient, but simplified for Remote Config's use case.\n *\n * Disambiguation: {@link GlobalFetch} interface and the Remote Config service define \"fetch\"\n * methods. The RestClient uses the former to make HTTP calls. This interface abstracts the latter.\n */\nexport interface RemoteConfigFetchClient {\n  /**\n   * @throws if response status is not 200 or 304.\n   */\n  fetch(request: FetchRequest): Promise<FetchResponse>;\n}\n\n/**\n * Defines a self-descriptive reference for config key-value pairs.\n */\nexport interface FirebaseRemoteConfigObject {\n  [key: string]: string;\n}\n\n/**\n * Shims a minimal AbortSignal.\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class RemoteConfigAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n\n/**\n * Defines per-request inputs for the Remote Config fetch request.\n *\n * <p>Modeled after the native {@link Request} interface, but simplified for Remote Config's\n * use case.\n */\nexport interface FetchRequest {\n  /**\n   * Uses cached config if it is younger than this age.\n   *\n   * <p>Required because it's defined by settings, which always have a value.\n   *\n   * <p>Comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the native\n   * Fetch API.\n   */\n  cacheMaxAgeMillis: number;\n\n  /**\n   * An event bus for the signal to abort a request.\n   *\n   * <p>Required because all requests should be abortable.\n   *\n   * <p>Comparable to the native\n   * Fetch API's \"signal\" field on its request configuration object\n   * https://fetch.spec.whatwg.org/#dom-requestinit-signal.\n   *\n   * <p>Disambiguation: Remote Config commonly refers to API inputs as\n   * \"signals\". See the private ConfigFetchRequestBody interface for those:\n   * http://google3/firebase/remote_config/web/src/core/rest_client.ts?l=14&rcl=255515243.\n   */\n  signal: RemoteConfigAbortSignal;\n\n  /**\n   * The ETag header value from the last response.\n   *\n   * <p>Optional in case this is the first request.\n   *\n   * <p>Comparable to passing `headers = { 'If-None-Match': <eTag> }` to the native Fetch API.\n   */\n  eTag?: string;\n}\n\n/**\n * Defines a successful response (200 or 304).\n *\n * <p>Modeled after the native {@link Response} interface, but simplified for Remote Config's\n * use case.\n */\nexport interface FetchResponse {\n  /**\n   * The HTTP status, which is useful for differentiating success responses with data from\n   * those without.\n   *\n   * <p>{@link RemoteConfigClient} is modeled after the native {@link GlobalFetch} interface, so\n   * HTTP status is first-class.\n   *\n   * <p>Disambiguation: the fetch response returns a legacy \"state\" value that is redundant with the\n   * HTTP status code. The former is normalized into the latter.\n   */\n  status: number;\n\n  /**\n   * Defines the ETag response header value.\n   *\n   * <p>Only defined for 200 and 304 responses.\n   */\n  eTag?: string;\n\n  /**\n   * Defines the map of parameters returned as \"entries\" in the fetch response body.\n   *\n   * <p>Only defined for 200 responses.\n   */\n  config?: FirebaseRemoteConfigObject;\n\n  // Note: we're not extracting experiment metadata until\n  // ABT and Analytics have Web SDKs.\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const RC_COMPONENT_NAME = 'remote-config';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\n\nexport const enum ErrorCode {\n  REGISTRATION_WINDOW = 'registration-window',\n  REGISTRATION_PROJECT_ID = 'registration-project-id',\n  REGISTRATION_API_KEY = 'registration-api-key',\n  REGISTRATION_APP_ID = 'registration-app-id',\n  STORAGE_OPEN = 'storage-open',\n  STORAGE_GET = 'storage-get',\n  STORAGE_SET = 'storage-set',\n  STORAGE_DELETE = 'storage-delete',\n  FETCH_NETWORK = 'fetch-client-network',\n  FETCH_TIMEOUT = 'fetch-timeout',\n  FETCH_THROTTLE = 'fetch-throttle',\n  FETCH_PARSE = 'fetch-client-parse',\n  FETCH_STATUS = 'fetch-status',\n  INDEXED_DB_UNAVAILABLE = 'indexed-db-unavailable'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.REGISTRATION_WINDOW]:\n    'Undefined window object. This SDK only supports usage in a browser environment.',\n  [ErrorCode.REGISTRATION_PROJECT_ID]:\n    'Undefined project identifier. Check Firebase app initialization.',\n  [ErrorCode.REGISTRATION_API_KEY]:\n    'Undefined API key. Check Firebase app initialization.',\n  [ErrorCode.REGISTRATION_APP_ID]:\n    'Undefined app identifier. Check Firebase app initialization.',\n  [ErrorCode.STORAGE_OPEN]:\n    'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_GET]:\n    'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_SET]:\n    'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_DELETE]:\n    'Error thrown when deleting from storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_NETWORK]:\n    'Fetch client failed to connect to a network. Check Internet connection.' +\n    ' Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_TIMEOUT]:\n    'The config fetch request timed out. ' +\n    ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.',\n  [ErrorCode.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [ErrorCode.FETCH_PARSE]:\n    'Fetch client could not parse response.' +\n    ' Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_STATUS]:\n    'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [ErrorCode.INDEXED_DB_UNAVAILABLE]:\n    'Indexed DB is not supported by current browser'\n};\n\n// Note this is effectively a type system binding a code to params. This approach overlaps with the\n// role of TS interfaces, but works well for a few reasons:\n// 1) JS is unaware of TS interfaces, eg we can't test for interface implementation in JS\n// 2) callers should have access to a human-readable summary of the error and this interpolates\n//    params into an error message;\n// 3) callers should be able to programmatically access data associated with an error, which\n//    ErrorData provides.\ninterface ErrorParams {\n  [ErrorCode.STORAGE_OPEN]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_GET]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_SET]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_DELETE]: { originalErrorMessage: string | undefined };\n  [ErrorCode.FETCH_NETWORK]: { originalErrorMessage: string };\n  [ErrorCode.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [ErrorCode.FETCH_PARSE]: { originalErrorMessage: string };\n  [ErrorCode.FETCH_STATUS]: { httpStatus: number };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'remoteconfig' /* service */,\n  'Remote Config' /* service name */,\n  ERROR_DESCRIPTION_MAP\n);\n\n// Note how this is like typeof/instanceof, but for ErrorCode.\nexport function hasErrorCode(e: Error, errorCode: ErrorCode): boolean {\n  return e instanceof FirebaseError && e.code.indexOf(errorCode) !== -1;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Value as ValueType, ValueSource } from '@firebase/remote-config-types';\n\nconst DEFAULT_VALUE_FOR_BOOLEAN = false;\nconst DEFAULT_VALUE_FOR_STRING = '';\nconst DEFAULT_VALUE_FOR_NUMBER = 0;\n\nconst BOOLEAN_TRUTHY_VALUES = ['1', 'true', 't', 'yes', 'y', 'on'];\n\nexport class Value implements ValueType {\n  constructor(\n    private readonly _source: ValueSource,\n    private readonly _value: string = DEFAULT_VALUE_FOR_STRING\n  ) {}\n\n  asString(): string {\n    return this._value;\n  }\n\n  asBoolean(): boolean {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_BOOLEAN;\n    }\n    return BOOLEAN_TRUTHY_VALUES.indexOf(this._value.toLowerCase()) >= 0;\n  }\n\n  asNumber(): number {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_NUMBER;\n    }\n    let num = Number(this._value);\n    if (isNaN(num)) {\n      num = DEFAULT_VALUE_FOR_NUMBER;\n    }\n    return num;\n  }\n\n  getSource(): ValueSource {\n    return this._source;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport {\n  LogLevel as RemoteConfigLogLevel,\n  RemoteConfig,\n  Value\n} from './public_types';\nimport { RemoteConfigAbortSignal } from './client/remote_config_fetch_client';\nimport { RC_COMPONENT_NAME } from './constants';\nimport { ErrorCode, hasErrorCode } from './errors';\nimport { RemoteConfig as RemoteConfigImpl } from './remote_config';\nimport { Value as ValueImpl } from './value';\nimport { LogLevel as FirebaseLogLevel } from '@firebase/logger';\nimport { getModularInstance } from '@firebase/util';\n\n/**\n *\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n * @returns A {@link RemoteConfig} instance.\n *\n * @public\n */\nexport function getRemoteConfig(app: FirebaseApp = getApp()): RemoteConfig {\n  app = getModularInstance(app);\n  const rcProvider = _getProvider(app, RC_COMPONENT_NAME);\n  return rcProvider.getImmediate();\n}\n\n/**\n * Makes the last fetched config available to the getters.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\n * If the fetched configs were already activated, the `Promise` will resolve to false.\n *\n * @public\n */\nexport async function activate(remoteConfig: RemoteConfig): Promise<boolean> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  const [lastSuccessfulFetchResponse, activeConfigEtag] = await Promise.all([\n    rc._storage.getLastSuccessfulFetchResponse(),\n    rc._storage.getActiveConfigEtag()\n  ]);\n  if (\n    !lastSuccessfulFetchResponse ||\n    !lastSuccessfulFetchResponse.config ||\n    !lastSuccessfulFetchResponse.eTag ||\n    lastSuccessfulFetchResponse.eTag === activeConfigEtag\n  ) {\n    // Either there is no successful fetched config, or is the same as current active\n    // config.\n    return false;\n  }\n  await Promise.all([\n    rc._storageCache.setActiveConfig(lastSuccessfulFetchResponse.config),\n    rc._storage.setActiveConfigEtag(lastSuccessfulFetchResponse.eTag)\n  ]);\n  return true;\n}\n\n/**\n * Ensures the last activated config are available to the getters.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n *\n * @returns A `Promise` that resolves when the last activated config is available to the getters.\n * @public\n */\nexport function ensureInitialized(remoteConfig: RemoteConfig): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (!rc._initializePromise) {\n    rc._initializePromise = rc._storageCache.loadFromStorage().then(() => {\n      rc._isInitializationComplete = true;\n    });\n  }\n  return rc._initializePromise;\n}\n\n/**\n * Fetches and caches configuration from the Remote Config service.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @public\n */\nexport async function fetchConfig(remoteConfig: RemoteConfig): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  // Aborts the request after the given timeout, causing the fetch call to\n  // reject with an `AbortError`.\n  //\n  // <p>Aborting after the request completes is a no-op, so we don't need a\n  // corresponding `clearTimeout`.\n  //\n  // Locating abort logic here because:\n  // * it uses a developer setting (timeout)\n  // * it applies to all retries (like curl's max-time arg)\n  // * it is consistent with the Fetch API's signal input\n  const abortSignal = new RemoteConfigAbortSignal();\n\n  setTimeout(async () => {\n    // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n    abortSignal.abort();\n  }, rc.settings.fetchTimeoutMillis);\n\n  // Catches *all* errors thrown by client so status can be set consistently.\n  try {\n    await rc._client.fetch({\n      cacheMaxAgeMillis: rc.settings.minimumFetchIntervalMillis,\n      signal: abortSignal\n    });\n\n    await rc._storageCache.setLastFetchStatus('success');\n  } catch (e) {\n    const lastFetchStatus = hasErrorCode(e as Error, ErrorCode.FETCH_THROTTLE)\n      ? 'throttle'\n      : 'failure';\n    await rc._storageCache.setLastFetchStatus(lastFetchStatus);\n    throw e;\n  }\n}\n\n/**\n * Gets all config.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @returns All config.\n *\n * @public\n */\nexport function getAll(remoteConfig: RemoteConfig): Record<string, Value> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  return getAllKeys(\n    rc._storageCache.getActiveConfig(),\n    rc.defaultConfig\n  ).reduce((allConfigs, key) => {\n    allConfigs[key] = getValue(remoteConfig, key);\n    return allConfigs;\n  }, {} as Record<string, Value>);\n}\n\n/**\n * Gets the value for the given key as a boolean.\n *\n * Convenience method for calling <code>remoteConfig.getValue(key).asBoolean()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a boolean.\n * @public\n */\nexport function getBoolean(remoteConfig: RemoteConfig, key: string): boolean {\n  return getValue(getModularInstance(remoteConfig), key).asBoolean();\n}\n\n/**\n * Gets the value for the given key as a number.\n *\n * Convenience method for calling <code>remoteConfig.getValue(key).asNumber()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a number.\n *\n * @public\n */\nexport function getNumber(remoteConfig: RemoteConfig, key: string): number {\n  return getValue(getModularInstance(remoteConfig), key).asNumber();\n}\n\n/**\n * Gets the value for the given key as a string.\n * Convenience method for calling <code>remoteConfig.getValue(key).asString()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a string.\n *\n * @public\n */\nexport function getString(remoteConfig: RemoteConfig, key: string): string {\n  return getValue(getModularInstance(remoteConfig), key).asString();\n}\n\n/**\n * Gets the {@link Value} for the given key.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key.\n *\n * @public\n */\nexport function getValue(remoteConfig: RemoteConfig, key: string): Value {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (!rc._isInitializationComplete) {\n    rc._logger.debug(\n      `A value was requested for key \"${key}\" before SDK initialization completed.` +\n        ' Await on ensureInitialized if the intent was to get a previously activated value.'\n    );\n  }\n  const activeConfig = rc._storageCache.getActiveConfig();\n  if (activeConfig && activeConfig[key] !== undefined) {\n    return new ValueImpl('remote', activeConfig[key]);\n  } else if (rc.defaultConfig && rc.defaultConfig[key] !== undefined) {\n    return new ValueImpl('default', String(rc.defaultConfig[key]));\n  }\n  rc._logger.debug(\n    `Returning static value for key \"${key}\".` +\n      ' Define a default or remote value if this is unintentional.'\n  );\n  return new ValueImpl('static');\n}\n\n/**\n * Defines the log level to use.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param logLevel - The log level to set.\n *\n * @public\n */\nexport function setLogLevel(\n  remoteConfig: RemoteConfig,\n  logLevel: RemoteConfigLogLevel\n): void {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  switch (logLevel) {\n    case 'debug':\n      rc._logger.logLevel = FirebaseLogLevel.DEBUG;\n      break;\n    case 'silent':\n      rc._logger.logLevel = FirebaseLogLevel.SILENT;\n      break;\n    default:\n      rc._logger.logLevel = FirebaseLogLevel.ERROR;\n  }\n}\n\n/**\n * Dedupes and returns an array of all the keys of the received objects.\n */\nfunction getAllKeys(obj1: {} = {}, obj2: {} = {}): string[] {\n  return Object.keys({ ...obj1, ...obj2 });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { StorageCache } from '../storage/storage_cache';\nimport {\n  FetchResponse,\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { Storage } from '../storage/storage';\nimport { Logger } from '@firebase/logger';\n\n/**\n * Implements the {@link RemoteConfigClient} abstraction with success response caching.\n *\n * <p>Comparable to the browser's Cache API for responses, but the Cache API requires a Service\n * Worker, which requires HTTPS, which would significantly complicate SDK installation. Also, the\n * Cache API doesn't support matching entries by time.\n */\nexport class CachingClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly client: RemoteConfigFetchClient,\n    private readonly storage: Storage,\n    private readonly storageCache: StorageCache,\n    private readonly logger: Logger\n  ) {}\n\n  /**\n   * Returns true if the age of the cached fetched configs is less than or equal to\n   * {@link Settings#minimumFetchIntervalInSeconds}.\n   *\n   * <p>This is comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the\n   * native Fetch API.\n   *\n   * <p>Visible for testing.\n   */\n  isCachedDataFresh(\n    cacheMaxAgeMillis: number,\n    lastSuccessfulFetchTimestampMillis: number | undefined\n  ): boolean {\n    // Cache can only be fresh if it's populated.\n    if (!lastSuccessfulFetchTimestampMillis) {\n      this.logger.debug('Config fetch cache check. Cache unpopulated.');\n      return false;\n    }\n\n    // Calculates age of cache entry.\n    const cacheAgeMillis = Date.now() - lastSuccessfulFetchTimestampMillis;\n\n    const isCachedDataFresh = cacheAgeMillis <= cacheMaxAgeMillis;\n\n    this.logger.debug(\n      'Config fetch cache check.' +\n        ` Cache age millis: ${cacheAgeMillis}.` +\n        ` Cache max age millis (minimumFetchIntervalMillis setting): ${cacheMaxAgeMillis}.` +\n        ` Is cache hit: ${isCachedDataFresh}.`\n    );\n\n    return isCachedDataFresh;\n  }\n\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    // Reads from persisted storage to avoid cache miss if callers don't wait on initialization.\n    const [lastSuccessfulFetchTimestampMillis, lastSuccessfulFetchResponse] =\n      await Promise.all([\n        this.storage.getLastSuccessfulFetchTimestampMillis(),\n        this.storage.getLastSuccessfulFetchResponse()\n      ]);\n\n    // Exits early on cache hit.\n    if (\n      lastSuccessfulFetchResponse &&\n      this.isCachedDataFresh(\n        request.cacheMaxAgeMillis,\n        lastSuccessfulFetchTimestampMillis\n      )\n    ) {\n      return lastSuccessfulFetchResponse;\n    }\n\n    // Deviates from pure decorator by not honoring a passed ETag since we don't have a public API\n    // that allows the caller to pass an ETag.\n    request.eTag =\n      lastSuccessfulFetchResponse && lastSuccessfulFetchResponse.eTag;\n\n    // Falls back to service on cache miss.\n    const response = await this.client.fetch(request);\n\n    // Fetch throws for non-success responses, so success is guaranteed here.\n\n    const storageOperations = [\n      // Uses write-through cache for consistency with synchronous public API.\n      this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())\n    ];\n\n    if (response.status === 200) {\n      // Caches response only if it has changed, ie non-304 responses.\n      storageOperations.push(\n        this.storage.setLastSuccessfulFetchResponse(response)\n      );\n    }\n\n    await Promise.all(storageOperations);\n\n    return response;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Attempts to get the most accurate browser language setting.\n *\n * <p>Adapted from getUserLanguage in packages/auth/src/utils.js for TypeScript.\n *\n * <p>Defers default language specification to server logic for consistency.\n *\n * @param navigatorLanguage Enables tests to override read-only {@link NavigatorLanguage}.\n */\nexport function getUserLanguage(\n  navigatorLanguage: NavigatorLanguage = navigator\n): string {\n  return (\n    // Most reliable, but only supported in Chrome/Firefox.\n    (navigatorLanguage.languages && navigatorLanguage.languages[0]) ||\n    // Supported in most browsers, but returns the language of the browser\n    // UI, not the language set in browser settings.\n    navigatorLanguage.language\n    // Polyfill otherwise.\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FetchResponse,\n  RemoteConfigFetchClient,\n  FirebaseRemoteConfigObject,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { ERROR_FACTORY, ErrorCode } from '../errors';\nimport { getUserLanguage } from '../language';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\n/**\n * Defines request body parameters required to call the fetch API:\n * https://firebase.google.com/docs/reference/remote-config/rest\n *\n * <p>Not exported because this file encapsulates REST API specifics.\n *\n * <p>Not passing User Properties because Analytics' source of truth on Web is server-side.\n */\ninterface FetchRequestBody {\n  // Disables camelcase linting for request body params.\n  /* eslint-disable camelcase*/\n  sdk_version: string;\n  app_instance_id: string;\n  app_instance_id_token: string;\n  app_id: string;\n  language_code: string;\n  /* eslint-enable camelcase */\n}\n\n/**\n * Implements the Client abstraction for the Remote Config REST API.\n */\nexport class RestClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly firebaseInstallations: _FirebaseInstallationsInternal,\n    private readonly sdkVersion: string,\n    private readonly namespace: string,\n    private readonly projectId: string,\n    private readonly apiKey: string,\n    private readonly appId: string\n  ) {}\n\n  /**\n   * Fetches from the Remote Config REST API.\n   *\n   * @throws a {@link ErrorCode.FETCH_NETWORK} error if {@link GlobalFetch#fetch} can't\n   * connect to the network.\n   * @throws a {@link ErrorCode.FETCH_PARSE} error if {@link Response#json} can't parse the\n   * fetch response.\n   * @throws a {@link ErrorCode.FETCH_STATUS} error if the service returns an HTTP error status.\n   */\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    const [installationId, installationToken] = await Promise.all([\n      this.firebaseInstallations.getId(),\n      this.firebaseInstallations.getToken()\n    ]);\n\n    const urlBase =\n      window.FIREBASE_REMOTE_CONFIG_URL_BASE ||\n      'https://firebaseremoteconfig.googleapis.com';\n\n    const url = `${urlBase}/v1/projects/${this.projectId}/namespaces/${this.namespace}:fetch?key=${this.apiKey}`;\n\n    const headers = {\n      'Content-Type': 'application/json',\n      'Content-Encoding': 'gzip',\n      // Deviates from pure decorator by not passing max-age header since we don't currently have\n      // service behavior using that header.\n      'If-None-Match': request.eTag || '*'\n    };\n\n    const requestBody: FetchRequestBody = {\n      /* eslint-disable camelcase */\n      sdk_version: this.sdkVersion,\n      app_instance_id: installationId,\n      app_instance_id_token: installationToken,\n      app_id: this.appId,\n      language_code: getUserLanguage()\n      /* eslint-enable camelcase */\n    };\n\n    const options = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(requestBody)\n    };\n\n    // This logic isn't REST-specific, but shimming abort logic isn't worth another decorator.\n    const fetchPromise = fetch(url, options);\n    const timeoutPromise = new Promise((_resolve, reject) => {\n      // Maps async event listener to Promise API.\n      request.signal.addEventListener(() => {\n        // Emulates https://heycam.github.io/webidl/#aborterror\n        const error = new Error('The operation was aborted.');\n        error.name = 'AbortError';\n        reject(error);\n      });\n    });\n\n    let response;\n    try {\n      await Promise.race([fetchPromise, timeoutPromise]);\n      response = await fetchPromise;\n    } catch (originalError) {\n      let errorCode = ErrorCode.FETCH_NETWORK;\n      if ((originalError as Error)?.name === 'AbortError') {\n        errorCode = ErrorCode.FETCH_TIMEOUT;\n      }\n      throw ERROR_FACTORY.create(errorCode, {\n        originalErrorMessage: (originalError as Error)?.message\n      });\n    }\n\n    let status = response.status;\n\n    // Normalizes nullable header to optional.\n    const responseEtag = response.headers.get('ETag') || undefined;\n\n    let config: FirebaseRemoteConfigObject | undefined;\n    let state: string | undefined;\n\n    // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n    // Requesting application/json and checking for a 200 ensures there's JSON data.\n    if (response.status === 200) {\n      let responseBody;\n      try {\n        responseBody = await response.json();\n      } catch (originalError) {\n        throw ERROR_FACTORY.create(ErrorCode.FETCH_PARSE, {\n          originalErrorMessage: (originalError as Error)?.message\n        });\n      }\n      config = responseBody['entries'];\n      state = responseBody['state'];\n    }\n\n    // Normalizes based on legacy state.\n    if (state === 'INSTANCE_STATE_UNSPECIFIED') {\n      status = 500;\n    } else if (state === 'NO_CHANGE') {\n      status = 304;\n    } else if (state === 'NO_TEMPLATE' || state === 'EMPTY_CONFIG') {\n      // These cases can be fixed remotely, so normalize to safe value.\n      config = {};\n    }\n\n    // Normalize to exception-based control flow for non-success cases.\n    // Encapsulates HTTP specifics in this class as much as possible. Status is still the best for\n    // differentiating success states (200 from 304; the state body param is undefined in a\n    // standard 304).\n    if (status !== 304 && status !== 200) {\n      throw ERROR_FACTORY.create(ErrorCode.FETCH_STATUS, {\n        httpStatus: status\n      });\n    }\n\n    return { status, eTag: responseEtag, config };\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  RemoteConfigAbortSignal,\n  RemoteConfigFetchClient,\n  FetchResponse,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { ThrottleMetadata, Storage } from '../storage/storage';\nimport { ErrorCode, ERROR_FACTORY } from '../errors';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nexport function setAbortableTimeout(\n  signal: RemoteConfigAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(ErrorCode.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Decorates a Client with retry logic.\n *\n * <p>Comparable to CachingClient, but uses backoff logic instead of cache max age and doesn't cache\n * responses (because the SDK has no use for error responses).\n */\nexport class RetryingClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly client: RemoteConfigFetchClient,\n    private readonly storage: Storage\n  ) {}\n\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    const throttleMetadata = (await this.storage.getThrottleMetadata()) || {\n      backoffCount: 0,\n      throttleEndTimeMillis: Date.now()\n    };\n\n    return this.attemptFetch(request, throttleMetadata);\n  }\n\n  /**\n   * A recursive helper for attempting a fetch request repeatedly.\n   *\n   * @throws any non-retriable errors.\n   */\n  async attemptFetch(\n    request: FetchRequest,\n    { throttleEndTimeMillis, backoffCount }: ThrottleMetadata\n  ): Promise<FetchResponse> {\n    // Starts with a (potentially zero) timeout to support resumption from stored state.\n    // Ensures the throttle end time is honored if the last attempt timed out.\n    // Note the SDK will never make a request if the fetch timeout expires at this point.\n    await setAbortableTimeout(request.signal, throttleEndTimeMillis);\n\n    try {\n      const response = await this.client.fetch(request);\n\n      // Note the SDK only clears throttle state if response is success or non-retriable.\n      await this.storage.deleteThrottleMetadata();\n\n      return response;\n    } catch (e) {\n      if (!isRetriableError(e as Error)) {\n        throw e;\n      }\n\n      // Increments backoff state.\n      const throttleMetadata = {\n        throttleEndTimeMillis:\n          Date.now() + calculateBackoffMillis(backoffCount),\n        backoffCount: backoffCount + 1\n      };\n\n      // Persists state.\n      await this.storage.setThrottleMetadata(throttleMetadata);\n\n      return this.attemptFetch(request, throttleMetadata);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  RemoteConfig as RemoteConfigType,\n  FetchStatus,\n  RemoteConfigSettings\n} from './public_types';\nimport { StorageCache } from './storage/storage_cache';\nimport { RemoteConfigFetchClient } from './client/remote_config_fetch_client';\nimport { Storage } from './storage/storage';\nimport { Logger } from '@firebase/logger';\n\nconst DEFAULT_FETCH_TIMEOUT_MILLIS = 60 * 1000; // One minute\nconst DEFAULT_CACHE_MAX_AGE_MILLIS = 12 * 60 * 60 * 1000; // Twelve hours.\n\n/**\n * Encapsulates business logic mapping network and storage dependencies to the public SDK API.\n *\n * See {@link https://github.com/firebase/firebase-js-sdk/blob/main/packages/firebase/index.d.ts|interface documentation} for method descriptions.\n */\nexport class RemoteConfig implements RemoteConfigType {\n  /**\n   * Tracks completion of initialization promise.\n   * @internal\n   */\n  _isInitializationComplete = false;\n\n  /**\n   * De-duplicates initialization calls.\n   * @internal\n   */\n  _initializePromise?: Promise<void>;\n\n  settings: RemoteConfigSettings = {\n    fetchTimeoutMillis: DEFAULT_FETCH_TIMEOUT_MILLIS,\n    minimumFetchIntervalMillis: DEFAULT_CACHE_MAX_AGE_MILLIS\n  };\n\n  defaultConfig: { [key: string]: string | number | boolean } = {};\n\n  get fetchTimeMillis(): number {\n    return this._storageCache.getLastSuccessfulFetchTimestampMillis() || -1;\n  }\n\n  get lastFetchStatus(): FetchStatus {\n    return this._storageCache.getLastFetchStatus() || 'no-fetch-yet';\n  }\n\n  constructor(\n    // Required by FirebaseServiceFactory interface.\n    readonly app: FirebaseApp,\n    // JS doesn't support private yet\n    // (https://github.com/tc39/proposal-class-fields#private-fields), so we hint using an\n    // underscore prefix.\n    /**\n     * @internal\n     */\n    readonly _client: RemoteConfigFetchClient,\n    /**\n     * @internal\n     */\n    readonly _storageCache: StorageCache,\n    /**\n     * @internal\n     */\n    readonly _storage: Storage,\n    /**\n     * @internal\n     */\n    readonly _logger: Logger\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchStatus } from '@firebase/remote-config-types';\nimport {\n  FetchResponse,\n  FirebaseRemoteConfigObject\n} from '../client/remote_config_fetch_client';\nimport { ERROR_FACTORY, ErrorCode } from '../errors';\nimport { FirebaseError } from '@firebase/util';\n\n/**\n * Converts an error event associated with a {@link IDBRequest} to a {@link FirebaseError}.\n */\nfunction toFirebaseError(event: Event, errorCode: ErrorCode): FirebaseError {\n  const originalError = (event.target as IDBRequest).error || undefined;\n  return ERROR_FACTORY.create(errorCode, {\n    originalErrorMessage: originalError && (originalError as Error)?.message\n  });\n}\n\n/**\n * A general-purpose store keyed by app + namespace + {@link\n * ProjectNamespaceKeyFieldValue}.\n *\n * <p>The Remote Config SDK can be used with multiple app installations, and each app can interact\n * with multiple namespaces, so this store uses app (ID + name) and namespace as common parent keys\n * for a set of key-value pairs. See {@link Storage#createCompositeKey}.\n *\n * <p>Visible for testing.\n */\nexport const APP_NAMESPACE_STORE = 'app_namespace_store';\n\nconst DB_NAME = 'firebase_remote_config';\nconst DB_VERSION = 1;\n\n/**\n * Encapsulates metadata concerning throttled fetch requests.\n */\nexport interface ThrottleMetadata {\n  // The number of times fetch has backed off. Used for resuming backoff after a timeout.\n  backoffCount: number;\n  // The Unix timestamp in milliseconds when callers can retry a request.\n  throttleEndTimeMillis: number;\n}\n\n/**\n * Provides type-safety for the \"key\" field used by {@link APP_NAMESPACE_STORE}.\n *\n * <p>This seems like a small price to avoid potentially subtle bugs caused by a typo.\n */\ntype ProjectNamespaceKeyFieldValue =\n  | 'active_config'\n  | 'active_config_etag'\n  | 'last_fetch_status'\n  | 'last_successful_fetch_timestamp_millis'\n  | 'last_successful_fetch_response'\n  | 'settings'\n  | 'throttle_metadata';\n\n// Visible for testing.\nexport function openDatabase(): Promise<IDBDatabase> {\n  return new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n      request.onerror = event => {\n        reject(toFirebaseError(event, ErrorCode.STORAGE_OPEN));\n      };\n      request.onsuccess = event => {\n        resolve((event.target as IDBOpenDBRequest).result);\n      };\n      request.onupgradeneeded = event => {\n        const db = (event.target as IDBOpenDBRequest).result;\n\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(APP_NAMESPACE_STORE, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (error) {\n      reject(\n        ERROR_FACTORY.create(ErrorCode.STORAGE_OPEN, {\n          originalErrorMessage: (error as Error)?.message\n        })\n      );\n    }\n  });\n}\n\n/**\n * Abstracts data persistence.\n */\nexport class Storage {\n  /**\n   * @param appId enables storage segmentation by app (ID + name).\n   * @param appName enables storage segmentation by app (ID + name).\n   * @param namespace enables storage segmentation by namespace.\n   */\n  constructor(\n    private readonly appId: string,\n    private readonly appName: string,\n    private readonly namespace: string,\n    private readonly openDbPromise = openDatabase()\n  ) {}\n\n  getLastFetchStatus(): Promise<FetchStatus | undefined> {\n    return this.get<FetchStatus>('last_fetch_status');\n  }\n\n  setLastFetchStatus(status: FetchStatus): Promise<void> {\n    return this.set<FetchStatus>('last_fetch_status', status);\n  }\n\n  // This is comparable to a cache entry timestamp. If we need to expire other data, we could\n  // consider adding timestamp to all storage records and an optional max age arg to getters.\n  getLastSuccessfulFetchTimestampMillis(): Promise<number | undefined> {\n    return this.get<number>('last_successful_fetch_timestamp_millis');\n  }\n\n  setLastSuccessfulFetchTimestampMillis(timestamp: number): Promise<void> {\n    return this.set<number>(\n      'last_successful_fetch_timestamp_millis',\n      timestamp\n    );\n  }\n\n  getLastSuccessfulFetchResponse(): Promise<FetchResponse | undefined> {\n    return this.get<FetchResponse>('last_successful_fetch_response');\n  }\n\n  setLastSuccessfulFetchResponse(response: FetchResponse): Promise<void> {\n    return this.set<FetchResponse>('last_successful_fetch_response', response);\n  }\n\n  getActiveConfig(): Promise<FirebaseRemoteConfigObject | undefined> {\n    return this.get<FirebaseRemoteConfigObject>('active_config');\n  }\n\n  setActiveConfig(config: FirebaseRemoteConfigObject): Promise<void> {\n    return this.set<FirebaseRemoteConfigObject>('active_config', config);\n  }\n\n  getActiveConfigEtag(): Promise<string | undefined> {\n    return this.get<string>('active_config_etag');\n  }\n\n  setActiveConfigEtag(etag: string): Promise<void> {\n    return this.set<string>('active_config_etag', etag);\n  }\n\n  getThrottleMetadata(): Promise<ThrottleMetadata | undefined> {\n    return this.get<ThrottleMetadata>('throttle_metadata');\n  }\n\n  setThrottleMetadata(metadata: ThrottleMetadata): Promise<void> {\n    return this.set<ThrottleMetadata>('throttle_metadata', metadata);\n  }\n\n  deleteThrottleMetadata(): Promise<void> {\n    return this.delete('throttle_metadata');\n  }\n\n  async get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T | undefined> {\n    const db = await this.openDbPromise;\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction([APP_NAMESPACE_STORE], 'readonly');\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.get(compositeKey);\n        request.onerror = event => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_GET));\n        };\n        request.onsuccess = event => {\n          const result = (event.target as IDBRequest).result;\n          if (result) {\n            resolve(result.value);\n          } else {\n            resolve(undefined);\n          }\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_GET, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  async set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void> {\n    const db = await this.openDbPromise;\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.put({\n          compositeKey,\n          value\n        });\n        request.onerror = (event: Event) => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_SET));\n        };\n        request.onsuccess = () => {\n          resolve();\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_SET, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  async delete(key: ProjectNamespaceKeyFieldValue): Promise<void> {\n    const db = await this.openDbPromise;\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.delete(compositeKey);\n        request.onerror = (event: Event) => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_DELETE));\n        };\n        request.onsuccess = () => {\n          resolve();\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_DELETE, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  // Facilitates composite key functionality (which is unsupported in IE).\n  createCompositeKey(key: ProjectNamespaceKeyFieldValue): string {\n    return [this.appId, this.appName, this.namespace, key].join();\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchStatus } from '@firebase/remote-config-types';\nimport { FirebaseRemoteConfigObject } from '../client/remote_config_fetch_client';\nimport { Storage } from './storage';\n\n/**\n * A memory cache layer over storage to support the SDK's synchronous read requirements.\n */\nexport class StorageCache {\n  constructor(private readonly storage: Storage) {}\n\n  /**\n   * Memory caches.\n   */\n  private lastFetchStatus?: FetchStatus;\n  private lastSuccessfulFetchTimestampMillis?: number;\n  private activeConfig?: FirebaseRemoteConfigObject;\n\n  /**\n   * Memory-only getters\n   */\n  getLastFetchStatus(): FetchStatus | undefined {\n    return this.lastFetchStatus;\n  }\n\n  getLastSuccessfulFetchTimestampMillis(): number | undefined {\n    return this.lastSuccessfulFetchTimestampMillis;\n  }\n\n  getActiveConfig(): FirebaseRemoteConfigObject | undefined {\n    return this.activeConfig;\n  }\n\n  /**\n   * Read-ahead getter\n   */\n  async loadFromStorage(): Promise<void> {\n    const lastFetchStatusPromise = this.storage.getLastFetchStatus();\n    const lastSuccessfulFetchTimestampMillisPromise =\n      this.storage.getLastSuccessfulFetchTimestampMillis();\n    const activeConfigPromise = this.storage.getActiveConfig();\n\n    // Note:\n    // 1. we consistently check for undefined to avoid clobbering defined values\n    //   in memory\n    // 2. we defer awaiting to improve readability, as opposed to destructuring\n    //   a Promise.all result, for example\n\n    const lastFetchStatus = await lastFetchStatusPromise;\n    if (lastFetchStatus) {\n      this.lastFetchStatus = lastFetchStatus;\n    }\n\n    const lastSuccessfulFetchTimestampMillis =\n      await lastSuccessfulFetchTimestampMillisPromise;\n    if (lastSuccessfulFetchTimestampMillis) {\n      this.lastSuccessfulFetchTimestampMillis =\n        lastSuccessfulFetchTimestampMillis;\n    }\n\n    const activeConfig = await activeConfigPromise;\n    if (activeConfig) {\n      this.activeConfig = activeConfig;\n    }\n  }\n\n  /**\n   * Write-through setters\n   */\n  setLastFetchStatus(status: FetchStatus): Promise<void> {\n    this.lastFetchStatus = status;\n    return this.storage.setLastFetchStatus(status);\n  }\n\n  setLastSuccessfulFetchTimestampMillis(\n    timestampMillis: number\n  ): Promise<void> {\n    this.lastSuccessfulFetchTimestampMillis = timestampMillis;\n    return this.storage.setLastSuccessfulFetchTimestampMillis(timestampMillis);\n  }\n\n  setActiveConfig(activeConfig: FirebaseRemoteConfigObject): Promise<void> {\n    this.activeConfig = activeConfig;\n    return this.storage.setActiveConfig(activeConfig);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { isIndexedDBAvailable } from '@firebase/util';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { Logger, LogLevel as FirebaseLogLevel } from '@firebase/logger';\nimport { RemoteConfig } from './public_types';\nimport { name as packageName, version } from '../package.json';\nimport { ensureInitialized } from './api';\nimport { CachingClient } from './client/caching_client';\nimport { RestClient } from './client/rest_client';\nimport { RetryingClient } from './client/retrying_client';\nimport { RC_COMPONENT_NAME } from './constants';\nimport { ErrorCode, ERROR_FACTORY } from './errors';\nimport { RemoteConfig as RemoteConfigImpl } from './remote_config';\nimport { Storage } from './storage/storage';\nimport { StorageCache } from './storage/storage_cache';\n// This needs to be in the same file that calls `getProvider()` on the component\n// or it will get tree-shaken out.\nimport '@firebase/installations';\n\nexport function registerRemoteConfig(): void {\n  _registerComponent(\n    new Component(\n      RC_COMPONENT_NAME,\n      remoteConfigFactory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(packageName, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(packageName, version, '__BUILD_TARGET__');\n\n  function remoteConfigFactory(\n    container: ComponentContainer,\n    { instanceIdentifier: namespace }: InstanceFactoryOptions\n  ): RemoteConfig {\n    /* Dependencies */\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    // The following call will always succeed because rc has `import '@firebase/installations'`\n    const installations = container\n      .getProvider('installations-internal')\n      .getImmediate();\n\n    // Guards against the SDK being used in non-browser environments.\n    if (typeof window === 'undefined') {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_WINDOW);\n    }\n    // Guards against the SDK being used when indexedDB is not available.\n    if (!isIndexedDBAvailable()) {\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNAVAILABLE);\n    }\n    // Normalizes optional inputs.\n    const { projectId, apiKey, appId } = app.options;\n    if (!projectId) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_PROJECT_ID);\n    }\n    if (!apiKey) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_API_KEY);\n    }\n    if (!appId) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_APP_ID);\n    }\n    namespace = namespace || 'firebase';\n\n    const storage = new Storage(appId, app.name, namespace);\n    const storageCache = new StorageCache(storage);\n\n    const logger = new Logger(packageName);\n\n    // Sets ERROR as the default log level.\n    // See RemoteConfig#setLogLevel for corresponding normalization to ERROR log level.\n    logger.logLevel = FirebaseLogLevel.ERROR;\n\n    const restClient = new RestClient(\n      installations,\n      // Uses the JS SDK version, by which the RC package version can be deduced, if necessary.\n      SDK_VERSION,\n      namespace,\n      projectId,\n      apiKey,\n      appId\n    );\n    const retryingClient = new RetryingClient(restClient, storage);\n    const cachingClient = new CachingClient(\n      retryingClient,\n      storage,\n      storageCache,\n      logger\n    );\n\n    const remoteConfigInstance = new RemoteConfigImpl(\n      app,\n      cachingClient,\n      storageCache,\n      storage,\n      logger\n    );\n\n    // Starts warming cache.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    ensureInitialized(remoteConfigInstance);\n\n    return remoteConfigInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RemoteConfig } from './public_types';\nimport { activate, fetchConfig } from './api';\nimport {\n  getModularInstance,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n// This API is put in a separate file, so we can stub fetchConfig and activate in tests.\n// It's not possible to stub standalone functions from the same module.\n/**\n *\n * Performs fetch and activate operations, as a convenience.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n *\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\n * If the fetched configs were already activated, the `Promise` will resolve to false.\n *\n * @public\n */\nexport async function fetchAndActivate(\n  remoteConfig: RemoteConfig\n): Promise<boolean> {\n  remoteConfig = getModularInstance(remoteConfig);\n  await fetchConfig(remoteConfig);\n  return activate(remoteConfig);\n}\n\n/**\n * This method provides two different checks:\n *\n * 1. Check if IndexedDB exists in the browser environment.\n * 2. Check if the current browser context allows IndexedDB `open()` calls.\n *\n * @returns A `Promise` which resolves to true if a {@link RemoteConfig} instance\n * can be initialized in this environment, or false if it cannot.\n * @public\n */\nexport async function isSupported(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n", "/**\n * The Firebase Remote Config Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerRemoteConfig } from './register';\n\n// Facilitates debugging by enabling settings changes without rebuilding asset.\n// Note these debug options are not part of a documented, supported API and can change at any time.\n// Consolidates debug options for easier discovery.\n// Uses transient variables on window to avoid lingering state causing panic.\ndeclare global {\n  interface Window {\n    FIREBASE_REMOTE_CONFIG_URL_BASE: string;\n  }\n}\n\nexport * from './api';\nexport * from './api2';\nexport * from './public_types';\n\n/** register component and version */\nregisterRemoteConfig();\n"], "names": ["ValueImpl", "FirebaseLogLevel", "packageName", "RemoteConfigImpl"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AA4BH;;;;;;;AAOG;MACU,uBAAuB,CAAA;AAApC,IAAA,WAAA,GAAA;QACE,IAAS,CAAA,SAAA,GAAsB,EAAE,CAAC;KAOnC;AANC,IAAA,gBAAgB,CAAC,QAAoB,EAAA;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;IACD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC;KAChD;AACF;;AC3DD;;;;;;;;;;;;;;;AAeG;AAEI,MAAM,iBAAiB,GAAG,eAAe;;ACjBhD;;;;;;;;;;;;;;;AAeG;AAqBH,MAAM,qBAAqB,GAA4C;AACrE,IAAA,CAAA,qBAAA,uCACE,iFAAiF;AACnF,IAAA,CAAA,yBAAA,2CACE,kEAAkE;AACpE,IAAA,CAAA,sBAAA,wCACE,uDAAuD;AACzD,IAAA,CAAA,qBAAA,uCACE,8DAA8D;AAChE,IAAA,CAAA,cAAA,gCACE,6EAA6E;AAC/E,IAAA,CAAA,aAAA,+BACE,kFAAkF;AACpF,IAAA,CAAA,aAAA,+BACE,gFAAgF;AAClF,IAAA,CAAA,gBAAA,kCACE,mFAAmF;AACrF,IAAA,CAAA,sBAAA,iCACE,yEAAyE;QACzE,2CAA2C;AAC7C,IAAA,CAAA,eAAA,iCACE,sCAAsC;QACtC,4DAA4D;AAC9D,IAAA,CAAA,gBAAA,kCACE,2EAA2E;QAC3E,4DAA4D;QAC5D,+FAA+F;AACjG,IAAA,CAAA,oBAAA,+BACE,wCAAwC;QACxC,2CAA2C;AAC7C,IAAA,CAAA,cAAA,gCACE,yEAAyE;AAC3E,IAAA,CAAA,wBAAA,0CACE,gDAAgD;CACnD,CAAC;AAoBK,MAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,cAAc,gBACd,eAAe,qBACf,qBAAqB,CACtB,CAAC;AAEF;AACgB,SAAA,YAAY,CAAC,CAAQ,EAAE,SAAoB,EAAA;AACzD,IAAA,OAAO,CAAC,YAAY,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE;;ACnGA;;;;;;;;;;;;;;;AAeG;AAIH,MAAM,yBAAyB,GAAG,KAAK,CAAC;AACxC,MAAM,wBAAwB,GAAG,EAAE,CAAC;AACpC,MAAM,wBAAwB,GAAG,CAAC,CAAC;AAEnC,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;MAEtD,KAAK,CAAA;IAChB,WACmB,CAAA,OAAoB,EACpB,MAAA,GAAiB,wBAAwB,EAAA;QADzC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAa;QACpB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAmC;KACxD;IAEJ,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,yBAAyB,CAAC;SAClC;AACD,QAAA,OAAO,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC;KACtE;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,wBAAwB,CAAC;SACjC;QACD,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,QAAA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YACd,GAAG,GAAG,wBAAwB,CAAC;SAChC;AACD,QAAA,OAAO,GAAG,CAAC;KACZ;IAED,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AACF;;ACxDD;;;;;;;;;;;;;;;AAeG;AAgBH;;;;;;AAMG;AACa,SAAA,eAAe,CAAC,GAAA,GAAmB,MAAM,EAAE,EAAA;AACzD,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;AACxD,IAAA,OAAO,UAAU,CAAC,YAAY,EAAE,CAAC;AACnC,CAAC;AAED;;;;;;;AAOG;AACI,eAAe,QAAQ,CAAC,YAA0B,EAAA;AACvD,IAAA,MAAM,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAqB,CAAC;IAChE,MAAM,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxE,QAAA,EAAE,CAAC,QAAQ,CAAC,8BAA8B,EAAE;AAC5C,QAAA,EAAE,CAAC,QAAQ,CAAC,mBAAmB,EAAE;AAClC,KAAA,CAAC,CAAC;AACH,IAAA,IACE,CAAC,2BAA2B;QAC5B,CAAC,2BAA2B,CAAC,MAAM;QACnC,CAAC,2BAA2B,CAAC,IAAI;AACjC,QAAA,2BAA2B,CAAC,IAAI,KAAK,gBAAgB,EACrD;;;AAGA,QAAA,OAAO,KAAK,CAAC;KACd;IACD,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,EAAE,CAAC,aAAa,CAAC,eAAe,CAAC,2BAA2B,CAAC,MAAM,CAAC;QACpE,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC,2BAA2B,CAAC,IAAI,CAAC;AAClE,KAAA,CAAC,CAAC;AACH,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;AAMG;AACG,SAAU,iBAAiB,CAAC,YAA0B,EAAA;AAC1D,IAAA,MAAM,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAqB,CAAC;AAChE,IAAA,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE;AAC1B,QAAA,EAAE,CAAC,kBAAkB,GAAG,EAAE,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,MAAK;AACnE,YAAA,EAAE,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACtC,SAAC,CAAC,CAAC;KACJ;IACD,OAAO,EAAE,CAAC,kBAAkB,CAAC;AAC/B,CAAC;AAED;;;;AAIG;AACI,eAAe,WAAW,CAAC,YAA0B,EAAA;AAC1D,IAAA,MAAM,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAqB,CAAC;;;;;;;;;;;AAWhE,IAAA,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;IAElD,UAAU,CAAC,YAAW;;QAEpB,WAAW,CAAC,KAAK,EAAE,CAAC;AACtB,KAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;;AAGnC,IAAA,IAAI;AACF,QAAA,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;AACrB,YAAA,iBAAiB,EAAE,EAAE,CAAC,QAAQ,CAAC,0BAA0B;AACzD,YAAA,MAAM,EAAE,WAAW;AACpB,SAAA,CAAC,CAAC;QAEH,MAAM,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;KACtD;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,eAAe,GAAG,YAAY,CAAC,CAAU,EAA2B,gBAAA,gCAAA;AACxE,cAAE,UAAU;cACV,SAAS,CAAC;QACd,MAAM,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;AAC3D,QAAA,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,MAAM,CAAC,YAA0B,EAAA;AAC/C,IAAA,MAAM,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAqB,CAAC;IAChE,OAAO,UAAU,CACf,EAAE,CAAC,aAAa,CAAC,eAAe,EAAE,EAClC,EAAE,CAAC,aAAa,CACjB,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,GAAG,KAAI;QAC3B,UAAU,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AAC9C,QAAA,OAAO,UAAU,CAAC;KACnB,EAAE,EAA2B,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,UAAU,CAAC,YAA0B,EAAE,GAAW,EAAA;AAChE,IAAA,OAAO,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;AACrE,CAAC;AAED;;;;;;;;;;;AAWG;AACa,SAAA,SAAS,CAAC,YAA0B,EAAE,GAAW,EAAA;AAC/D,IAAA,OAAO,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AACpE,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,SAAS,CAAC,YAA0B,EAAE,GAAW,EAAA;AAC/D,IAAA,OAAO,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AACpE,CAAC;AAED;;;;;;;;;AASG;AACa,SAAA,QAAQ,CAAC,YAA0B,EAAE,GAAW,EAAA;AAC9D,IAAA,MAAM,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAqB,CAAC;AAChE,IAAA,IAAI,CAAC,EAAE,CAAC,yBAAyB,EAAE;AACjC,QAAA,EAAE,CAAC,OAAO,CAAC,KAAK,CACd,CAAA,+BAAA,EAAkC,GAAG,CAAwC,sCAAA,CAAA;AAC3E,YAAA,oFAAoF,CACvF,CAAC;KACH;IACD,MAAM,YAAY,GAAG,EAAE,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;IACxD,IAAI,YAAY,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;QACnD,OAAO,IAAIA,KAAS,CAAC,QAAQ,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;KACnD;AAAM,SAAA,IAAI,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;AAClE,QAAA,OAAO,IAAIA,KAAS,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAChE;AACD,IAAA,EAAE,CAAC,OAAO,CAAC,KAAK,CACd,CAAA,gCAAA,EAAmC,GAAG,CAAI,EAAA,CAAA;AACxC,QAAA,6DAA6D,CAChE,CAAC;AACF,IAAA,OAAO,IAAIA,KAAS,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;AAOG;AACa,SAAA,WAAW,CACzB,YAA0B,EAC1B,QAA8B,EAAA;AAE9B,IAAA,MAAM,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAqB,CAAC;IAChE,QAAQ,QAAQ;AACd,QAAA,KAAK,OAAO;YACV,EAAE,CAAC,OAAO,CAAC,QAAQ,GAAGC,QAAgB,CAAC,KAAK,CAAC;YAC7C,MAAM;AACR,QAAA,KAAK,QAAQ;YACX,EAAE,CAAC,OAAO,CAAC,QAAQ,GAAGA,QAAgB,CAAC,MAAM,CAAC;YAC9C,MAAM;AACR,QAAA;YACE,EAAE,CAAC,OAAO,CAAC,QAAQ,GAAGA,QAAgB,CAAC,KAAK,CAAC;KAChD;AACH,CAAC;AAED;;AAEG;AACH,SAAS,UAAU,CAAC,IAAA,GAAW,EAAE,EAAE,OAAW,EAAE,EAAA;IAC9C,OAAO,MAAM,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAM,IAAI,CAAK,EAAA,IAAI,EAAG,CAAC;AAC3C;;ACnQA;;;;;;;;;;;;;;;AAeG;AAWH;;;;;;AAMG;MACU,aAAa,CAAA;AACxB,IAAA,WAAA,CACmB,MAA+B,EAC/B,OAAgB,EAChB,YAA0B,EAC1B,MAAc,EAAA;QAHd,IAAM,CAAA,MAAA,GAAN,MAAM,CAAyB;QAC/B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAChB,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAc;QAC1B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;KAC7B;AAEJ;;;;;;;;AAQG;IACH,iBAAiB,CACf,iBAAyB,EACzB,kCAAsD,EAAA;;QAGtD,IAAI,CAAC,kCAAkC,EAAE;AACvC,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;AAClE,YAAA,OAAO,KAAK,CAAC;SACd;;QAGD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,kCAAkC,CAAC;AAEvE,QAAA,MAAM,iBAAiB,GAAG,cAAc,IAAI,iBAAiB,CAAC;AAE9D,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B;AACzB,YAAA,CAAA,mBAAA,EAAsB,cAAc,CAAG,CAAA,CAAA;AACvC,YAAA,CAAA,4DAAA,EAA+D,iBAAiB,CAAG,CAAA,CAAA;YACnF,CAAkB,eAAA,EAAA,iBAAiB,CAAG,CAAA,CAAA,CACzC,CAAC;AAEF,QAAA,OAAO,iBAAiB,CAAC;KAC1B;IAED,MAAM,KAAK,CAAC,OAAqB,EAAA;;QAE/B,MAAM,CAAC,kCAAkC,EAAE,2BAA2B,CAAC,GACrE,MAAM,OAAO,CAAC,GAAG,CAAC;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,qCAAqC,EAAE;AACpD,YAAA,IAAI,CAAC,OAAO,CAAC,8BAA8B,EAAE;AAC9C,SAAA,CAAC,CAAC;;AAGL,QAAA,IACE,2BAA2B;YAC3B,IAAI,CAAC,iBAAiB,CACpB,OAAO,CAAC,iBAAiB,EACzB,kCAAkC,CACnC,EACD;AACA,YAAA,OAAO,2BAA2B,CAAC;SACpC;;;AAID,QAAA,OAAO,CAAC,IAAI;AACV,YAAA,2BAA2B,IAAI,2BAA2B,CAAC,IAAI,CAAC;;QAGlE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;AAIlD,QAAA,MAAM,iBAAiB,GAAG;;YAExB,IAAI,CAAC,YAAY,CAAC,qCAAqC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SACpE,CAAC;AAEF,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;;AAE3B,YAAA,iBAAiB,CAAC,IAAI,CACpB,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CACtD,CAAC;SACH;AAED,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAErC,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF;;ACxHD;;;;;;;;;;;;;;;AAeG;AAEH;;;;;;;;AAQG;AACa,SAAA,eAAe,CAC7B,iBAAA,GAAuC,SAAS,EAAA;IAEhD;;IAEE,CAAC,iBAAiB,CAAC,SAAS,IAAI,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;;;AAG9D,QAAA,iBAAiB,CAAC,QAAQ;;MAE1B;AACJ;;ACrCA;;;;;;;;;;;;;;;AAeG;AA+BH;;AAEG;MACU,UAAU,CAAA;IACrB,WACmB,CAAA,qBAAqD,EACrD,UAAkB,EAClB,SAAiB,EACjB,SAAiB,EACjB,MAAc,EACd,KAAa,EAAA;QALb,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAgC;QACrD,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAClB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QACjB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QACjB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACd,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;KAC5B;AAEJ;;;;;;;;AAQG;IACH,MAAM,KAAK,CAAC,OAAqB,EAAA;QAC/B,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AAC5D,YAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,OAAO,GACX,MAAM,CAAC,+BAA+B;AACtC,YAAA,6CAA6C,CAAC;AAEhD,QAAA,MAAM,GAAG,GAAG,CAAA,EAAG,OAAO,CAAA,aAAA,EAAgB,IAAI,CAAC,SAAS,CAAe,YAAA,EAAA,IAAI,CAAC,SAAS,CAAA,WAAA,EAAc,IAAI,CAAC,MAAM,EAAE,CAAC;AAE7G,QAAA,MAAM,OAAO,GAAG;AACd,YAAA,cAAc,EAAE,kBAAkB;AAClC,YAAA,kBAAkB,EAAE,MAAM;;;AAG1B,YAAA,eAAe,EAAE,OAAO,CAAC,IAAI,IAAI,GAAG;SACrC,CAAC;AAEF,QAAA,MAAM,WAAW,GAAqB;;YAEpC,WAAW,EAAE,IAAI,CAAC,UAAU;AAC5B,YAAA,eAAe,EAAE,cAAc;AAC/B,YAAA,qBAAqB,EAAE,iBAAiB;YACxC,MAAM,EAAE,IAAI,CAAC,KAAK;YAClB,aAAa,EAAE,eAAe,EAAE;;SAEjC,CAAC;AAEF,QAAA,MAAM,OAAO,GAAG;AACd,YAAA,MAAM,EAAE,MAAM;YACd,OAAO;AACP,YAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;SAClC,CAAC;;QAGF,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACzC,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAI;;AAEtD,YAAA,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAK;;AAEnC,gBAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AACtD,gBAAA,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC;gBAC1B,MAAM,CAAC,KAAK,CAAC,CAAC;AAChB,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,QAAQ,CAAC;AACb,QAAA,IAAI;YACF,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACnD,QAAQ,GAAG,MAAM,YAAY,CAAC;SAC/B;QAAC,OAAO,aAAa,EAAE;YACtB,IAAI,SAAS,wDAA2B;YACxC,IAAI,CAAC,aAAuB,KAAA,IAAA,IAAvB,aAAa,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAb,aAAa,CAAY,IAAI,MAAK,YAAY,EAAE;AACnD,gBAAA,SAAS,iDAA2B;aACrC;AACD,YAAA,MAAM,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE;AACpC,gBAAA,oBAAoB,EAAG,aAAuB,KAAA,IAAA,IAAvB,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAY,OAAO;AACxD,aAAA,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;;AAG7B,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;AAE/D,QAAA,IAAI,MAA8C,CAAC;AACnD,QAAA,IAAI,KAAyB,CAAC;;;AAI9B,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;AAC3B,YAAA,IAAI,YAAY,CAAC;AACjB,YAAA,IAAI;AACF,gBAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtC;YAAC,OAAO,aAAa,EAAE;gBACtB,MAAM,aAAa,CAAC,MAAM,CAAwB,oBAAA,8BAAA;AAChD,oBAAA,oBAAoB,EAAG,aAAuB,KAAA,IAAA,IAAvB,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAY,OAAO;AACxD,iBAAA,CAAC,CAAC;aACJ;AACD,YAAA,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;AACjC,YAAA,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;SAC/B;;AAGD,QAAA,IAAI,KAAK,KAAK,4BAA4B,EAAE;YAC1C,MAAM,GAAG,GAAG,CAAC;SACd;AAAM,aAAA,IAAI,KAAK,KAAK,WAAW,EAAE;YAChC,MAAM,GAAG,GAAG,CAAC;SACd;aAAM,IAAI,KAAK,KAAK,aAAa,IAAI,KAAK,KAAK,cAAc,EAAE;;YAE9D,MAAM,GAAG,EAAE,CAAC;SACb;;;;;QAMD,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE;YACpC,MAAM,aAAa,CAAC,MAAM,CAAyB,cAAA,+BAAA;AACjD,gBAAA,UAAU,EAAE,MAAM;AACnB,aAAA,CAAC,CAAC;SACJ;QAED,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC;KAC/C;AACF;;AC/KD;;;;;;;;;;;;;;;AAeG;AAYH;;;;;;;;;;;AAWG;AACa,SAAA,mBAAmB,CACjC,MAA+B,EAC/B,qBAA6B,EAAA;IAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;;AAErC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtE,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;;AAGnD,QAAA,MAAM,CAAC,gBAAgB,CAAC,MAAK;YAC3B,YAAY,CAAC,OAAO,CAAC,CAAC;;AAGtB,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA2B,gBAAA,iCAAA;gBAC7C,qBAAqB;AACtB,aAAA,CAAC,CACH,CAAC;AACJ,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAGD;;AAEG;AACH,SAAS,gBAAgB,CAAC,CAAQ,EAAA;AAChC,IAAA,IAAI,EAAE,CAAC,YAAY,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE;AAClD,QAAA,OAAO,KAAK,CAAC;KACd;;IAGD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;IAEtD,QACE,UAAU,KAAK,GAAG;AAClB,QAAA,UAAU,KAAK,GAAG;AAClB,QAAA,UAAU,KAAK,GAAG;QAClB,UAAU,KAAK,GAAG,EAClB;AACJ,CAAC;AAED;;;;;AAKG;MACU,cAAc,CAAA;IACzB,WACmB,CAAA,MAA+B,EAC/B,OAAgB,EAAA;QADhB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAyB;QAC/B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;KAC/B;IAEJ,MAAM,KAAK,CAAC,OAAqB,EAAA;QAC/B,MAAM,gBAAgB,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK;AACrE,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,qBAAqB,EAAE,IAAI,CAAC,GAAG,EAAE;SAClC,CAAC;QAEF,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;KACrD;AAED;;;;AAIG;IACH,MAAM,YAAY,CAChB,OAAqB,EACrB,EAAE,qBAAqB,EAAE,YAAY,EAAoB,EAAA;;;;QAKzD,MAAM,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;AAEjE,QAAA,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;AAGlD,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;AAE5C,YAAA,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,CAAC,gBAAgB,CAAC,CAAU,CAAC,EAAE;AACjC,gBAAA,MAAM,CAAC,CAAC;aACT;;AAGD,YAAA,MAAM,gBAAgB,GAAG;gBACvB,qBAAqB,EACnB,IAAI,CAAC,GAAG,EAAE,GAAG,sBAAsB,CAAC,YAAY,CAAC;gBACnD,YAAY,EAAE,YAAY,GAAG,CAAC;aAC/B,CAAC;;YAGF,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YAEzD,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;SACrD;KACF;AACF;;AC/ID;;;;;;;;;;;;;;;AAeG;AAaH,MAAM,4BAA4B,GAAG,EAAE,GAAG,IAAI,CAAC;AAC/C,MAAM,4BAA4B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAEzD;;;;AAIG;MACU,YAAY,CAAA;AAoBvB,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAAC,CAAC;KACzE;AAED,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,IAAI,cAAc,CAAC;KAClE;AAED,IAAA,WAAA;;IAEW,GAAgB;;;;AAIzB;;AAEG;IACM,OAAgC;AACzC;;AAEG;IACM,aAA2B;AACpC;;AAEG;IACM,QAAiB;AAC1B;;AAEG;IACM,OAAe,EAAA;QAnBf,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAOhB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;QAIhC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAI3B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QAIjB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;AAhD1B;;;AAGG;QACH,IAAyB,CAAA,yBAAA,GAAG,KAAK,CAAC;AAQlC,QAAA,IAAA,CAAA,QAAQ,GAAyB;AAC/B,YAAA,kBAAkB,EAAE,4BAA4B;AAChD,YAAA,0BAA0B,EAAE,4BAA4B;SACzD,CAAC;QAEF,IAAa,CAAA,aAAA,GAAiD,EAAE,CAAC;KAgC7D;AACL;;ACvFD;;;;;;;;;;;;;;;AAeG;AAUH;;AAEG;AACH,SAAS,eAAe,CAAC,KAAY,EAAE,SAAoB,EAAA;IACzD,MAAM,aAAa,GAAI,KAAK,CAAC,MAAqB,CAAC,KAAK,IAAI,SAAS,CAAC;AACtE,IAAA,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE;QACrC,oBAAoB,EAAE,aAAa,KAAK,aAAuB,KAAA,IAAA,IAAvB,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAY,OAAO,CAAA;AACzE,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;AASG;AACI,MAAM,mBAAmB,GAAG,qBAAqB,CAAC;AAEzD,MAAM,OAAO,GAAG,wBAAwB,CAAC;AACzC,MAAM,UAAU,GAAG,CAAC,CAAC;AA0BrB;SACgB,YAAY,GAAA;IAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,QAAA,IAAI;YACF,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACpD,YAAA,OAAO,CAAC,OAAO,GAAG,KAAK,IAAG;AACxB,gBAAA,MAAM,CAAC,eAAe,CAAC,KAAK,EAAA,cAAA,8BAAyB,CAAC,CAAC;AACzD,aAAC,CAAC;AACF,YAAA,OAAO,CAAC,SAAS,GAAG,KAAK,IAAG;AAC1B,gBAAA,OAAO,CAAE,KAAK,CAAC,MAA2B,CAAC,MAAM,CAAC,CAAC;AACrD,aAAC,CAAC;AACF,YAAA,OAAO,CAAC,eAAe,GAAG,KAAK,IAAG;AAChC,gBAAA,MAAM,EAAE,GAAI,KAAK,CAAC,MAA2B,CAAC,MAAM,CAAC;;;;;;AAOrD,gBAAA,QAAQ,KAAK,CAAC,UAAU;AACtB,oBAAA,KAAK,CAAC;AACJ,wBAAA,EAAE,CAAC,iBAAiB,CAAC,mBAAmB,EAAE;AACxC,4BAAA,OAAO,EAAE,cAAc;AACxB,yBAAA,CAAC,CAAC;iBACN;AACH,aAAC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAAyB,cAAA,+BAAA;AAC3C,gBAAA,oBAAoB,EAAG,KAAe,KAAA,IAAA,IAAf,KAAK,KAAL,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,KAAK,CAAY,OAAO;AAChD,aAAA,CAAC,CACH,CAAC;SACH;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;MACU,OAAO,CAAA;AAClB;;;;AAIG;IACH,WACmB,CAAA,KAAa,EACb,OAAe,EACf,SAAiB,EACjB,aAAA,GAAgB,YAAY,EAAE,EAAA;QAH9B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QACjB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAiB;KAC7C;IAEJ,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAc,mBAAmB,CAAC,CAAC;KACnD;AAED,IAAA,kBAAkB,CAAC,MAAmB,EAAA;QACpC,OAAO,IAAI,CAAC,GAAG,CAAc,mBAAmB,EAAE,MAAM,CAAC,CAAC;KAC3D;;;IAID,qCAAqC,GAAA;AACnC,QAAA,OAAO,IAAI,CAAC,GAAG,CAAS,wCAAwC,CAAC,CAAC;KACnE;AAED,IAAA,qCAAqC,CAAC,SAAiB,EAAA;QACrD,OAAO,IAAI,CAAC,GAAG,CACb,wCAAwC,EACxC,SAAS,CACV,CAAC;KACH;IAED,8BAA8B,GAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,GAAG,CAAgB,gCAAgC,CAAC,CAAC;KAClE;AAED,IAAA,8BAA8B,CAAC,QAAuB,EAAA;QACpD,OAAO,IAAI,CAAC,GAAG,CAAgB,gCAAgC,EAAE,QAAQ,CAAC,CAAC;KAC5E;IAED,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,GAAG,CAA6B,eAAe,CAAC,CAAC;KAC9D;AAED,IAAA,eAAe,CAAC,MAAkC,EAAA;QAChD,OAAO,IAAI,CAAC,GAAG,CAA6B,eAAe,EAAE,MAAM,CAAC,CAAC;KACtE;IAED,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAS,oBAAoB,CAAC,CAAC;KAC/C;AAED,IAAA,mBAAmB,CAAC,IAAY,EAAA;QAC9B,OAAO,IAAI,CAAC,GAAG,CAAS,oBAAoB,EAAE,IAAI,CAAC,CAAC;KACrD;IAED,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAmB,mBAAmB,CAAC,CAAC;KACxD;AAED,IAAA,mBAAmB,CAAC,QAA0B,EAAA;QAC5C,OAAO,IAAI,CAAC,GAAG,CAAmB,mBAAmB,EAAE,QAAQ,CAAC,CAAC;KAClE;IAED,sBAAsB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;KACzC;IAED,MAAM,GAAG,CAAI,GAAkC,EAAA;AAC7C,QAAA,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC;QACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,YAAA,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,UAAU,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAClD,YAAA,IAAI;gBACF,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC9C,gBAAA,OAAO,CAAC,OAAO,GAAG,KAAK,IAAG;AACxB,oBAAA,MAAM,CAAC,eAAe,CAAC,KAAK,EAAA,aAAA,6BAAwB,CAAC,CAAC;AACxD,iBAAC,CAAC;AACF,gBAAA,OAAO,CAAC,SAAS,GAAG,KAAK,IAAG;AAC1B,oBAAA,MAAM,MAAM,GAAI,KAAK,CAAC,MAAqB,CAAC,MAAM,CAAC;oBACnD,IAAI,MAAM,EAAE;AACV,wBAAA,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;qBACvB;yBAAM;wBACL,OAAO,CAAC,SAAS,CAAC,CAAC;qBACpB;AACH,iBAAC,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAAwB,aAAA,8BAAA;AAC1C,oBAAA,oBAAoB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC5C,iBAAA,CAAC,CACH,CAAC;aACH;AACH,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,MAAM,GAAG,CAAI,GAAkC,EAAE,KAAQ,EAAA;AACvD,QAAA,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC;QACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,YAAA,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,WAAW,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAClD,YAAA,IAAI;AACF,gBAAA,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC;oBAC9B,YAAY;oBACZ,KAAK;AACN,iBAAA,CAAC,CAAC;AACH,gBAAA,OAAO,CAAC,OAAO,GAAG,CAAC,KAAY,KAAI;AACjC,oBAAA,MAAM,CAAC,eAAe,CAAC,KAAK,EAAA,aAAA,6BAAwB,CAAC,CAAC;AACxD,iBAAC,CAAC;AACF,gBAAA,OAAO,CAAC,SAAS,GAAG,MAAK;AACvB,oBAAA,OAAO,EAAE,CAAC;AACZ,iBAAC,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAAwB,aAAA,8BAAA;AAC1C,oBAAA,oBAAoB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC5C,iBAAA,CAAC,CACH,CAAC;aACH;AACH,SAAC,CAAC,CAAC;KACJ;IAED,MAAM,MAAM,CAAC,GAAkC,EAAA;AAC7C,QAAA,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC;QACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,YAAA,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,WAAW,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAClD,YAAA,IAAI;gBACF,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACjD,gBAAA,OAAO,CAAC,OAAO,GAAG,CAAC,KAAY,KAAI;AACjC,oBAAA,MAAM,CAAC,eAAe,CAAC,KAAK,EAAA,gBAAA,gCAA2B,CAAC,CAAC;AAC3D,iBAAC,CAAC;AACF,gBAAA,OAAO,CAAC,SAAS,GAAG,MAAK;AACvB,oBAAA,OAAO,EAAE,CAAC;AACZ,iBAAC,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA2B,gBAAA,iCAAA;AAC7C,oBAAA,oBAAoB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC5C,iBAAA,CAAC,CACH,CAAC;aACH;AACH,SAAC,CAAC,CAAC;KACJ;;AAGD,IAAA,kBAAkB,CAAC,GAAkC,EAAA;AACnD,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;KAC/D;AACF;;AC3QD;;;;;;;;;;;;;;;AAeG;AAMH;;AAEG;MACU,YAAY,CAAA;AACvB,IAAA,WAAA,CAA6B,OAAgB,EAAA;QAAhB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;KAAI;AASjD;;AAEG;IACH,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IAED,qCAAqC,GAAA;QACnC,OAAO,IAAI,CAAC,kCAAkC,CAAC;KAChD;IAED,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAED;;AAEG;AACH,IAAA,MAAM,eAAe,GAAA;QACnB,MAAM,sBAAsB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACjE,MAAM,yCAAyC,GAC7C,IAAI,CAAC,OAAO,CAAC,qCAAqC,EAAE,CAAC;QACvD,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;;;;;;AAQ3D,QAAA,MAAM,eAAe,GAAG,MAAM,sBAAsB,CAAC;QACrD,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;SACxC;AAED,QAAA,MAAM,kCAAkC,GACtC,MAAM,yCAAyC,CAAC;QAClD,IAAI,kCAAkC,EAAE;AACtC,YAAA,IAAI,CAAC,kCAAkC;AACrC,gBAAA,kCAAkC,CAAC;SACtC;AAED,QAAA,MAAM,YAAY,GAAG,MAAM,mBAAmB,CAAC;QAC/C,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;SAClC;KACF;AAED;;AAEG;AACH,IAAA,kBAAkB,CAAC,MAAmB,EAAA;AACpC,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;KAChD;AAED,IAAA,qCAAqC,CACnC,eAAuB,EAAA;AAEvB,QAAA,IAAI,CAAC,kCAAkC,GAAG,eAAe,CAAC;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,qCAAqC,CAAC,eAAe,CAAC,CAAC;KAC5E;AAED,IAAA,eAAe,CAAC,YAAwC,EAAA;AACtD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;KACnD;AACF;;ACrGD;;;;;;;;;;;;;;;AAeG;SA6Ba,oBAAoB,GAAA;AAClC,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,iBAAiB,EACjB,mBAAmB,EAEpB,QAAA,4BAAA,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AAEF,IAAA,eAAe,CAACC,IAAW,EAAE,OAAO,CAAC,CAAC;;AAEtC,IAAA,eAAe,CAACA,IAAW,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;IAE1D,SAAS,mBAAmB,CAC1B,SAA6B,EAC7B,EAAE,kBAAkB,EAAE,SAAS,EAA0B,EAAA;;;QAIzD,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;;QAExD,MAAM,aAAa,GAAG,SAAS;aAC5B,WAAW,CAAC,wBAAwB,CAAC;AACrC,aAAA,YAAY,EAAE,CAAC;;AAGlB,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,qCAA+B,CAAC;SAC3D;;AAED,QAAA,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC3B,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,wBAAA,wCAAkC,CAAC;SAC9D;;QAED,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;QACjD,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,yBAAA,yCAAmC,CAAC;SAC/D;QACD,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,sBAAA,sCAAgC,CAAC;SAC5D;QACD,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,qCAA+B,CAAC;SAC3D;AACD,QAAA,SAAS,GAAG,SAAS,IAAI,UAAU,CAAC;AAEpC,QAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACxD,QAAA,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;AAE/C,QAAA,MAAM,MAAM,GAAG,IAAI,MAAM,CAACA,IAAW,CAAC,CAAC;;;AAIvC,QAAA,MAAM,CAAC,QAAQ,GAAGD,QAAgB,CAAC,KAAK,CAAC;AAEzC,QAAA,MAAM,UAAU,GAAG,IAAI,UAAU,CAC/B,aAAa;;QAEb,WAAW,EACX,SAAS,EACT,SAAS,EACT,MAAM,EACN,KAAK,CACN,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC/D,QAAA,MAAM,aAAa,GAAG,IAAI,aAAa,CACrC,cAAc,EACd,OAAO,EACP,YAAY,EACZ,MAAM,CACP,CAAC;AAEF,QAAA,MAAM,oBAAoB,GAAG,IAAIE,YAAgB,CAC/C,GAAG,EACH,aAAa,EACb,YAAY,EACZ,OAAO,EACP,MAAM,CACP,CAAC;;;QAIF,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;AAExC,QAAA,OAAO,oBAAoB,CAAC;KAC7B;AACH;;AClIA;;;;;;;;;;;;;;;AAeG;AAUH;AACA;AACA;;;;;;;;;;AAUG;AACI,eAAe,gBAAgB,CACpC,YAA0B,EAAA;AAE1B,IAAA,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAChD,IAAA,MAAM,WAAW,CAAC,YAAY,CAAC,CAAC;AAChC,IAAA,OAAO,QAAQ,CAAC,YAAY,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;;;AASG;AACI,eAAe,WAAW,GAAA;AAC/B,IAAA,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC3B,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI;AACF,QAAA,MAAM,YAAY,GAAY,MAAM,yBAAyB,EAAE,CAAC;AAChE,QAAA,OAAO,YAAY,CAAC;KACrB;IAAC,OAAO,KAAK,EAAE;AACd,QAAA,OAAO,KAAK,CAAC;KACd;AACH;;ACnEA;;;;;AAKG;AAmCH;AACA,oBAAoB,EAAE;;;;"}