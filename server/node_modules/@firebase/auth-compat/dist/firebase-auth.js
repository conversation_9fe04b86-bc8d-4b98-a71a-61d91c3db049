!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)}(this,function(Vi,xi){"use strict";try{!function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=e(Vi);const t={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var i=e?this.byteToCharMapWebSafe_:this.byteToCharMap_;const s=[];for(let n=0;n<r.length;n+=3){var a=r[n],o=n+1<r.length,c=o?r[n+1]:0,l=n+2<r.length,u=l?r[n+2]:0;let e=(15&c)<<2|u>>6,t=63&u;l||(t=64,o||(e=64)),s.push(i[a>>2],i[(3&a)<<4|c>>4],i[e],i[t])}return s.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(function(n){const r=[];let i=0;for(let t=0;t<n.length;t++){let e=n.charCodeAt(t);e<128?r[i++]=e:(e<2048?r[i++]=e>>6|192:(55296==(64512&e)&&t+1<n.length&&56320==(64512&n.charCodeAt(t+1))?(e=65536+((1023&e)<<10)+(1023&n.charCodeAt(++t)),r[i++]=e>>18|240,r[i++]=e>>12&63|128):r[i++]=e>>12|224,r[i++]=e>>6&63|128),r[i++]=63&e|128)}return r}(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let n=0,r=0;for(;n<e.length;){var i,s,a=e[n++];a<128?t[r++]=String.fromCharCode(a):191<a&&a<224?(i=e[n++],t[r++]=String.fromCharCode((31&a)<<6|63&i)):239<a&&a<365?(s=((7&a)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++])-65536,t[r++]=String.fromCharCode(55296+(s>>10)),t[r++]=String.fromCharCode(56320+(1023&s))):(i=e[n++],s=e[n++],t[r++]=String.fromCharCode((15&a)<<12|(63&i)<<6|63&s))}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(t,e){this.init_();var n=e?this.charToByteMapWebSafe_:this.charToByteMap_;const r=[];for(let e=0;e<t.length;){var i=n[t.charAt(e++)],s=e<t.length?n[t.charAt(e)]:0;++e;var a=e<t.length?n[t.charAt(e)]:64;++e;var o=e<t.length?n[t.charAt(e)]:64;if(++e,null==i||null==s||null==a||null==o)throw new c;r.push(i<<2|s>>4),64!==a&&(r.push(s<<4&240|a>>2),64!==o&&r.push(a<<6&192|o))}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class c extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const s=function(e){try{return t.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};const n=()=>function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__,r=()=>{if("undefined"!=typeof process&&void 0!==process.env){var e=process.env.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0}},a=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&s(e[1]);return t&&JSON.parse(t)}},o=()=>{try{return n()||r()||a()}catch(e){return void console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`)}};var l,u;function d(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function h(){var e=null===(e=o())||void 0===e?void 0:e.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function p(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function f(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function m(){const e=d();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function g(){try{return"object"==typeof indexedDB}catch(e){return!1}}class v extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,v.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,_.prototype.create)}}class _{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){var r,n=t[0]||{},t=`${this.service}/${e}`,e=this.errors[e],e=e?(r=n,e.replace(y,(e,t)=>{var n=r[t];return null!=n?String(n):`<${t}?>`})):"Error",e=`${this.serviceName}: ${e} (${t}).`;return new v(t,e,n)}}const y=/\{\$([^}]+)}/g;function I(e){const t=[];for(const[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.push(encodeURIComponent(n)+"="+encodeURIComponent(e))}):t.push(encodeURIComponent(n)+"="+encodeURIComponent(r));return t.length?"&"+t.join("&"):""}function w(e){const n={},t=e.replace(/^\?/,"").split("&");return t.forEach(e=>{var t;e&&([t,e]=e.split("="),n[decodeURIComponent(t)]=decodeURIComponent(e))}),n}function T(e){var t=e.indexOf("?");if(!t)return"";var n=e.indexOf("#",t);return e.substring(t,0<n?n:void 0)}class E{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,n){let r;if(void 0===e&&void 0===t&&void 0===n)throw new Error("Missing Observer.");r=function(e,t){if("object"!=typeof e||null===e)return!1;for(const n of t)if(n in e&&"function"==typeof e[n])return!0;return!1}(e,["next","error","complete"])?e:{next:e,error:t,complete:n},void 0===r.next&&(r.next=b),void 0===r.error&&(r.error=b),void 0===r.complete&&(r.complete=b);n=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?r.error(this.finalError):r.complete()}catch(e){}}),this.observers.push(r),n}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function b(){}function k(e){return e&&e._delegate?e._delegate:e}(u=l=l||{})[u.DEBUG=0]="DEBUG",u[u.VERBOSE=1]="VERBOSE",u[u.INFO=2]="INFO",u[u.WARN=3]="WARN",u[u.ERROR=4]="ERROR",u[u.SILENT=5]="SILENT";const S={debug:l.DEBUG,verbose:l.VERBOSE,info:l.INFO,warn:l.WARN,error:l.ERROR,silent:l.SILENT},R=l.INFO,A={[l.DEBUG]:"log",[l.VERBOSE]:"log",[l.INFO]:"info",[l.WARN]:"warn",[l.ERROR]:"error"},P=(e,t,...n)=>{if(!(t<e.logLevel)){var r=(new Date).toISOString(),i=A[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${r}]  ${e.name}:`,...n)}};function C(e,t){var n={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n}class O{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const N={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},L={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function D(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function U(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements."}}const M=D,F=new _("auth","Firebase",D()),V=new class{constructor(e){this.name=e,this._logLevel=R,this._logHandler=P,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in l))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?S[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,l.DEBUG,...e),this._logHandler(this,l.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,l.VERBOSE,...e),this._logHandler(this,l.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,l.INFO,...e),this._logHandler(this,l.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,l.WARN,...e),this._logHandler(this,l.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,l.ERROR,...e),this._logHandler(this,l.ERROR,...e)}}("@firebase/auth");function x(e,...t){V.logLevel<=l.ERROR&&V.error(`Auth (${xi.SDK_VERSION}): ${e}`,...t)}function j(e,...t){throw z(e,...t)}function H(e,...t){return z(e,...t)}function W(e,t,n){n=Object.assign(Object.assign({},M()),{[t]:n});const r=new _("auth","Firebase",n);return r.create(t,{appName:e.name})}function q(e){return W(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function B(e,t,n){if(!(t instanceof n))throw n.name!==t.constructor.name&&j(e,"argument-error"),W(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function z(e,...t){if("string"==typeof e)return F.create(e,...t);{var n=t[0];const r=[...t.slice(1)];return r[0]&&(r[0].appName=e.name),e._errorFactory.create(n,...r)}}function G(e,t,...n){if(!e)throw z(t,...n)}function K(e){e="INTERNAL ASSERTION FAILED: "+e;throw x(e),new Error(e)}function $(e,t){e||K(t)}function J(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.href)||""}function Y(){return"http:"===X()||"https:"===X()}function X(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.protocol)||null}class Q{constructor(e,t){$((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(d())||f()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(Y()||p()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function Z(e,t){$(e.emulator,"Emulator should always be set here");var e=e.emulator["url"];return t?`${e}${t.startsWith("/")?t.slice(1):t}`:e}class ee{static initialize(e,t,n){this.fetchImpl=e,t&&(this.headersImpl=t),n&&(this.responseImpl=n)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void K("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void K("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void K("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}const te={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},ne=new Q(3e4,6e4);function re(e,t){return e.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:e.tenantId}):t}async function ie(s,a,o,c,e={}){return se(s,e,async()=>{let e={},t={};c&&("GET"===a?t=c:e={body:JSON.stringify(c)});var n=I(Object.assign({key:s.config.apiKey},t)).slice(1);const r=await s._getAdditionalHeaders();r["Content-Type"]="application/json",s.languageCode&&(r["X-Firebase-Locale"]=s.languageCode);const i=Object.assign({method:a,headers:r},e);return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(i.referrerPolicy="no-referrer"),ee.fetch()(oe(s,s.config.apiHost,o,n),i)})}async function se(t,e,n){t._canInitEmulator=!1;e=Object.assign(Object.assign({},te),e);try{const s=new ce(t),a=await Promise.race([n(),s.promise]);s.clearNetworkTimeout();var r=await a.json();if("needConfirmation"in r)throw le(t,"account-exists-with-different-credential",r);if(a.ok&&!("errorMessage"in r))return r;{const o=a.ok?r.errorMessage:r.error.message,[c,l]=o.split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===c)throw le(t,"credential-already-in-use",r);if("EMAIL_EXISTS"===c)throw le(t,"email-already-in-use",r);if("USER_DISABLED"===c)throw le(t,"user-disabled",r);var i=e[c]||c.toLowerCase().replace(/[_\s]+/g,"-");if(l)throw W(t,i,l);j(t,i)}}catch(e){if(e instanceof v)throw e;j(t,"network-request-failed",{message:String(e)})}}async function ae(e,t,n,r,i={}){i=await ie(e,t,n,r,i);return"mfaPendingCredential"in i&&j(e,"multi-factor-auth-required",{_serverResponse:i}),i}function oe(e,t,n,r){r=`${t}${n}?${r}`;return e.config.emulator?Z(e.config,r):`${e.config.apiScheme}://${r}`}class ce{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(H(this.auth,"network-request-failed")),ne.get())})}}function le(e,t,n){const r={appName:e.name};n.email&&(r.email=n.email),n.phoneNumber&&(r.phoneNumber=n.phoneNumber);const i=H(e,t,r);return i.customData._tokenResponse=n,i}function ue(e){return void 0!==e&&void 0!==e.getResponse}function de(e){return void 0!==e&&void 0!==e.enterprise}class he{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(e){if(!this.recaptchaEnforcementState||0===this.recaptchaEnforcementState.length)return null;for(const t of this.recaptchaEnforcementState)if(t.provider&&t.provider===e)return function(e){switch(e){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}}(t.enforcementState);return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}}async function pe(e,t){return ie(e,"GET","/v2/recaptchaConfig",re(e,t))}async function fe(e,t){return ie(e,"POST","/v1/accounts:lookup",t)}function me(e){if(e)try{const t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function ge(e){return 1e3*Number(e)}function ve(e){var[t,n,e]=e.split(".");if(void 0===t||void 0===n||void 0===e)return x("JWT malformed, contained fewer than 3 sections"),null;try{var r=s(n);return r?JSON.parse(r):(x("Failed to decode base64 JWT payload"),null)}catch(e){return x("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function _e(e){e=ve(e);return G(e,"internal-error"),G(void 0!==e.exp,"internal-error"),G(void 0!==e.iat,"internal-error"),Number(e.exp)-Number(e.iat)}async function ye(t,n,e=!1){if(e)return n;try{return n}catch(e){throw e instanceof v&&(n=[e["code"]][0],"auth/user-disabled"===n||"auth/user-token-expired"===n)&&t.auth.currentUser===t&&await t.auth.signOut(),e}}class Ie{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId&&clearTimeout(this.timerId))}getInterval(e){if(e){var t=this.errorBackoff;return this.errorBackoff=Math.min(2*this.errorBackoff,96e4),t}this.errorBackoff=3e4;t=(null!==(t=this.user.stsTokenManager.expirationTime)&&void 0!==t?t:0)-Date.now()-3e5;return Math.max(0,t)}schedule(e=!1){this.isRunning&&(e=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},e))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===(null==e?void 0:e.code)&&this.schedule(!0))}this.schedule()}}class we{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=me(this.lastLoginAt),this.creationTime=me(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function Te(e){var t=e.auth,n=await e.getIdToken(),r=await ye(e,fe(t,{idToken:n}));G(null==r?void 0:r.users.length,t,"internal-error");var i=r.users[0];e._notifyReloadListener(i);t=null!==(n=i.providerUserInfo)&&void 0!==n&&n.length?Ee(i.providerUserInfo):[],r=function(e,n){e=e.filter(t=>!n.some(e=>e.providerId===t.providerId));return[...e,...n]}(e.providerData,t),n=e.isAnonymous,t=!(e.email&&i.passwordHash||null!=r&&r.length),t=!!n&&t,t={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:r,metadata:new we(i.createdAt,i.lastLoginAt),isAnonymous:t};Object.assign(e,t)}function Ee(e){return e.map(e=>{var t=e["providerId"],e=C(e,["providerId"]);return{providerId:t,uid:e.rawId||"",displayName:e.displayName||null,email:e.email||null,phoneNumber:e.phoneNumber||null,photoURL:e.photoUrl||null}})}class be{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){G(e.idToken,"internal-error"),G(void 0!==e.idToken,"internal-error"),G(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):_e(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){G(0!==e.length,"internal-error");var t=_e(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?(G(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){var i,s,{accessToken:t,refreshToken:e,expiresIn:n}=(s=t,await{accessToken:(n=await se(i=e,{},async()=>{var e=I({grant_type:"refresh_token",refresh_token:s}).slice(1),{tokenApiHost:t,apiKey:n}=i.config,n=oe(i,t,"/v1/token",`key=${n}`);const r=await i._getAdditionalHeaders();return r["Content-Type"]="application/x-www-form-urlencoded",ee.fetch()(n,{method:"POST",headers:r,body:e})})).access_token,expiresIn:n.expires_in,refreshToken:n.refresh_token});this.updateTokensAndExpiration(t,e,Number(n))}updateTokensAndExpiration(e,t,n){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*n}static fromJSON(e,t){var{refreshToken:n,accessToken:r,expirationTime:t}=t;const i=new be;return n&&(G("string"==typeof n,"internal-error",{appName:e}),i.refreshToken=n),r&&(G("string"==typeof r,"internal-error",{appName:e}),i.accessToken=r),t&&(G("number"==typeof t,"internal-error",{appName:e}),i.expirationTime=t),i}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new be,this.toJSON())}_performRefresh(){return K("not implemented")}}function ke(e,t){G("string"==typeof e||void 0===e,"internal-error",{appName:t})}class Se{constructor(e){var{uid:t,auth:n,stsTokenManager:r}=e,e=C(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new Ie(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=n,this.stsTokenManager=r,this.accessToken=r.accessToken,this.displayName=e.displayName||null,this.email=e.email||null,this.emailVerified=e.emailVerified||!1,this.phoneNumber=e.phoneNumber||null,this.photoURL=e.photoURL||null,this.isAnonymous=e.isAnonymous||!1,this.tenantId=e.tenantId||null,this.providerData=e.providerData?[...e.providerData]:[],this.metadata=new we(e.createdAt||void 0,e.lastLoginAt||void 0)}async getIdToken(e){e=await ye(this,this.stsTokenManager.getToken(this.auth,e));return G(e,this.auth,"internal-error"),this.accessToken!==e&&(this.accessToken=e,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),e}getIdTokenResult(e){return async function(e,t=!1){const n=k(e);var r=await n.getIdToken(t),i=ve(r);return G(i&&i.exp&&i.auth_time&&i.iat,n.auth,"internal-error"),e="object"==typeof i.firebase?i.firebase:void 0,t=null==e?void 0:e.sign_in_provider,{claims:i,token:r,authTime:me(ge(i.auth_time)),issuedAtTime:me(ge(i.iat)),expirationTime:me(ge(i.exp)),signInProvider:t||null,signInSecondFactor:(null==e?void 0:e.sign_in_second_factor)||null}}(this,e)}reload(){return async function(e){const t=k(e);await Te(t),await t.auth._persistUserIfCurrent(t),t.auth._notifyListenersIfCurrent(t)}(this)}_assign(e){this!==e&&(G(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>Object.assign({},e)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){const t=new Se(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t}_onReload(e){G(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let n=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),n=!0),t&&await Te(this),await this.auth._persistUserIfCurrent(this),n&&this.auth._notifyListenersIfCurrent(this)}async delete(){if(xi._isFirebaseServerApp(this.auth.app))return Promise.reject(q(this.auth));var e=await this.getIdToken();return await ye(this,async function(e,t){return ie(e,"POST","/v1/accounts:delete",t)}(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut()}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var n=null!==(l=t.displayName)&&void 0!==l?l:void 0,r=null!==(a=t.email)&&void 0!==a?a:void 0,i=null!==(c=t.phoneNumber)&&void 0!==c?c:void 0,s=null!==(o=t.photoURL)&&void 0!==o?o:void 0,a=null!==(l=t.tenantId)&&void 0!==l?l:void 0,o=null!==(c=t._redirectEventId)&&void 0!==c?c:void 0,c=null!==(l=t.createdAt)&&void 0!==l?l:void 0,l=null!==(l=t.lastLoginAt)&&void 0!==l?l:void 0;const{uid:u,emailVerified:d,isAnonymous:h,providerData:p,stsTokenManager:f}=t;G(u&&f,e,"internal-error");t=be.fromJSON(this.name,f);G("string"==typeof u,e,"internal-error"),ke(n,e.name),ke(r,e.name),G("boolean"==typeof d,e,"internal-error"),G("boolean"==typeof h,e,"internal-error"),ke(i,e.name),ke(s,e.name),ke(a,e.name),ke(o,e.name),ke(c,e.name),ke(l,e.name);const m=new Se({uid:u,auth:e,email:r,emailVerified:d,displayName:n,isAnonymous:h,photoURL:s,phoneNumber:i,tenantId:a,stsTokenManager:t,createdAt:c,lastLoginAt:l});return p&&Array.isArray(p)&&(m.providerData=p.map(e=>Object.assign({},e))),o&&(m._redirectEventId=o),m}static async _fromIdTokenResponse(e,t,n=!1){const r=new be;r.updateFromServerResponse(t);n=new Se({uid:t.localId,auth:e,stsTokenManager:r,isAnonymous:n});return await Te(n),n}static async _fromGetAccountInfoResponse(e,t,n){var r=t.users[0];G(void 0!==r.localId,"internal-error");var i=void 0!==r.providerUserInfo?Ee(r.providerUserInfo):[],t=!(r.email&&r.passwordHash||null!=i&&i.length);const s=new be;s.updateFromIdToken(n);t=new Se({uid:r.localId,auth:e,stsTokenManager:s,isAnonymous:t}),i={uid:r.localId,displayName:r.displayName||null,photoURL:r.photoUrl||null,email:r.email||null,emailVerified:r.emailVerified||!1,phoneNumber:r.phoneNumber||null,tenantId:r.tenantId||null,providerData:i,metadata:new we(r.createdAt,r.lastLoginAt),isAnonymous:!(r.email&&r.passwordHash||null!=i&&i.length)};return Object.assign(t,i),t}}const Re=new Map;function Ae(e){$(e instanceof Function,"Expected a class definition");let t=Re.get(e);return t?$(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,Re.set(e,t)),t}class Pe{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){e=this.storage[e];return void 0===e?null:e}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}Pe.type="NONE";const Ce=Pe;function Oe(e,t,n){return`firebase:${e}:${t}:${n}`}class Ne{constructor(e,t,n){this.persistence=e,this.auth=t,this.userKey=n;var{config:e,name:n}=this.auth;this.fullUserKey=Oe(this.userKey,e.apiKey,n),this.fullPersistenceKey=Oe("persistence",e.apiKey,n),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e=await this.persistence._get(this.fullUserKey);return e?Se._fromJSON(this.auth,e):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){if(this.persistence!==e){var t=await this.getCurrentUser();return await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(e,t,n="authUser"){if(!t.length)return new Ne(Ae(Ce),e,n);const r=(await Promise.all(t.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let i=r[0]||Ae(Ce);const s=Oe(n,e.config.apiKey,e.name);let a=null;for(const u of t)try{var o=await u._get(s);if(o){var c=Se._fromJSON(e,o);u!==i&&(a=c),i=u;break}}catch(e){}var l=r.filter(e=>e._shouldAllowMigration);return i._shouldAllowMigration&&l.length&&(i=l[0],a&&await i._set(s,a.toJSON()),await Promise.all(t.map(async e=>{if(e!==i)try{await e._remove(s)}catch(e){}}))),new Ne(i,e,n)}}function Le(e){const t=e.toLowerCase();if(t.includes("opera/")||t.includes("opr/")||t.includes("opios/"))return"Opera";if(Fe(t))return"IEMobile";if(t.includes("msie")||t.includes("trident/"))return"IE";if(t.includes("edge/"))return"Edge";if(De(t))return"Firefox";if(t.includes("silk/"))return"Silk";if(xe(t))return"Blackberry";if(je(t))return"Webos";if(Ue(t))return"Safari";if((t.includes("chrome/")||Me(t))&&!t.includes("edge/"))return"Chrome";if(Ve(t))return"Android";e=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/);return 2===(null==e?void 0:e.length)?e[1]:"Other"}function De(e=d()){return/firefox\//i.test(e)}function Ue(e=d()){const t=e.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}function Me(e=d()){return/crios\//i.test(e)}function Fe(e=d()){return/iemobile/i.test(e)}function Ve(e=d()){return/android/i.test(e)}function xe(e=d()){return/blackberry/i.test(e)}function je(e=d()){return/webos/i.test(e)}function He(e=d()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function We(e=d()){return He(e)||Ve(e)||je(e)||xe(e)||/windows phone/i.test(e)||Fe(e)}function qe(e,t=[]){let n;switch(e){case"Browser":n=Le(d());break;case"Worker":n=`${Le(d())}-${e}`;break;default:n=e}t=t.length?t.join(","):"FirebaseCore-web";return`${n}/JsCore/${xi.SDK_VERSION}/${t}`}class Be{constructor(e){this.auth=e,this.queue=[]}pushCallback(r,e){var t=n=>new Promise((e,t)=>{try{e(r(n))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);const n=this.queue.length-1;return()=>{this.queue[n]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){const t=[];try{for(const n of this.queue)await n(e),n.onAbort&&t.push(n.onAbort)}catch(e){t.reverse();for(const r of t)try{r()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==e?void 0:e.message})}}}}class ze{constructor(e){var t,n=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!==(t=n.minPasswordLength)&&void 0!==t?t:6,n.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=n.maxPasswordLength),void 0!==n.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=n.containsLowercaseCharacter),void 0!==n.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=n.containsUppercaseCharacter),void 0!==n.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=n.containsNumericCharacter),void 0!==n.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=n.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!==(n=null===(n=e.allowedNonAlphanumericCharacters)||void 0===n?void 0:n.join(""))&&void 0!==n?n:"",this.forceUpgradeOnSignin=null!==(n=e.forceUpgradeOnSignin)&&void 0!==n&&n,this.schemaVersion=e.schemaVersion}validatePassword(e){var t,n,r;const i={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,i),this.validatePasswordCharacterOptions(e,i),i.isValid&&(i.isValid=null===(t=i.meetsMinPasswordLength)||void 0===t||t),i.isValid&&(i.isValid=null===(t=i.meetsMaxPasswordLength)||void 0===t||t),i.isValid&&(i.isValid=null===(n=i.containsLowercaseLetter)||void 0===n||n),i.isValid&&(i.isValid=null===(n=i.containsUppercaseLetter)||void 0===n||n),i.isValid&&(i.isValid=null===(r=i.containsNumericCharacter)||void 0===r||r),i.isValid&&(i.isValid=null===(r=i.containsNonAlphanumericCharacter)||void 0===r||r),i}validatePasswordLengthOptions(e,t){var n=this.customStrengthOptions.minPasswordLength,r=this.customStrengthOptions.maxPasswordLength;n&&(t.meetsMinPasswordLength=e.length>=n),r&&(t.meetsMaxPasswordLength=e.length<=r)}validatePasswordCharacterOptions(t,n){var r;this.updatePasswordCharacterOptionsStatuses(n,!1,!1,!1,!1);for(let e=0;e<t.length;e++)r=t.charAt(e),this.updatePasswordCharacterOptionsStatuses(n,"a"<=r&&r<="z","A"<=r&&r<="Z","0"<=r&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(e,t,n,r,i){this.customStrengthOptions.containsLowercaseLetter&&(e.containsLowercaseLetter||(e.containsLowercaseLetter=t)),this.customStrengthOptions.containsUppercaseLetter&&(e.containsUppercaseLetter||(e.containsUppercaseLetter=n)),this.customStrengthOptions.containsNumericCharacter&&(e.containsNumericCharacter||(e.containsNumericCharacter=r)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter||(e.containsNonAlphanumericCharacter=i))}}class Ge{constructor(e,t,n,r){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=n,this.config=r,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new $e(this),this.idTokenSubscription=new $e(this),this.beforeStateQueue=new Be(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=F,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=r.sdkClientVersion}_initializeWithPersistence(t,n){return n&&(this._popupRedirectResolver=Ae(n)),this._initializationPromise=this.queue(async()=>{var e;if(!this._deleted&&(this.persistenceManager=await Ne.create(this,t),!this._deleted)){if(null!==(e=this._popupRedirectResolver)&&void 0!==e&&e._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(n),this.lastNotifiedUid=(null===(e=this.currentUser)||void 0===e?void 0:e.uid)||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){if(!this._deleted){var e=await this.assertedPersistence.getCurrentUser();if(this.currentUser||e)return this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),void await this.currentUser.getIdToken()):void await this._updateCurrentUser(e,!0)}}async initializeCurrentUserFromIdToken(e){try{var t=await fe(this,{idToken:e}),n=await Se._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(n)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(xi._isFirebaseServerApp(this.app)){const a=this.app.settings.authIdToken;return a?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(a).then(e,e))}):this.directlySetCurrentUser(null)}var t,n,r=await this.assertedPersistence.getCurrentUser();let i=r,s=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=null===(n=this.redirectUser)||void 0===n?void 0:n._redirectEventId,n=null===i||void 0===i?void 0:i._redirectEventId,e=await this.tryRedirectSignIn(e),t&&t!==n||null==e||!e.user||(i=e.user,s=!0)),!i)return this.directlySetCurrentUser(null);if(i._redirectEventId)return G(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===i._redirectEventId?this.directlySetCurrentUser(i):this.reloadAndSetCurrentUserOrClear(i);if(s)try{await this.beforeStateQueue.runMiddleware(i)}catch(e){i=r,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return i?this.reloadAndSetCurrentUserOrClear(i):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await Te(e)}catch(e){if("auth/network-request-failed"!==(null==e?void 0:e.code))return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){this.languageCode=function(){if("undefined"==typeof navigator)return null;var e=navigator;return e.languages&&e.languages[0]||e.language||null}()}async _delete(){this._deleted=!0}async updateCurrentUser(e){if(xi._isFirebaseServerApp(this.app))return Promise.reject(q(this));const t=e?k(e):null;return t&&G(t.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(t&&t._clone(this))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&G(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return xi._isFirebaseServerApp(this.app)?Promise.reject(q(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return xi._isFirebaseServerApp(this.app)?Promise.reject(q(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(Ae(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();const t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e=await ie(e=this,"GET","/v2/passwordPolicy",re(e,{})),e=new ze(e);null===this.tenantId?this._projectPasswordPolicy=e:this._tenantPasswordPolicies[this.tenantId]=e}_getPersistence(){return this.assertedPersistence.persistence.type}_updateErrorMap(e){this._errorFactory=new _("auth","Firebase",e())}onAuthStateChanged(e,t,n){return this.registerStateListener(this.authStateSubscription,e,t,n)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,n){return this.registerStateListener(this.idTokenSubscription,e,t,n)}authStateReady(){return new Promise((e,t)=>{if(this.currentUser)e();else{const n=this.onAuthStateChanged(()=>{n(),e()},t)}})}async revokeAccessToken(e){if(this.currentUser){const n={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()};null!=this.tenantId&&(n.tenantId=this.tenantId),t=this,e=n,await ie(t,"POST","/v2/accounts:revokeToken",re(t,e))}var t}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null===(e=this._currentUser)||void 0===e?void 0:e.toJSON()}}async _setRedirectUser(e,t){const n=await this.getOrInitRedirectPersistenceManager(t);return null===e?n.removeCurrentUser():n.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){return this.redirectPersistenceManager||(G(e=e&&Ae(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await Ne.create(this,[Ae(e._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){var t;return this._isInitialized&&await this.queue(async()=>{}),(null===(t=this._currentUser)||void 0===t?void 0:t._redirectEventId)===e?this._currentUser:(null===(t=this.redirectUser)||void 0===t?void 0:t._redirectEventId)===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:${this.name}`}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=null!==(e=null===(e=this.currentUser)||void 0===e?void 0:e.uid)&&void 0!==e?e:null,this.lastNotifiedUid!==e&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser)))}registerStateListener(e,t,n,r){if(this._deleted)return()=>{};const i="function"==typeof t?t:t.next.bind(t);let s=!1;const a=this._isInitialized?Promise.resolve():this._initializationPromise;if(G(a,this,"internal-error"),a.then(()=>{s||i(this.currentUser)}),"function"==typeof t){const o=e.addObserver(t,n,r);return()=>{s=!0,o()}}{const c=e.addObserver(t);return()=>{s=!0,c()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return G(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=qe(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){const e={"X-Client-Version":this.clientVersion};this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId);var t=await(null===(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))||void 0===t?void 0:t.getHeartbeatsHeader());t&&(e["X-Firebase-Client"]=t);t=await this._getAppCheckToken();return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,n=await(null===(t=this.appCheckServiceProvider.getImmediate({optional:!0}))||void 0===t?void 0:t.getToken());return null!=n&&n.error&&(e=`Error while retrieving App Check token: ${n.error}`,t=[],V.logLevel<=l.WARN&&V.warn(`Auth (${xi.SDK_VERSION}): ${e}`,...t)),null==n?void 0:n.token}}function Ke(e){return k(e)}class $e{constructor(e){this.auth=e,this.observer=null,this.addObserver=function(e,t){const n=new E(e,t);return n.subscribe.bind(n)}(e=>this.observer=e)}get next(){return G(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let Je={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function Ye(e){return Je.loadJS(e)}function Xe(e){return`__${e}${Math.floor(1e6*Math.random())}`}class Qe{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var n=this.counter;return this._widgets.set(n,new tt(e,this.auth.name,t||{})),this.counter++,n}reset(e){var t=e||1e12;null===(e=this._widgets.get(t))||void 0===e||e.delete(),this._widgets.delete(t)}getResponse(e){return(null===(e=this._widgets.get(e||1e12))||void 0===e?void 0:e.getResponse())||""}async execute(e){return null===(e=this._widgets.get(e||1e12))||void 0===e||e.execute(),""}}class Ze{constructor(){this.enterprise=new et}ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class et{ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class tt{constructor(e,t,n){this.params=n,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};e="string"==typeof e?document.getElementById(e):e;G(e,"argument-error",{appName:t}),this.container=e,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=function(t){const n=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<t;e++)n.push(r.charAt(Math.floor(Math.random()*r.length)));return n.join("")}(50);const{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}const nt="NO_RECAPTCHA";class rt{constructor(e){this.type="recaptcha-enterprise",this.auth=Ke(e)}async verify(i="verify",e=!1){function s(e,t,n){const r=window.grecaptcha;de(r)?r.enterprise.ready(()=>{r.enterprise.execute(e,{action:i}).then(e=>{t(e)}).catch(()=>{t(nt)})}):n(Error("No reCAPTCHA enterprise script loaded."))}if(this.auth.settings.appVerificationDisabledForTesting){const t=new Ze;return t.execute("siteKey",{action:"verify"})}return new Promise((n,r)=>{!async function(r){if(!e){if(null==r.tenantId&&null!=r._agentRecaptchaConfig)return r._agentRecaptchaConfig.siteKey;if(null!=r.tenantId&&void 0!==r._tenantRecaptchaConfigs[r.tenantId])return r._tenantRecaptchaConfigs[r.tenantId].siteKey}return new Promise(async(t,n)=>{pe(r,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{if(void 0!==e.recaptchaKey){e=new he(e);return null==r.tenantId?r._agentRecaptchaConfig=e:r._tenantRecaptchaConfigs[r.tenantId]=e,t(e.siteKey)}n(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{n(e)})})}(this.auth).then(t=>{if(!e&&de(window.grecaptcha))s(t,n,r);else if("undefined"!=typeof window){let e=Je.recaptchaEnterpriseScript;0!==e.length&&(e+=t),Ye(e).then(()=>{s(t,n,r)}).catch(e=>{r(e)})}else r(new Error("RecaptchaVerifier is only supported in browser"))}).catch(e=>{r(e)})})}}async function it(e,t,n,r=!1,i=!1){const s=new rt(e);let a;if(i)a=nt;else try{a=await s.verify(n)}catch(e){a=await s.verify(n,!0)}var o,t=Object.assign({},t);return"mfaSmsEnrollment"===n||"mfaSmsSignIn"===n?"phoneEnrollmentInfo"in t?(n=t.phoneEnrollmentInfo.phoneNumber,o=t.phoneEnrollmentInfo.recaptchaToken,Object.assign(t,{phoneEnrollmentInfo:{phoneNumber:n,recaptchaToken:o,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):"phoneSignInInfo"in t&&(o=t.phoneSignInInfo.recaptchaToken,Object.assign(t,{phoneSignInInfo:{recaptchaToken:o,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):(r?Object.assign(t,{captchaResp:a}):Object.assign(t,{captchaResponse:a}),Object.assign(t,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(t,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"})),t}async function st(n,r,i,s,e){if("EMAIL_PASSWORD_PROVIDER"===e){if(null!==(t=n._getRecaptchaConfig())&&void 0!==t&&t.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")){var t=await it(n,r,i,"getOobCode"===i);return s(n,t)}return s(n,r).catch(async e=>{if("auth/missing-recaptcha-token"!==e.code)return Promise.reject(e);console.log(`${i} is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.`);e=await it(n,r,i,"getOobCode"===i);return s(n,e)})}if("PHONE_PROVIDER"!==e)return Promise.reject(e+" provider is not supported.");if(null!==(e=n._getRecaptchaConfig())&&void 0!==e&&e.isProviderEnabled("PHONE_PROVIDER")){var a=await it(n,r,i);return s(n,a).catch(async e=>{var t;if("AUDIT"!==(null===(t=n._getRecaptchaConfig())||void 0===t?void 0:t.getProviderEnforcementState("PHONE_PROVIDER"))||"auth/missing-recaptcha-token"!==e.code&&"auth/invalid-app-credential"!==e.code)return Promise.reject(e);console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${i} flow.`);e=await it(n,r,i,!1,!0);return s(n,e)})}a=await it(n,r,i,!1,!0);return s(n,a)}function at(e,t,n){const r=Ke(e);G(r._canInitEmulator,r,"emulator-config-failed"),G(/^https?:\/\//.test(t),r,"invalid-emulator-scheme");e=!(null==n||!n.disableWarnings);const i=ot(t);var{host:n,port:t}=function(e){const t=ot(e),n=/(\/\/)?([^?#/]+)/.exec(e.substr(t.length));if(!n)return{host:"",port:null};const r=n[2].split("@").pop()||"",i=/^(\[[^\]]+\])(:|$)/.exec(r);{if(i){var s=i[1];return{host:s,port:ct(r.substr(s.length+1))}}var[e,s]=r.split(":");return{host:e,port:ct(s)}}}(t);r.config.emulator={url:`${i}//${n}${null===t?"":`:${t}`}/`},r.settings.appVerificationDisabledForTesting=!0,r.emulatorConfig=Object.freeze({host:n,port:t,protocol:i.replace(":",""),options:Object.freeze({disableWarnings:e})}),e||function(){function e(){const e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}"undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.");"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",e):e())}()}function ot(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function ct(e){if(!e)return null;e=Number(e);return isNaN(e)?null:e}class lt{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return K("not implemented")}_getIdTokenResponse(e){return K("not implemented")}_linkToIdToken(e,t){return K("not implemented")}_getReauthenticationResolver(e){return K("not implemented")}}async function ut(e,t){return ie(e,"POST","/v1/accounts:resetPassword",re(e,t))}async function dt(e,t){return ie(e,"POST","/v1/accounts:signUp",t)}async function ht(e,t){return ae(e,"POST","/v1/accounts:signInWithPassword",re(e,t))}async function pt(e,t){return ie(e,"POST","/v1/accounts:sendOobCode",re(e,t))}async function ft(e,t){return pt(e,t)}async function mt(e,t){return pt(e,t)}class gt extends lt{constructor(e,t,n,r=null){super("password",n),this._email=e,this._password=t,this._tenantId=r}static _fromEmailAndPassword(e,t){return new gt(e,t,"password")}static _fromEmailAndCode(e,t,n=null){return new gt(e,t,"emailLink",n)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){e="string"==typeof e?JSON.parse(e):e;if(null!=e&&e.email&&null!=e&&e.password){if("password"===e.signInMethod)return this._fromEmailAndPassword(e.email,e.password);if("emailLink"===e.signInMethod)return this._fromEmailAndCode(e.email,e.password,e.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return st(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",ht,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithEmailLink",re(e,t))}(e,{email:this._email,oobCode:this._password});default:j(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return st(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",dt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithEmailLink",re(e,t))}(e,{idToken:t,email:this._email,oobCode:this._password});default:j(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function vt(e,t){return ae(e,"POST","/v1/accounts:signInWithIdp",re(e,t))}class _t extends lt{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){const t=new _t(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):j("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e,{providerId:n,signInMethod:e}=t,t=C(t,["providerId","signInMethod"]);if(!n||!e)return null;const r=new _t(n,e);return r.idToken=t.idToken||void 0,r.accessToken=t.accessToken||void 0,r.secret=t.secret,r.nonce=t.nonce,r.pendingToken=t.pendingToken||null,r}_getIdTokenResponse(e){return vt(e,this.buildRequest())}_linkToIdToken(e,t){const n=this.buildRequest();return n.idToken=t,vt(e,n)}_getReauthenticationResolver(e){const t=this.buildRequest();return t.autoCreate=!1,vt(e,t)}buildRequest(){const e={requestUri:"http://localhost",returnSecureToken:!0};if(this.pendingToken)e.pendingToken=this.pendingToken;else{const t={};this.idToken&&(t.id_token=this.idToken),this.accessToken&&(t.access_token=this.accessToken),this.secret&&(t.oauth_token_secret=this.secret),t.providerId=this.providerId,this.nonce&&!this.pendingToken&&(t.nonce=this.nonce),e.postBody=I(t)}return e}}async function yt(e,t){return ie(e,"POST","/v1/accounts:sendVerificationCode",re(e,t))}const It={USER_NOT_FOUND:"user-not-found"};class wt extends lt{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new wt({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new wt({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithPhoneNumber",re(e,t))}(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return async function(e,t){if((t=await ae(e,"POST","/v1/accounts:signInWithPhoneNumber",re(e,t))).temporaryProof)throw le(e,"account-exists-with-different-credential",t);return t}(e,Object.assign({idToken:t},this._makeVerificationRequest()))}_getReauthenticationResolver(e){return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithPhoneNumber",re(e,Object.assign(Object.assign({},t),{operation:"REAUTH"})),It)}(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:n,verificationCode:r}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:n,code:r}}toJSON(){const e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:t,verificationCode:n,phoneNumber:r,temporaryProof:e}=e="string"==typeof e?JSON.parse(e):e;return n||t||r||e?new wt({verificationId:t,verificationCode:n,phoneNumber:r,temporaryProof:e}):null}}class Tt{constructor(e){var t=w(T(e)),n=null!==(r=t.apiKey)&&void 0!==r?r:null,r=null!==(e=t.oobCode)&&void 0!==e?e:null,e=function(e){switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}}(null!==(e=t.mode)&&void 0!==e?e:null);G(n&&r&&e,"argument-error"),this.apiKey=n,this.operation=e,this.code=r,this.continueUrl=null!==(r=t.continueUrl)&&void 0!==r?r:null,this.languageCode=null!==(r=t.languageCode)&&void 0!==r?r:null,this.tenantId=null!==(t=t.tenantId)&&void 0!==t?t:null}static parseLink(e){var t,n,r,t=(n=w(T(t=e)).link,r=n?w(T(n)).deep_link_id:null,((e=w(T(t)).deep_link_id)?w(T(e)).link:null)||e||r||n||t);try{return new Tt(t)}catch(e){return null}}}class Et{constructor(){this.providerId=Et.PROVIDER_ID}static credential(e,t){return gt._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){t=Tt.parseLink(t);return G(t,"argument-error"),gt._fromEmailAndCode(e,t.code,t.tenantId)}}Et.PROVIDER_ID="password",Et.EMAIL_PASSWORD_SIGN_IN_METHOD="password",Et.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class bt{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class kt extends bt{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class St extends kt{static credentialFromJSON(e){e="string"==typeof e?JSON.parse(e):e;return G("providerId"in e&&"signInMethod"in e,"argument-error"),_t._fromParams(e)}credential(e){return this._credential(Object.assign(Object.assign({},e),{nonce:e.rawNonce}))}_credential(e){return G(e.idToken||e.accessToken,"argument-error"),_t._fromParams(Object.assign(Object.assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))}static credentialFromResult(e){return St.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return St.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:n,oauthTokenSecret:r,pendingToken:i,nonce:s,providerId:e}=e;if(!(n||r||t||i))return null;if(!e)return null;try{return new St(e)._credential({idToken:t,accessToken:n,nonce:s,pendingToken:i})}catch(e){return null}}}class Rt extends kt{constructor(){super("facebook.com")}static credential(e){return _t._fromParams({providerId:Rt.PROVIDER_ID,signInMethod:Rt.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return Rt.credentialFromTaggedObject(e)}static credentialFromError(e){return Rt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return Rt.credential(e.oauthAccessToken)}catch(e){return null}}}Rt.FACEBOOK_SIGN_IN_METHOD="facebook.com",Rt.PROVIDER_ID="facebook.com";class At extends kt{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return _t._fromParams({providerId:At.PROVIDER_ID,signInMethod:At.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return At.credentialFromTaggedObject(e)}static credentialFromError(e){return At.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:e}=e;if(!t&&!e)return null;try{return At.credential(t,e)}catch(e){return null}}}At.GOOGLE_SIGN_IN_METHOD="google.com",At.PROVIDER_ID="google.com";class Pt extends kt{constructor(){super("github.com")}static credential(e){return _t._fromParams({providerId:Pt.PROVIDER_ID,signInMethod:Pt.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return Pt.credentialFromTaggedObject(e)}static credentialFromError(e){return Pt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return Pt.credential(e.oauthAccessToken)}catch(e){return null}}}Pt.GITHUB_SIGN_IN_METHOD="github.com",Pt.PROVIDER_ID="github.com";class Ct extends lt{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return vt(e,this.buildRequest())}_linkToIdToken(e,t){const n=this.buildRequest();return n.idToken=t,vt(e,n)}_getReauthenticationResolver(e){const t=this.buildRequest();return t.autoCreate=!1,vt(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:t,signInMethod:n,pendingToken:e}="string"==typeof e?JSON.parse(e):e;return t&&n&&e&&t===n?new Ct(t,e):null}static _create(e,t){return new Ct(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class Ot extends bt{constructor(e){G(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return Ot.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return Ot.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){e=Ct.fromJSON(e);return G(e,"argument-error"),e}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:t,providerId:e}=e;if(!t||!e)return null;try{return Ct._create(e,t)}catch(e){return null}}}class Nt extends kt{constructor(){super("twitter.com")}static credential(e,t){return _t._fromParams({providerId:Nt.PROVIDER_ID,signInMethod:Nt.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return Nt.credentialFromTaggedObject(e)}static credentialFromError(e){return Nt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:t,oauthTokenSecret:e}=e;if(!t||!e)return null;try{return Nt.credential(t,e)}catch(e){return null}}}async function Lt(e,t){return ae(e,"POST","/v1/accounts:signUp",re(e,t))}Nt.TWITTER_SIGN_IN_METHOD="twitter.com",Nt.PROVIDER_ID="twitter.com";class Dt{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,n,r=!1){e=await Se._fromIdTokenResponse(e,n,r),r=Ut(n);return new Dt({user:e,providerId:r,_tokenResponse:n,operationType:t})}static async _forOperation(e,t,n){await e._updateTokensIfNecessary(n,!0);var r=Ut(n);return new Dt({user:e,providerId:r,_tokenResponse:n,operationType:t})}}function Ut(e){return e.providerId||("phoneNumber"in e?"phone":null)}class Mt extends v{constructor(e,t,n,r){super(t.code,t.message),this.operationType=n,this.user=r,Object.setPrototypeOf(this,Mt.prototype),this.customData={appName:e.name,tenantId:null!==(e=e.tenantId)&&void 0!==e?e:void 0,_serverResponse:t.customData._serverResponse,operationType:n}}static _fromErrorAndOperation(e,t,n,r){return new Mt(e,t,n,r)}}function Ft(t,n,e,r){const i="reauthenticate"===n?e._getReauthenticationResolver(t):e._getIdTokenResponse(t);return i.catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw Mt._fromErrorAndOperation(t,e,n,r);throw e})}function Vt(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function xt(e,t){const n=k(e);await Ht(!0,n,t);var t=(e=n.auth,t={idToken:await n.getIdToken(),deleteProvider:[t]},await ie(e,"POST","/v1/accounts:update",t))["providerUserInfo"];const r=Vt(t||[]);return n.providerData=n.providerData.filter(e=>r.has(e.providerId)),r.has("phone")||(n.phoneNumber=null),await n.auth._persistUserIfCurrent(n),n}async function jt(e,t,n=!1){n=await ye(e,t._linkToIdToken(e.auth,await e.getIdToken()),n);return Dt._forOperation(e,"link",n)}async function Ht(e,t,n){await Te(t);const r=Vt(t.providerData);var i=!1===e?"provider-already-linked":"no-such-provider";G(r.has(n)===e,t.auth,i)}async function Wt(e,t,n=!1){var r=e["auth"];if(xi._isFirebaseServerApp(r.app))return Promise.reject(q(r));var i="reauthenticate";try{var s=await ye(e,Ft(r,i,t,e),n);G(s.idToken,r,"internal-error");var a=ve(s.idToken);G(a,r,"internal-error");var o=a["sub"];return G(e.uid===o,r,"user-mismatch"),Dt._forOperation(e,i,s)}catch(e){throw"auth/user-not-found"===(null==e?void 0:e.code)&&j(r,"user-mismatch"),e}}async function qt(e,t,n=!1){if(xi._isFirebaseServerApp(e.app))return Promise.reject(q(e));t=await Ft(e,"signIn",t),t=await Dt._fromIdTokenResponse(e,"signIn",t);return n||await e._updateCurrentUser(t.user),t}async function Bt(e,t){return qt(Ke(e),t)}async function zt(e,t){e=k(e);return await Ht(!1,e,t.providerId),jt(e,t)}async function Gt(e,t){return Wt(k(e),t)}async function Kt(e,t){if(xi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const n=Ke(e);t=await ae(n,"POST","/v1/accounts:signInWithCustomToken",re(n,{token:t,returnSecureToken:!0})),t=await Dt._fromIdTokenResponse(n,"signIn",t);return await n._updateCurrentUser(t.user),t}class $t{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?Jt._fromServerResponse(e,t):"totpInfo"in t?Yt._fromServerResponse(e,t):j(e,"internal-error")}}class Jt extends $t{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new Jt(t)}}class Yt extends $t{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new Yt(t)}}function Xt(e,t,n){var r;G(0<(null===(r=n.url)||void 0===r?void 0:r.length),e,"invalid-continue-uri"),G(void 0===n.dynamicLinkDomain||0<n.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),t.continueUrl=n.url,t.dynamicLinkDomain=n.dynamicLinkDomain,t.canHandleCodeInApp=n.handleCodeInApp,n.iOS&&(G(0<n.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=n.iOS.bundleId),n.android&&(G(0<n.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=n.android.installApp,t.androidMinimumVersionCode=n.android.minimumVersion,t.androidPackageName=n.android.packageName)}async function Qt(e){const t=Ke(e);t._getPasswordPolicyInternal()&&await t._updatePasswordPolicy()}async function Zt(e,t){await ie(e=k(e),"POST","/v1/accounts:update",re(e,{oobCode:t}))}async function en(e,t){var n=k(e),r=await ut(n,{oobCode:t}),t=r.requestType;switch(G(t,n,"internal-error"),t){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":G(r.newEmail,n,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":G(r.mfaInfo,n,"internal-error");default:G(r.email,n,"internal-error")}let i=null;return r.mfaInfo&&(i=$t._fromServerResponse(Ke(n),r.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===r.requestType?r.newEmail:r.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===r.requestType?r.email:r.newEmail)||null,multiFactorInfo:i},operation:t}}async function tn(e,t){var n=Y()?J():"http://localhost",n=(await ie(e=k(e),"POST","/v1/accounts:createAuthUri",re(e,{identifier:t,continueUri:n})))["signinMethods"];return n||[]}async function nn(e,t){var n=k(e),r={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()};t&&Xt(n.auth,r,t);var r=(await pt(n.auth,r))["email"];r!==e.email&&await e.reload()}async function rn(e,t,n){var r=k(e),t={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t};n&&Xt(r.auth,t,n);var t=(await pt(r.auth,t))["email"];t!==e.email&&await e.reload()}async function sn(e,{displayName:t,photoURL:n}){if(void 0!==t||void 0!==n){const r=k(e);e=await r.getIdToken(),n=await ye(r,async function(e,t){return ie(e,"POST","/v1/accounts:update",t)}(r.auth,{idToken:e,displayName:t,photoUrl:n,returnSecureToken:!0}));r.displayName=n.displayName||null,r.photoURL=n.photoUrl||null;const i=r.providerData.find(({providerId:e})=>"password"===e);i&&(i.displayName=r.displayName,i.photoURL=r.photoURL),await r._updateTokensIfNecessary(n)}}async function an(e,t,n){var r=e["auth"];const i={idToken:await e.getIdToken(),returnSecureToken:!0};t&&(i.email=t),n&&(i.password=n);r=await ye(e,async function(e,t){return ie(e,"POST","/v1/accounts:update",t)}(r,i));await e._updateTokensIfNecessary(r,!0)}class on{constructor(e,t,n={}){this.isNewUser=e,this.providerId=t,this.profile=n}}class cn extends on{constructor(e,t,n,r){super(e,t,n),this.username=r}}class ln extends on{constructor(e,t){super(e,"facebook.com",t)}}class un extends cn{constructor(e,t){super(e,"github.com",t,"string"==typeof(null==t?void 0:t.login)?null==t?void 0:t.login:null)}}class dn extends on{constructor(e,t){super(e,"google.com",t)}}class hn extends cn{constructor(e,t,n){super(e,"twitter.com",t,n)}}function pn(e){var{user:t,_tokenResponse:e}=e;return t.isAnonymous&&!e?{providerId:null,isNewUser:!1,profile:null}:function(e){if(!e)return null;var t=e["providerId"],n=e.rawUserInfo?JSON.parse(e.rawUserInfo):{},r=e.isNewUser||"identitytoolkit#SignupNewUserResponse"===e.kind;if(!t&&null!=e&&e.idToken){var i=null===(i=null===(i=ve(e.idToken))||void 0===i?void 0:i.firebase)||void 0===i?void 0:i.sign_in_provider;if(i){i="anonymous"!==i&&"custom"!==i?i:null;return new on(r,i)}}if(!t)return null;switch(t){case"facebook.com":return new ln(r,n);case"github.com":return new un(r,n);case"google.com":return new dn(r,n);case"twitter.com":return new hn(r,n,e.screenName||null);case"custom":case"anonymous":return new on(r,null);default:return new on(r,t,n)}}(e)}class fn{constructor(e,t,n){this.type=e,this.credential=t,this.user=n}static _fromIdtoken(e,t){return new fn("enroll",e,t)}static _fromMfaPendingCredential(e){return new fn("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){var t;if(null!=e&&e.multiFactorSession){if(null!==(t=e.multiFactorSession)&&void 0!==t&&t.pendingCredential)return fn._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null!==(t=e.multiFactorSession)&&void 0!==t&&t.idToken)return fn._fromIdtoken(e.multiFactorSession.idToken)}return null}}class mn{constructor(e,t,n){this.session=e,this.hints=t,this.signInResolver=n}static _fromError(e,r){const i=Ke(e),s=r.customData._serverResponse;e=(s.mfaInfo||[]).map(e=>$t._fromServerResponse(i,e));G(s.mfaPendingCredential,i,"internal-error");const a=fn._fromMfaPendingCredential(s.mfaPendingCredential);return new mn(a,e,async e=>{e=await e._process(i,a);delete s.mfaInfo,delete s.mfaPendingCredential;var t=Object.assign(Object.assign({},s),{idToken:e.idToken,refreshToken:e.refreshToken});switch(r.operationType){case"signIn":var n=await Dt._fromIdTokenResponse(i,r.operationType,t);return await i._updateCurrentUser(n.user),n;case"reauthenticate":return G(r.user,i,"internal-error"),Dt._forOperation(r.user,r.operationType,t);default:j(i,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}function gn(e,t){return ie(e,"POST","/v2/accounts/mfaEnrollment:start",re(e,t))}class vn{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>$t._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new vn(e)}async getSession(){return fn._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){const n=e;e=await this.getSession(),t=await ye(this.user,n._process(this.user.auth,e,t));return await this.user._updateTokensIfNecessary(t),this.user.reload()}async unenroll(e){const t="string"==typeof e?e:e.uid;var n,r,e=await this.user.getIdToken();try{var i=await ye(this.user,(n=this.user.auth,r={idToken:e,mfaEnrollmentId:t},ie(n,"POST","/v2/accounts/mfaEnrollment:withdraw",re(n,r))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(i),await this.user.reload()}catch(e){throw e}}}const _n=new WeakMap;const yn="__sak";class In{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(yn,"1"),this.storage.removeItem(yn),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){e=this.storage.getItem(e);return Promise.resolve(e?JSON.parse(e):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class wn extends In{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=We(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(const r of Object.keys(this.listeners)){var t=this.storage.getItem(r),n=this.localCache[r];t!==n&&e(r,n,t)}}onStorageEvent(e,t=!1){if(e.key){const i=e.key;t?this.detachListener():this.stopPolling();var n=()=>{var e=this.storage.getItem(i);!t&&this.localCache[i]===e||this.notifyListeners(i,e)},r=this.storage.getItem(i);m()&&10===document.documentMode&&r!==e.newValue&&e.newValue!==e.oldValue?setTimeout(n,10):n()}else this.forAllChangedKeys((e,t,n)=>{this.notifyListeners(e,n)})}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(const n of Array.from(e))n(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,n)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:n}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}wn.type="LOCAL";const Tn=wn;class En extends In{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}En.type="SESSION";const bn=En;class kn{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));if(e)return e;e=new kn(t);return this.receivers.push(e),e}isListeningto(e){return this.eventTarget===e}async handleEvent(e){const t=e,{eventId:n,eventType:r,data:i}=t.data;e=this.handlersMap[r];null!=e&&e.size&&(t.ports[0].postMessage({status:"ack",eventId:n,eventType:r}),e=Array.from(e).map(async e=>e(t.origin,i)),e=await Promise.all(e.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:n,eventType:r,response:e}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function Sn(e="",t=10){let n="";for(let e=0;e<t;e++)n+=Math.floor(10*Math.random());return e+n}kn.receivers=[];class Rn{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,a=50){const o="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!o)throw new Error("connection_unavailable");let c,l;return new Promise((n,r)=>{const i=Sn("",20);o.port1.start();const s=setTimeout(()=>{r(new Error("unsupported_event"))},a);l={messageChannel:o,onMessage(e){var t=e;if(t.data.eventId===i)switch(t.data.status){case"ack":clearTimeout(s),c=setTimeout(()=>{r(new Error("timeout"))},3e3);break;case"done":clearTimeout(c),n(t.data.response);break;default:clearTimeout(s),clearTimeout(c),r(new Error("invalid_response"))}}},this.handlers.add(l),o.port1.addEventListener("message",l.onMessage),this.target.postMessage({eventType:e,eventId:i,data:t},[o.port2])}).finally(()=>{l&&this.removeMessageHandler(l)})}}function An(){return window}function Pn(){return void 0!==An().WorkerGlobalScope&&"function"==typeof An().importScripts}const Cn="firebaseLocalStorageDb",On="firebaseLocalStorage",Nn="fbase_key";class Ln{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function Dn(e,t){return e.transaction([On],t?"readwrite":"readonly").objectStore(On)}function Un(){const r=indexedDB.open(Cn,1);return new Promise((n,t)=>{r.addEventListener("error",()=>{t(r.error)}),r.addEventListener("upgradeneeded",()=>{const e=r.result;try{e.createObjectStore(On,{keyPath:Nn})}catch(e){t(e)}}),r.addEventListener("success",async()=>{const e=r.result;var t;e.objectStoreNames.contains(On)?n(e):(e.close(),t=indexedDB.deleteDatabase(Cn),await new Ln(t).toPromise(),n(await Un()))})})}async function Mn(e,t,n){n=Dn(e,!0).put({fbase_key:t,value:n});return new Ln(n).toPromise()}function Fn(e,t){t=Dn(e,!0).delete(t);return new Ln(t).toPromise()}class Vn{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Un(),this.db)}async _withRetries(e){let t=0;for(;;)try{return e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Pn()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=kn._getInstance(Pn()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>{const n=await this._poll();return{keyProcessed:n.includes(t.key)}}),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e,t,n;this.activeServiceWorker=await async function(){if(null===navigator||void 0===navigator||!navigator.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch(e){return null}}(),this.activeServiceWorker&&(this.sender=new Rn(this.activeServiceWorker),(n=await this.sender._send("ping",{},800))&&null!==(e=n[0])&&void 0!==e&&e.fulfilled&&null!==(t=n[0])&&void 0!==t&&t.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0))}async notifyServiceWorker(e){var t;if(this.sender&&this.activeServiceWorker&&((null===(t=null===navigator||void 0===navigator?void 0:navigator.serviceWorker)||void 0===t?void 0:t.controller)||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch(e){}}async _isAvailable(){try{if(!indexedDB)return!1;var e=await Un();return await Mn(e,yn,"1"),await Fn(e,yn),!0}catch(e){}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,n){return this._withPendingWrite(async()=>(await this._withRetries(e=>Mn(e,t,n)),this.localCache[t]=n,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>async function(e,t){return t=Dn(e,!1).get(t),void 0===(t=await new Ln(t).toPromise())?null:t.value}(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>Fn(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{e=Dn(e,!1).getAll();return new Ln(e).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];const t=[],n=new Set;if(0!==e.length)for(var{fbase_key:r,value:i}of e)n.add(r),JSON.stringify(this.localCache[r])!==JSON.stringify(i)&&(this.notifyListeners(r,i),t.push(r));for(const s of Object.keys(this.localCache))this.localCache[s]&&!n.has(s)&&(this.notifyListeners(s,null),t.push(s));return t}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(const n of Array.from(e))n(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&this.stopPolling()}}Vn.type="LOCAL";const xn=Vn;function jn(e,t){return ie(e,"POST","/v2/accounts/mfaSignIn:start",re(e,t))}const Hn=Xe("rcb"),Wn=new Q(3e4,6e4);class qn{constructor(){var e;this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!(null===(e=An().grecaptcha)||void 0===e||!e.render)}load(s,a=""){var e;return G((e=a).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),s,"argument-error"),this.shouldResolveImmediately(a)&&ue(An().grecaptcha)?Promise.resolve(An().grecaptcha):new Promise((t,r)=>{const i=An().setTimeout(()=>{r(H(s,"network-request-failed"))},Wn.get());An()[Hn]=()=>{An().clearTimeout(i),delete An()[Hn];const e=An().grecaptcha;if(e&&ue(e)){const n=e.render;e.render=(e,t)=>{t=n(e,t);return this.counter++,t},this.hostLanguage=a,t(e)}else r(H(s,"internal-error"))},Ye(`${Je.recaptchaV2Script}?${I({onload:Hn,render:"explicit",hl:a})}`).catch(()=>{clearTimeout(i),r(H(s,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){var t;return!(null===(t=An().grecaptcha)||void 0===t||!t.render)&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class Bn{async load(e){return new Qe(e)}clearedOneInstance(){}}const zn="recaptcha",Gn={theme:"light",type:"image"};class Kn{constructor(e,t,n=Object.assign({},Gn)){this.parameters=n,this.type=zn,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=Ke(e),this.isInvisible="invisible"===this.parameters.size,G("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");t="string"==typeof t?document.getElementById(t):t;G(t,this.auth,"argument-error"),this.container=t,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?Bn:qn),this.validateStartingState()}async verify(){this.assertNotDestroyed();const e=await this.render(),r=this.getAssertedRecaptcha();var t=r.getResponse(e);return t||new Promise(t=>{const n=e=>{e&&(this.tokenChangeListeners.delete(n),t(e))};this.tokenChangeListeners.add(n),this.isInvisible&&r.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e}),this.renderPromise)}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){G(!this.parameters.sitekey,this.auth,"argument-error"),G(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),G("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(n){return t=>{if(this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof n)n(t);else if("string"==typeof n){const e=An()[n];"function"==typeof e&&e(t)}}}assertNotDestroyed(){G(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){G(Y()&&!Pn(),this.auth,"internal-error"),await function(){let t=null;return new Promise(e=>{"complete"!==document.readyState?(t=()=>e(),window.addEventListener("load",t)):e()}).catch(e=>{throw t&&window.removeEventListener("load",t),e})}(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await ie(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");G(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return G(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class $n{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){e=wt._fromVerification(this.verificationId,e);return this.onConfirmation(e)}}async function Jn(t,n,r){var i;if(!t._getRecaptchaConfig())try{await async function(e){const t=Ke(e);e=await pe(t,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"});const n=new he(e);if(null==t.tenantId?t._agentRecaptchaConfig=n:t._tenantRecaptchaConfigs[t.tenantId]=n,n.isAnyProviderEnabled()){const r=new rt(t);r.verify()}}(t)}catch(e){console.log("Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.")}try{let e;if(e="string"==typeof n?{phoneNumber:n}:n,"session"in e){var s=e.session;if("phoneNumber"in e){G("enroll"===s.type,t,"internal-error");var a={idToken:s.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"}};const u=st(t,a,"mfaSmsEnrollment",async(e,t)=>{return t.phoneEnrollmentInfo.captchaResponse!==nt?gn(e,t):(G((null==r?void 0:r.type)===zn,e,"argument-error"),gn(e,await Yn(e,t,r)))},"PHONE_PROVIDER");return(await u.catch(e=>Promise.reject(e))).phoneSessionInfo.sessionInfo}{G("signin"===s.type,t,"internal-error");var o=(null===(i=e.multiFactorHint)||void 0===i?void 0:i.uid)||e.multiFactorUid;G(o,t,"missing-multi-factor-info");var c={mfaPendingCredential:s.credential,mfaEnrollmentId:o,phoneSignInInfo:{clientType:"CLIENT_TYPE_WEB"}};const d=st(t,c,"mfaSmsSignIn",async(e,t)=>{return t.phoneSignInInfo.captchaResponse!==nt?jn(e,t):(G((null==r?void 0:r.type)===zn,e,"argument-error"),jn(e,await Yn(e,t,r)))},"PHONE_PROVIDER");return(await d.catch(e=>Promise.reject(e))).phoneResponseInfo.sessionInfo}}{var l={phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"};const h=st(t,l,"sendVerificationCode",async(e,t)=>{return t.captchaResponse!==nt?yt(e,t):(G((null==r?void 0:r.type)===zn,e,"argument-error"),yt(e,await Yn(e,t,r)))},"PHONE_PROVIDER");return(await h.catch(e=>Promise.reject(e))).sessionInfo}}finally{null!=r&&r._reset()}}async function Yn(e,t,n){G(n.type===zn,e,"argument-error");n=await n.verify();G("string"==typeof n,e,"argument-error");e=Object.assign({},t);if("phoneEnrollmentInfo"in e){var t=e.phoneEnrollmentInfo.phoneNumber,r=e.phoneEnrollmentInfo.captchaResponse,i=e.phoneEnrollmentInfo.clientType,s=e.phoneEnrollmentInfo.recaptchaVersion;return Object.assign(e,{phoneEnrollmentInfo:{phoneNumber:t,recaptchaToken:n,captchaResponse:r,clientType:i,recaptchaVersion:s}}),e}if("phoneSignInInfo"in e){r=e.phoneSignInInfo.captchaResponse,i=e.phoneSignInInfo.clientType,s=e.phoneSignInInfo.recaptchaVersion;return Object.assign(e,{phoneSignInInfo:{recaptchaToken:n,captchaResponse:r,clientType:i,recaptchaVersion:s}}),e}return Object.assign(e,{recaptchaToken:n}),e}class Xn{constructor(e){this.providerId=Xn.PROVIDER_ID,this.auth=Ke(e)}verifyPhoneNumber(e,t){return Jn(this.auth,e,k(t))}static credential(e,t){return wt._fromVerification(e,t)}static credentialFromResult(e){return Xn.credentialFromTaggedObject(e)}static credentialFromError(e){return Xn.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{phoneNumber:t,temporaryProof:e}=e;return t&&e?wt._fromTokenResponse(t,e):null}}function Qn(e,t){return t?Ae(t):(G(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}Xn.PROVIDER_ID="phone",Xn.PHONE_SIGN_IN_METHOD="phone";class Zn extends lt{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return vt(e,this._buildIdpRequest())}_linkToIdToken(e,t){return vt(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return vt(e,this._buildIdpRequest())}_buildIdpRequest(e){const t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function er(e){return qt(e.auth,new Zn(e),e.bypassAuthState)}function tr(e){var{auth:t,user:n}=e;return G(n,t,"internal-error"),Wt(n,new Zn(e),e.bypassAuthState)}async function nr(e){var{auth:t,user:n}=e;return G(n,t,"internal-error"),jt(n,new Zn(e),e.bypassAuthState)}class rr{constructor(e,t,n,r,i=!1){this.auth=e,this.resolver=n,this.user=r,this.bypassAuthState=i,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:t,sessionId:n,postBody:r,tenantId:i,error:s,type:e}=e;if(s)this.reject(s);else{r={auth:this.auth,requestUri:t,sessionId:n,tenantId:i||void 0,postBody:r||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(e)(r))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return er;case"linkViaPopup":case"linkViaRedirect":return nr;case"reauthViaPopup":case"reauthViaRedirect":return tr;default:j(this.auth,"internal-error")}}resolve(e){$(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){$(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}const ir=new Q(2e3,1e4);class sr extends rr{constructor(e,t,n,r,i){super(e,t,r,i),this.provider=n,this.authWindow=null,this.pollId=null,sr.currentPopupAction&&sr.currentPopupAction.cancel(),sr.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return G(e,this.auth,"internal-error"),e}async onExecution(){$(1===this.filter.length,"Popup operations only handle one event");var e=Sn();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(H(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){var e;return(null===(e=this.authWindow)||void 0===e?void 0:e.associatedEvent)||null}cancel(){this.reject(H(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,sr.currentPopupAction=null}pollUserCancellation(){const t=()=>{var e;null!==(e=null===(e=this.authWindow)||void 0===e?void 0:e.window)&&void 0!==e&&e.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(H(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(t,ir.get())};t()}}sr.currentPopupAction=null;const ar="pendingRedirect",or=new Map;class cr extends rr{constructor(e,t,n=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,n),this.eventId=null}async execute(){let t=or.get(this.auth._key());if(!t){try{const e=await async function(e,t){const n=hr(t),r=dr(e);if(!await r._isAvailable())return!1;e="true"===await r._get(n);return await r._remove(n),e}(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}or.set(this.auth._key(),t)}return this.bypassAuthState||or.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"!==e.type){if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}else this.resolve(null)}async onExecution(){}cleanUp(){}}async function lr(e,t){return dr(e)._set(hr(t),"true")}function ur(e,t){or.set(e._key(),t)}function dr(e){return Ae(e._redirectPersistence)}function hr(e){return Oe(ar,e.config.apiKey,e.name)}function pr(e,t,n){return async function(e,t,n){if(xi._isFirebaseServerApp(e.app))return Promise.reject(q(e));var r=Ke(e);B(e,t,bt),await r._initializationPromise;const i=Qn(r,n);return await lr(i,r),i._openRedirect(r,t,"signInViaRedirect")}(e,t,n)}function fr(e,t,n){return async function(e,t,n){e=k(e);if(B(e.auth,t,bt),xi._isFirebaseServerApp(e.auth.app))return Promise.reject(q(e.auth));await e.auth._initializationPromise;const r=Qn(e.auth,n);await lr(r,e.auth);n=await vr(e);return r._openRedirect(e.auth,t,"reauthViaRedirect",n)}(e,t,n)}function mr(e,t,n){return async function(e,t,n){e=k(e);B(e.auth,t,bt),await e.auth._initializationPromise;const r=Qn(e.auth,n);await Ht(!1,e,t.providerId),await lr(r,e.auth);n=await vr(e);return r._openRedirect(e.auth,t,"linkViaRedirect",n)}(e,t,n)}async function gr(e,t,n=!1){if(xi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const r=Ke(e);e=Qn(r,t);const i=new cr(r,e,n),s=await i.execute();return s&&!n&&(delete s.user._redirectEventId,await r._persistUserIfCurrent(s.user),await r._setRedirectUser(null,t)),s}async function vr(e){var t=Sn(`${e.uid}:::`);return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class _r{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let n=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(n=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!function(e){switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return!0;case"unknown":return Ir(e);default:return!1}}(t)||(this.hasHandledPotentialRedirect=!0,n||(this.queuedRedirectEvent=t,n=!0)),n}sendToConsumer(e,t){var n;e.error&&!Ir(e)?(n=(null===(n=e.error.code)||void 0===n?void 0:n.split("auth/")[1])||"internal-error",t.onError(H(this.auth,n))):t.onAuthEvent(e)}isEventForConsumer(e,t){var n=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&n}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(yr(e))}saveEventToCache(e){this.cachedEventUids.add(yr(e)),this.lastProcessedEventTime=Date.now()}}function yr(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function Ir({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===(null==t?void 0:t.code)}async function wr(e,t={}){return ie(e,"GET","/v1/projects",t)}const Tr=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Er=/^https?/;async function br(e){if(!e.config.emulator){var t=(await wr(e))["authorizedDomains"];for(const n of t)try{if(function(e){const t=J(),{protocol:n,hostname:r}=new URL(t);if(e.startsWith("chrome-extension://")){var i=new URL(e);return""===i.hostname&&""===r?"chrome-extension:"===n&&e.replace("chrome-extension://","")===t.replace("chrome-extension://",""):"chrome-extension:"===n&&i.hostname===r}if(!Er.test(n))return!1;if(Tr.test(e))return r===e;const s=e.replace(/\./g,"\\."),a=new RegExp("^(.+\\."+s+"|"+s+")$","i");return a.test(r)}(n))return}catch(e){}j(e,"unauthorized-domain")}}const kr=new Q(3e4,6e4);function Sr(){const t=An().___jsl;if(null!==t&&void 0!==t&&t.H)for(const e of Object.keys(t.H))if(t.H[e].r=t.H[e].r||[],t.H[e].L=t.H[e].L||[],t.H[e].r=[...t.H[e].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function Rr(i){return new Promise((e,t)=>{function n(){Sr(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{Sr(),t(H(i,"network-request-failed"))},timeout:kr.get()})}if(null!==(r=null===(r=An().gapi)||void 0===r?void 0:r.iframes)&&void 0!==r&&r.Iframe)e(gapi.iframes.getContext());else{if(null===(r=An().gapi)||void 0===r||!r.load){var r=Xe("iframefcb");return An()[r]=()=>{gapi.load?n():t(H(i,"network-request-failed"))},Ye(`${Je.gapiScript}?onload=${r}`).catch(e=>t(e))}n()}}).catch(e=>{throw Ar=null,e})}let Ar=null;const Pr=new Q(5e3,15e3),Cr="__/auth/iframe",Or="emulator/auth/iframe",Nr={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},Lr=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Dr(a){const e=(t=a,Ar=Ar||Rr(t),await Ar);var t=An().gapi;return G(t,a,"internal-error"),e.open({where:document.body,url:function(e){var t=e.config;G(t.authDomain,e,"auth-domain-config-required");var n=t.emulator?Z(t,Or):`https://${e.config.authDomain}/${Cr}`;const r={apiKey:t.apiKey,appName:e.name,v:xi.SDK_VERSION};(t=Lr.get(e.config.apiHost))&&(r.eid=t);const i=e._getFrameworks();return i.length&&(r.fw=i.join(",")),`${n}?${I(r).slice(1)}`}(a),messageHandlersFilter:t.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:Nr,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});const n=H(a,"network-request-failed"),r=An().setTimeout(()=>{t(n)},Pr.get());function i(){An().clearTimeout(r),e(s)}s.ping(i).then(i,()=>{t(n)})}))}const Ur={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class Mr{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Fr(e,t,n,r=500,i=600){var s=Math.max((window.screen.availHeight-i)/2,0).toString(),a=Math.max((window.screen.availWidth-r)/2,0).toString();let o="";const c=Object.assign(Object.assign({},Ur),{width:r.toString(),height:i.toString(),top:s,left:a});a=d().toLowerCase();n&&(o=Me(a)?"_blank":n),De(a)&&(t=t||"http://localhost",c.scrollbars="yes");var l,n=Object.entries(c).reduce((e,[t,n])=>`${e}${t}=${n},`,"");if([a=d()]=[a],He(a)&&null!==(l=window.navigator)&&void 0!==l&&l.standalone&&"_self"!==o)return function(e,t){const n=document.createElement("a");n.href=e,n.target=t;const r=document.createEvent("MouseEvent");r.initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),n.dispatchEvent(r)}(t||"",o),new Mr(null);const u=window.open(t||"",o,n);G(u,e,"popup-blocked");try{u.focus()}catch(e){}return new Mr(u)}const Vr="__/auth/handler",xr="emulator/auth/handler",jr=encodeURIComponent("fac");async function Hr(e,t,n,r,i,s){G(e.config.authDomain,e,"auth-domain-config-required"),G(e.config.apiKey,e,"invalid-api-key");const a={apiKey:e.config.apiKey,appName:e.name,authType:n,redirectUrl:r,v:xi.SDK_VERSION,eventId:i};if(t instanceof bt){t.setDefaultLanguage(e.languageCode),a.providerId=t.providerId||"",function(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1}(t.getCustomParameters())||(a.customParameters=JSON.stringify(t.getCustomParameters()));for(var[o,c]of Object.entries(s||{}))a[o]=c}if(t instanceof kt){const u=t.getScopes().filter(e=>""!==e);0<u.length&&(a.scopes=u.join(","))}e.tenantId&&(a.tid=e.tenantId);const l=a;for(const d of Object.keys(l))void 0===l[d]&&delete l[d];t=await e._getAppCheckToken(),t=t?`#${jr}=${encodeURIComponent(t)}`:"";return`${e=[e["config"]][0],e.emulator?Z(e,xr):`https://${e.authDomain}/${Vr}`}?${I(l).slice(1)}${t}`}const Wr="webStorageSupport";const qr=class{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=bn,this._completeRedirectFn=gr,this._overrideRedirectResult=ur}async _openPopup(e,t,n,r){var i;return $(null===(i=this.eventManagers[e._key()])||void 0===i?void 0:i.manager,"_initialize() not called before _openPopup()"),Fr(e,await Hr(e,t,n,J(),r),Sn())}async _openRedirect(e,t,n,r){await this._originValidation(e);r=await Hr(e,t,n,J(),r);return An().location.href=r,new Promise(()=>{})}_initialize(e){const t=e._key();if(this.eventManagers[t]){const{manager:r,promise:n}=this.eventManagers[t];return r?Promise.resolve(r):($(n,"If manager is not set, promise should be"),n)}const n=this.initAndGetManager(e);return this.eventManagers[t]={promise:n},n.catch(()=>{delete this.eventManagers[t]}),n}async initAndGetManager(t){const e=await Dr(t),n=new _r(t);return e.register("authEvent",e=>{return G(null==e?void 0:e.authEvent,t,"invalid-auth-event"),{status:n.onEvent(e.authEvent)?"ACK":"ERROR"}},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:n},this.iframes[t._key()]=e,n}_isIframeWebStorageSupported(t,n){const e=this.iframes[t._key()];e.send(Wr,{type:Wr},e=>{e=null===(e=null==e?void 0:e[0])||void 0===e?void 0:e[Wr];void 0!==e&&n(!!e),j(t,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=br(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return We()||Ue()||He()}};class Br extends class{constructor(e){this.factorId=e}_process(e,t,n){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,n);case"signin":return this._finalizeSignIn(e,t.credential);default:return K("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new Br(e)}_finalizeEnroll(e,t,n){return e=e,n={idToken:t,displayName:n,phoneVerificationInfo:this.credential._makeVerificationRequest()},ie(e,"POST","/v2/accounts/mfaEnrollment:finalize",re(e,n))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},ie(e,"POST","/v2/accounts/mfaSignIn:finalize",re(e,t))}}class zr{constructor(){}static assertion(e){return Br._fromCredential(e)}}zr.FACTOR_ID="phone";var Gr="@firebase/auth";class Kr{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),(null===(e=this.auth.currentUser)||void 0===e?void 0:e.uid)||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t((null==e?void 0:e.stsTokenManager.accessToken)||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();const t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){G(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var $r,Jr,Yr;function Xr(){return window}$r="authIdTokenMaxAge",null===(Jr=o())||void 0===Jr||Jr[`_${$r}`],Je={loadJS(r){return new Promise((e,n)=>{const t=document.createElement("script");t.setAttribute("src",r),t.onload=e,t.onerror=e=>{const t=H("internal-error");t.customData=e,n(t)},t.type="text/javascript",t.charset="UTF-8",(null!==(e=null===(e=document.getElementsByTagName("head"))||void 0===e?void 0:e[0])&&void 0!==e?e:document).appendChild(t)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},Yr="Browser",xi._registerComponent(new O("auth",(e,{options:t})=>{var n=e.getProvider("app").getImmediate(),r=e.getProvider("heartbeat"),i=e.getProvider("app-check-internal");const{apiKey:s,authDomain:a}=n.options;G(s&&!s.includes(":"),"invalid-api-key",{appName:n.name});e={apiKey:s,authDomain:a,clientPlatform:Yr,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:qe(Yr)},e=new Ge(n,r,i,e);return function(e,t){const n=(null==t?void 0:t.persistence)||[];var r=(Array.isArray(n)?n:[n]).map(Ae);null!=t&&t.errorMap&&e._updateErrorMap(t.errorMap),e._initializeWithPersistence(r,null==t?void 0:t.popupRedirectResolver)}(e,t),e},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,n)=>{const r=e.getProvider("auth-internal");r.initialize()})),xi._registerComponent(new O("auth-internal",e=>{e=Ke(e.getProvider("auth").getImmediate());return e=e,new Kr(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),xi.registerVersion(Gr,"1.8.1",function(e){switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}(Yr)),xi.registerVersion(Gr,"1.8.1","esm2017");async function Qr(e,t,n){var r=Xr()["BuildInfo"];$(t.sessionId,"AuthEvent did not contain a session ID");var i=await async function(e){const t=function(t){if($(/[0-9a-zA-Z]+/.test(t),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(t);const e=new ArrayBuffer(t.length),n=new Uint8Array(e);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);return n}(e),n=await crypto.subtle.digest("SHA-256",t),r=Array.from(new Uint8Array(n));return r.map(e=>e.toString(16).padStart(2,"0")).join("")}(t.sessionId);const s={};return He()?s.ibi=r.packageName:Ve()?s.apn=r.packageName:j(e,"operation-not-supported-in-this-environment"),r.displayName&&(s.appDisplayName=r.displayName),s.sessionId=i,Hr(e,n,t.type,void 0,null!==(t=t.eventId)&&void 0!==t?t:void 0,s)}function Zr(r){const i=Xr()["cordova"];return new Promise(n=>{i.plugins.browsertab.isAvailable(e=>{let t=null;e?i.plugins.browsertab.openUrl(r):t=i.InAppBrowser.open(r,(e=d(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),n(t)})})}const ei=20;class ti extends _r{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function ni(e,t,n=null){return{type:t,eventId:n,urlResponse:null,sessionId:function(){const t=[],n="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<ei;e++){var r=Math.floor(Math.random()*n.length);t.push(n.charAt(r))}return t.join("")}(),postBody:null,tenantId:e.tenantId,error:H(e,"no-auth-event")}}async function ri(e){var t=await si()._get(ai(e));return t&&await si()._remove(ai(e)),t}function ii(e,t){var n,r,i;const s=(n=oi(a=t),r=n.link?decodeURIComponent(n.link):void 0,i=oi(r).link,t=n.deep_link_id?decodeURIComponent(n.deep_link_id):void 0,(n=oi(t).link)||t||i||r||a);if(s.includes("/__/auth/callback")){var a=oi(s),a=a.firebaseError?function(e){try{return JSON.parse(e)}catch(e){return null}}(decodeURIComponent(a.firebaseError)):null,a=null===(a=null===(a=null==a?void 0:a.code)||void 0===a?void 0:a.split("auth/"))||void 0===a?void 0:a[1],a=a?H(a):null;return a?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:a,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:s,postBody:null}}return null}function si(){return Ae(Tn)}function ai(e){return Oe("authEvent",e.config.apiKey,e.name)}function oi(e){if(null==e||!e.includes("?"))return{};const[,...t]=e.split("?");return w(t.join("?"))}const ci=class{constructor(){this._redirectPersistence=bn,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=gr,this._overrideRedirectResult=ur}async _initialize(e){var t=e._key();let n=this.eventManagers.get(t);return n||(n=new ti(e),this.eventManagers.set(t,n),this.attachCallbackListeners(e,n)),n}_openPopup(e){j(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,n,r){var i,s;o=e,s=Xr(),G("function"==typeof(null===(i=null==s?void 0:s.universalLinks)||void 0===i?void 0:i.subscribe),o,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),G(void 0!==(null===(i=null==s?void 0:s.BuildInfo)||void 0===i?void 0:i.packageName),o,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),G("function"==typeof(null===(i=null===(i=null===(i=null==s?void 0:s.cordova)||void 0===i?void 0:i.plugins)||void 0===i?void 0:i.browsertab)||void 0===i?void 0:i.openUrl),o,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),G("function"==typeof(null===(i=null===(i=null===(i=null==s?void 0:s.cordova)||void 0===i?void 0:i.plugins)||void 0===i?void 0:i.browsertab)||void 0===i?void 0:i.isAvailable),o,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),G("function"==typeof(null===(s=null===(s=null==s?void 0:s.cordova)||void 0===s?void 0:s.InAppBrowser)||void 0===s?void 0:s.open),o,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});const a=await this._initialize(e);await a.initialized(),a.resetRedirect(),or.clear(),await this._originValidation(e);var o=ni(e,n,r);n=e,r=o,await si()._set(ai(n),r);t=await Zr(await Qr(e,o,t));return async function(a,o,c){const l=Xr()["cordova"];let u=()=>{};try{await new Promise((n,e)=>{let t=null;function r(){var e;n();const t=null===(e=l.plugins.browsertab)||void 0===e?void 0:e.close;"function"==typeof t&&t(),"function"==typeof(null==c?void 0:c.close)&&c.close()}function i(){t=t||window.setTimeout(()=>{e(H(a,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===(null===document||void 0===document?void 0:document.visibilityState)&&i()}o.addPassiveListener(r),document.addEventListener("resume",i,!1),Ve()&&document.addEventListener("visibilitychange",s,!1),u=()=>{o.removePassiveListener(r),document.removeEventListener("resume",i,!1),document.removeEventListener("visibilitychange",s,!1),t&&window.clearTimeout(t)}})}finally{u()}}(e,a,t)}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=async function(e){var t=Xr()["BuildInfo"];const n={};He()?n.iosBundleId=t.packageName:Ve()?n.androidPackageName=t.packageName:j(e,"operation-not-supported-in-this-environment"),await wr(e,n)}(e)),this.originValidationPromises[t]}attachCallbackListeners(r,i){const{universalLinks:e,handleOpenURL:t,BuildInfo:n}=Xr(),s=setTimeout(async()=>{await ri(r),i.onEvent(li())},500),a=async e=>{clearTimeout(s);var t=await ri(r);let n=null;t&&null!=e&&e.url&&(n=ii(t,e.url)),i.onEvent(n||li())};void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,a);const o=t,c=`${n.packageName.toLowerCase()}://`;Xr().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(c)&&a({url:e}),"function"==typeof o)try{o(e)}catch(e){console.error(e)}}}};function li(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:H("no-auth-event")}}var ui;function di(){var e;return(null===(e=null===self||void 0===self?void 0:self.location)||void 0===e?void 0:e.protocol)||null}function hi(e=d()){return!("file:"!==di()&&"ionic:"!==di()&&"capacitor:"!==di()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function pi(e=d()){return m()&&11===(null===document||void 0===document?void 0:document.documentMode)||([e=d()]=[e],/Edge\/\d+/.test(e))}function fi(){try{const t=self.localStorage;var e=Sn();if(t)return t.setItem(e,"1"),t.removeItem(e),!pi()||g()}catch(e){return mi()&&g()}return!1}function mi(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function gi(){return("http:"===di()||"https:"===di()||p()||hi())&&!(f()||h())&&fi()&&!mi()}function vi(){return hi()&&"undefined"!=typeof document}const _i={LOCAL:"local",NONE:"none",SESSION:"session"},yi=G,Ii="persistence";async function wi(e){await e._initializationPromise;const t=Ti();var n=Oe(Ii,e.config.apiKey,e.name);t&&t.setItem(n,e._getPersistence())}function Ti(){var e;try{return(null===(e="undefined"!=typeof window?window:null)?void 0:e.sessionStorage)||null}catch(e){return null}}const Ei=G;class bi{constructor(){this.browserResolver=Ae(qr),this.cordovaResolver=Ae(ci),this.underlyingResolver=null,this._redirectPersistence=bn,this._completeRedirectFn=gr,this._overrideRedirectResult=ur}async _initialize(e){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._initialize(e)}async _openPopup(e,t,n,r){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openPopup(e,t,n,r)}async _openRedirect(e,t,n,r){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openRedirect(e,t,n,r)}_isIframeWebStorageSupported(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)}_originValidation(e){return this.assertedUnderlyingResolver._originValidation(e)}get _shouldInitProactively(){return vi()||this.browserResolver._shouldInitProactively}get assertedUnderlyingResolver(){return Ei(this.underlyingResolver,"internal-error"),this.underlyingResolver}async selectUnderlyingResolver(){var e;this.underlyingResolver||(e=await(!!vi()&&new Promise(e=>{const t=setTimeout(()=>{e(!1)},1e3);document.addEventListener("deviceready",()=>{clearTimeout(t),e(!0)})})),this.underlyingResolver=e?this.cordovaResolver:this.browserResolver)}}function ki(e){return e.unwrap()}function Si(e,t){var n,r=null===(n=t.customData)||void 0===n?void 0:n._tokenResponse;if("auth/multi-factor-auth-required"===(null==t?void 0:t.code)){const s=t;s.resolver=new Ci(e,(n=t,e=k(i=e),G((i=n).customData.operationType,e,"argument-error"),G(null===(n=i.customData._serverResponse)||void 0===n?void 0:n.mfaPendingCredential,e,"argument-error"),mn._fromError(e,i)))}else if(r){var i=Ri(t);const a=t;i&&(a.credential=i,a.tenantId=r.tenantId||void 0,a.email=r.email||void 0,a.phoneNumber=r.phoneNumber||void 0)}}function Ri(e){var t=(e instanceof v?e.customData:e)["_tokenResponse"];if(!t)return null;if(!(e instanceof v)&&"temporaryProof"in t&&"phoneNumber"in t)return Xn.credentialFromResult(e);const n=t.providerId;if(!n||n===N.PASSWORD)return null;let r;switch(n){case N.GOOGLE:r=At;break;case N.FACEBOOK:r=Rt;break;case N.GITHUB:r=Pt;break;case N.TWITTER:r=Nt;break;default:var{oauthIdToken:i,oauthAccessToken:s,oauthTokenSecret:a,pendingToken:o,nonce:c}=t;return s||a||i||o?o?n.startsWith("saml.")?Ct._create(n,o):_t._fromParams({providerId:n,signInMethod:n,pendingToken:o,idToken:i,accessToken:s}):new St(n).credential({idToken:i,accessToken:s,rawNonce:c}):null}return e instanceof v?r.credentialFromError(e):r.credentialFromResult(e)}function Ai(t,e){return e.catch(e=>{throw e instanceof v&&Si(t,e),e}).then(e=>{var t=e.operationType,n=e.user;return{operationType:t,credential:Ri(e),additionalUserInfo:pn(e),user:Oi.getOrCreate(n)}})}async function Pi(t,e){const n=await e;return{verificationId:n.verificationId,confirm:e=>Ai(t,n.confirm(e))}}class Ci{constructor(e,t){this.resolver=t,this.auth=e.wrapped()}get session(){return this.resolver.session}get hints(){return this.resolver.hints}resolveSignIn(e){return Ai(ki(this.auth),this.resolver.resolveSignIn(e))}}class Oi{constructor(e){this._delegate=e,this.multiFactor=(e=k(e=e),_n.has(e)||_n.set(e,vn._fromUser(e)),_n.get(e))}static getOrCreate(e){return Oi.USER_MAP.has(e)||Oi.USER_MAP.set(e,new Oi(e)),Oi.USER_MAP.get(e)}delete(){return this._delegate.delete()}reload(){return this._delegate.reload()}toJSON(){return this._delegate.toJSON()}getIdTokenResult(e){return this._delegate.getIdTokenResult(e)}getIdToken(e){return this._delegate.getIdToken(e)}linkAndRetrieveDataWithCredential(e){return this.linkWithCredential(e)}async linkWithCredential(e){return Ai(this.auth,zt(this._delegate,e))}async linkWithPhoneNumber(e,t){return Pi(this.auth,async function(e,t,n){const r=k(e);return await Ht(!1,r,"phone"),n=await Jn(r.auth,t,k(n)),new $n(n,e=>zt(r,e))}(this._delegate,e,t))}async linkWithPopup(e){return Ai(this.auth,async function(e,t,n){B((e=k(e)).auth,t,bt),n=Qn(e.auth,n);const r=new sr(e.auth,"linkViaPopup",t,n,e);return r.executeNotNull()}(this._delegate,e,bi))}async linkWithRedirect(e){return await wi(Ke(this.auth)),mr(this._delegate,e,bi)}reauthenticateAndRetrieveDataWithCredential(e){return this.reauthenticateWithCredential(e)}async reauthenticateWithCredential(e){return Ai(this.auth,Gt(this._delegate,e))}reauthenticateWithPhoneNumber(e,t){return Pi(this.auth,async function(e,t,n){const r=k(e);return xi._isFirebaseServerApp(r.auth.app)?Promise.reject(q(r.auth)):(n=await Jn(r.auth,t,k(n)),new $n(n,e=>Gt(r,e)))}(this._delegate,e,t))}reauthenticateWithPopup(e){return Ai(this.auth,async function(e,t,n){if(e=k(e),xi._isFirebaseServerApp(e.auth.app))return Promise.reject(H(e.auth,"operation-not-supported-in-this-environment"));B(e.auth,t,bt),n=Qn(e.auth,n);const r=new sr(e.auth,"reauthViaPopup",t,n,e);return r.executeNotNull()}(this._delegate,e,bi))}async reauthenticateWithRedirect(e){return await wi(Ke(this.auth)),fr(this._delegate,e,bi)}sendEmailVerification(e){return nn(this._delegate,e)}async unlink(e){return await xt(this._delegate,e),this}updateEmail(e){return t=this._delegate,e=e,t=k(t),xi._isFirebaseServerApp(t.auth.app)?Promise.reject(q(t.auth)):an(t,e,null);var t}updatePassword(e){return an(k(this._delegate),null,e)}updatePhoneNumber(e){return async function(e,t){if(e=k(e),xi._isFirebaseServerApp(e.auth.app))return Promise.reject(q(e.auth));await jt(e,t)}(this._delegate,e)}updateProfile(e){return sn(this._delegate,e)}verifyBeforeUpdateEmail(e,t){return rn(this._delegate,e,t)}get emailVerified(){return this._delegate.emailVerified}get isAnonymous(){return this._delegate.isAnonymous}get metadata(){return this._delegate.metadata}get phoneNumber(){return this._delegate.phoneNumber}get providerData(){return this._delegate.providerData}get refreshToken(){return this._delegate.refreshToken}get tenantId(){return this._delegate.tenantId}get displayName(){return this._delegate.displayName}get email(){return this._delegate.email}get photoURL(){return this._delegate.photoURL}get providerId(){return this._delegate.providerId}get uid(){return this._delegate.uid}get auth(){return this._delegate.auth}}Oi.USER_MAP=new WeakMap;const Ni=G;class Li{constructor(e,t){if(this.app=e,t.isInitialized())return this._delegate=t.getImmediate(),void this.linkUnderlyingAuth();var n=e.options["apiKey"];Ni(n,"invalid-api-key",{appName:e.name}),Ni(n,"invalid-api-key",{appName:e.name});var r="undefined"!=typeof window?bi:void 0;this._delegate=t.initialize({options:{persistence:function(e,t){const n=function(e,t){const n=Ti();if(!n)return[];switch(t=Oe(Ii,e,t),n.getItem(t)){case _i.NONE:return[Ce];case _i.LOCAL:return[xn,bn];case _i.SESSION:return[bn];default:return[]}}(e,t);"undefined"==typeof self||n.includes(xn)||n.push(xn);if("undefined"!=typeof window)for(const r of[Tn,bn])n.includes(r)||n.push(r);n.includes(Ce)||n.push(Ce);return n}(n,e.name),popupRedirectResolver:r}}),this._delegate._updateErrorMap(U),this.linkUnderlyingAuth()}get emulatorConfig(){return this._delegate.emulatorConfig}get currentUser(){return this._delegate.currentUser?Oi.getOrCreate(this._delegate.currentUser):null}get languageCode(){return this._delegate.languageCode}set languageCode(e){this._delegate.languageCode=e}get settings(){return this._delegate.settings}get tenantId(){return this._delegate.tenantId}set tenantId(e){this._delegate.tenantId=e}useDeviceLanguage(){this._delegate.useDeviceLanguage()}signOut(){return this._delegate.signOut()}useEmulator(e,t){at(this._delegate,e,t)}applyActionCode(e){return Zt(this._delegate,e)}checkActionCode(e){return en(this._delegate,e)}confirmPasswordReset(e,t){return async function(t,e,n){await ut(k(t),{oobCode:e,newPassword:n}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Qt(t),e})}(this._delegate,e,t)}async createUserWithEmailAndPassword(e,t){return Ai(this._delegate,async function(t,e,n){if(xi._isFirebaseServerApp(t.app))return Promise.reject(q(t));const r=Ke(t),i=st(r,{returnSecureToken:!0,email:e,password:n,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Lt,"EMAIL_PASSWORD_PROVIDER");return n=await i.catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Qt(t),e}),n=await Dt._fromIdTokenResponse(r,"signIn",n),await r._updateCurrentUser(n.user),n}(this._delegate,e,t))}fetchProvidersForEmail(e){return this.fetchSignInMethodsForEmail(e)}fetchSignInMethodsForEmail(e){return tn(this._delegate,e)}isSignInWithEmailLink(e){return this._delegate,e=e,"EMAIL_SIGNIN"===(null==(e=Tt.parseLink(e))?void 0:e.operation)}async getRedirectResult(){Ni(gi(),this._delegate,"operation-not-supported-in-this-environment");var e,t,t=(e=this._delegate,t=bi,await Ke(e)._initializationPromise,await gr(e,t,!1));return t?Ai(this._delegate,Promise.resolve(t)):{credential:null,user:null}}addFrameworkForLogging(e){Ke(this._delegate)._logFramework(e)}onAuthStateChanged(e,t,n){var{next:e,error:t,complete:n}=Di(e,t,n);return this._delegate.onAuthStateChanged(e,t,n)}onIdTokenChanged(e,t,n){var{next:e,error:t,complete:n}=Di(e,t,n);return this._delegate.onIdTokenChanged(e,t,n)}sendSignInLinkToEmail(e,t){return async function(e,t,n){const r=Ke(e);e=t={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"},G((n=n).handleCodeInApp,r,"argument-error"),n&&Xt(r,e,n),await st(r,t,"getOobCode",mt,"EMAIL_PASSWORD_PROVIDER")}(this._delegate,e,t)}sendPasswordResetEmail(e,t){return async function(e,t,n){e=Ke(e),t={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"},n&&Xt(e,t,n),await st(e,t,"getOobCode",ft,"EMAIL_PASSWORD_PROVIDER")}(this._delegate,e,t||void 0)}async setPersistence(e){var t,n;t=this._delegate,n=e,yi(Object.values(_i).includes(n),t,"invalid-persistence-type"),f()?yi(n!==_i.SESSION,t,"unsupported-persistence-type"):h()?yi(n===_i.NONE,t,"unsupported-persistence-type"):mi()?yi(n===_i.NONE||n===_i.LOCAL&&g(),t,"unsupported-persistence-type"):yi(n===_i.NONE||fi(),t,"unsupported-persistence-type");let r;switch(e){case _i.SESSION:r=bn;break;case _i.LOCAL:var i=await Ae(xn)._isAvailable();r=i?xn:Tn;break;case _i.NONE:r=Ce;break;default:return j("argument-error",{appName:this._delegate.name})}return this._delegate.setPersistence(r)}signInAndRetrieveDataWithCredential(e){return this.signInWithCredential(e)}signInAnonymously(){return Ai(this._delegate,async function(e){if(xi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const t=Ke(e);return await t._initializationPromise,null!==(e=t.currentUser)&&void 0!==e&&e.isAnonymous?new Dt({user:t.currentUser,providerId:null,operationType:"signIn"}):(e=await Lt(t,{returnSecureToken:!0}),e=await Dt._fromIdTokenResponse(t,"signIn",e,!0),await t._updateCurrentUser(e.user),e)}(this._delegate))}signInWithCredential(e){return Ai(this._delegate,Bt(this._delegate,e))}signInWithCustomToken(e){return Ai(this._delegate,Kt(this._delegate,e))}signInWithEmailAndPassword(e,t){return Ai(this._delegate,(n=this._delegate,e=e,t=t,xi._isFirebaseServerApp(n.app)?Promise.reject(q(n)):Bt(k(n),Et.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Qt(n),e})));var n}signInWithEmailLink(e,t){return Ai(this._delegate,async function(e,t,n){return xi._isFirebaseServerApp(e.app)?Promise.reject(q(e)):(e=k(e),G((n=Et.credentialWithLink(t,n||J()))._tenantId===(e.tenantId||null),e,"tenant-id-mismatch"),Bt(e,n))}(this._delegate,e,t))}signInWithPhoneNumber(e,t){return Pi(this._delegate,async function(e,t,n){if(xi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const r=Ke(e);return n=await Jn(r,t,k(n)),new $n(n,e=>Bt(r,e))}(this._delegate,e,t))}async signInWithPopup(e){return Ni(gi(),this._delegate,"operation-not-supported-in-this-environment"),Ai(this._delegate,async function(e,t,n){if(xi._isFirebaseServerApp(e.app))return Promise.reject(H(e,"operation-not-supported-in-this-environment"));var r=Ke(e);B(e,t,bt),n=Qn(r,n);const i=new sr(r,"signInViaPopup",t,n);return i.executeNotNull()}(this._delegate,e,bi))}async signInWithRedirect(e){return Ni(gi(),this._delegate,"operation-not-supported-in-this-environment"),await wi(this._delegate),pr(this._delegate,e,bi)}updateCurrentUser(e){return this._delegate.updateCurrentUser(e)}verifyPasswordResetCode(e){return async function(e,t){return(t=(await en(k(e),t))["data"]).email}(this._delegate,e)}unwrap(){return this._delegate}_delete(){return this._delegate._delete()}linkUnderlyingAuth(){this._delegate.wrapped=()=>this}}function Di(e,t,n){let r=e;"function"!=typeof e&&({next:r,error:t,complete:n}=e);const i=r;return{next:e=>i(e&&Oi.getOrCreate(e)),error:t,complete:n}}Li.Persistence=_i;class Ui{static credential(e,t){return Xn.credential(e,t)}constructor(){this.providerId="phone",this._delegate=new Xn(ki(i.default.auth()))}verifyPhoneNumber(e,t){return this._delegate.verifyPhoneNumber(e,t)}unwrap(){return this._delegate}}Ui.PHONE_SIGN_IN_METHOD=Xn.PHONE_SIGN_IN_METHOD,Ui.PROVIDER_ID=Xn.PROVIDER_ID;const Mi=G;class Fi{constructor(e,t,n=i.default.app()){var r;Mi(null===(r=n.options)||void 0===r?void 0:r.apiKey,"invalid-api-key",{appName:n.name}),this._delegate=new Kn(n.auth(),e,t),this.type=this._delegate.type}clear(){this._delegate.clear()}render(){return this._delegate.render()}verify(){return this._delegate.verify()}}(ui=i.default).INTERNAL.registerComponent(new O("auth-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),e=e.getProvider("auth");return new Li(t,e)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:L.EMAIL_SIGNIN,PASSWORD_RESET:L.PASSWORD_RESET,RECOVER_EMAIL:L.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:L.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:L.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:L.VERIFY_EMAIL}},EmailAuthProvider:Et,FacebookAuthProvider:Rt,GithubAuthProvider:Pt,GoogleAuthProvider:At,OAuthProvider:St,SAMLAuthProvider:Ot,PhoneAuthProvider:Ui,PhoneMultiFactorGenerator:zr,RecaptchaVerifier:Fi,TwitterAuthProvider:Nt,Auth:Li,AuthCredential:lt,Error:v}).setInstantiationMode("LAZY").setMultipleInstances(!1)),ui.registerVersion("@firebase/auth-compat","0.5.16")}.apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth.js - be sure to load firebase-app.js first.")}});//# sourceMappingURL=firebase-auth.js.map
