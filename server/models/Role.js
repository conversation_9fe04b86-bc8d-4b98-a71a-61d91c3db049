const mongoose = require('mongoose');

const RoleSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true, 
    unique: true,
    trim: true
  },
  displayName: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  permissions: {
    inventory: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    transfers: {
      initiate: { type: Boolean, default: false },
      receive: { type: Boolean, default: false },
      view: { type: Boolean, default: false }
    },
    storeRegistration: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    products: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    users: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    billing: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    machinery: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    reports: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    },
    roles: {
      view: { type: Boolean, default: false },
      manage: { type: <PERSON>ole<PERSON>, default: false }
    },
    activityLog: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false }
    }
  },
  isSystem: {
    type: Boolean,
    default: false
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Role', RoleSchema); 