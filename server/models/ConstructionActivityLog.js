const mongoose = require('mongoose');

// Dynamic Item Schema for Materials, Labour, Machinery
const DynamicItemSchema = new mongoose.Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  unit: { type: String, required: true }, // ton, CubicMeter, Kg, day, hour, etc.
  quantity: { type: Number, required: true, default: 0 },
  rate: { type: Number, required: true, default: 0 },
  amount: { type: Number, required: true, default: 0 }, // quantity * rate
  category: { type: String, required: true }, // 'material', 'labour', 'machinery'
  isActive: { type: Boolean, default: true },
  order: { type: Number, default: 0 } // For drag & drop ordering
}, { _id: false });

// Concrete Grade Configuration Schema
const ConcreteGradeSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true }, // P.C.C M10 Grade, R.C.C M25 Grade
  unit: { type: String, required: true, default: 'Cum' }, // Cum, Sqm, etc.
  description: { type: String },
  
  // Dynamic Categories
  materials: [DynamicItemSchema], // Cement, Sand, Aggregates, etc.
  labour: [DynamicItemSchema],    // Mason, Mazdoor, etc.
  machinery: [DynamicItemSchema], // Mixers, Generators, etc.
  
  // Calculations
  totalMaterialCost: { type: Number, default: 0 },
  totalLabourCost: { type: Number, default: 0 },
  totalMachineryCost: { type: Number, default: 0 },
  overheadPercentage: { type: Number, default: 10.0 }, // 10% overhead
  overheadAmount: { type: Number, default: 0 },
  ratePerUnit: { type: Number, default: 0 }, // Final rate per Cum/unit
  totalCost: { type: Number, default: 0 },
  
  // Metadata
  createdBy: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true }
});

// Rate Card Schema for Dynamic Pricing
const RateCardSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  effectiveDate: { type: Date, required: true },
  
  // Dynamic Rate Items
  materials: [{
    name: { type: String, required: true },
    unit: { type: String, required: true },
    rate: { type: Number, required: true },
    category: { type: String, default: 'material' }
  }],
  labour: [{
    name: { type: String, required: true },
    unit: { type: String, required: true },
    rate: { type: Number, required: true },
    category: { type: String, default: 'labour' }
  }],
  machinery: [{
    name: { type: String, required: true },
    unit: { type: String, required: true },
    rate: { type: Number, required: true },
    category: { type: String, default: 'machinery' }
  }],
  
  // Metadata
  createdBy: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true }
});

// Daily Activity Log Schema
const ConstructionActivityLogSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  date: { type: Date, required: true },
  gradeId: { type: String, required: true }, // Reference to ConcreteGrade
  gradeName: { type: String, required: true },
  
  // Work Details
  workDescription: { type: String, required: true },
  quantityCompleted: { type: Number, required: true }, // Cum completed
  unit: { type: String, required: true, default: 'Cum' },
  
  // Cost Breakdown (calculated from grade configuration)
  materials: [DynamicItemSchema],
  labour: [DynamicItemSchema],
  machinery: [DynamicItemSchema],

  // Material Breakdown with Variance (actual vs expected)
  materialBreakdown: [{
    id: { type: String, required: true },
    name: { type: String, required: true },
    unit: { type: String, required: true },
    expectedQuantity: { type: Number, required: true },
    actualQuantity: { type: Number, required: true },
    rate: { type: Number, required: true },
    amount: { type: Number, required: true }, // actualQuantity * rate
    variance: { type: Number, required: true }, // actualQuantity - expectedQuantity
    varianceAmount: { type: Number, required: true }, // variance * rate
    category: { type: String, required: true, enum: ['material', 'labour', 'machinery'] }
  }],

  // Variance Summary
  varianceSummary: {
    expectedTotal: { type: Number, default: 0 },
    actualTotal: { type: Number, default: 0 },
    totalVariance: { type: Number, default: 0 },
    totalVariancePercentage: { type: Number, default: 0 }
  },

  // Totals
  totalMaterialCost: { type: Number, default: 0 },
  totalLabourCost: { type: Number, default: 0 },
  totalMachineryCost: { type: Number, default: 0 },
  overheadAmount: { type: Number, default: 0 },
  totalCost: { type: Number, default: 0 },
  
  // Project Details
  projectId: { type: String },
  projectName: { type: String },
  location: { type: String },
  contractor: { type: String },
  
  // Metadata
  createdBy: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  status: { type: String, enum: ['draft', 'completed', 'verified'], default: 'draft' }
});

// Pre-save middleware to calculate totals
ConcreteGradeSchema.pre('save', function(next) {
  // Calculate material costs
  this.totalMaterialCost = this.materials.reduce((sum, item) => sum + (item.amount || 0), 0);
  
  // Calculate labour costs
  this.totalLabourCost = this.labour.reduce((sum, item) => sum + (item.amount || 0), 0);
  
  // Calculate machinery costs
  this.totalMachineryCost = this.machinery.reduce((sum, item) => sum + (item.amount || 0), 0);
  
  // Calculate overhead
  const subtotal = this.totalMaterialCost + this.totalLabourCost + this.totalMachineryCost;
  this.overheadAmount = (subtotal * this.overheadPercentage) / 100;
  
  // Calculate total cost and rate per unit
  this.totalCost = subtotal + this.overheadAmount;
  this.ratePerUnit = this.totalCost;
  
  this.updatedAt = new Date();
  next();
});

// Pre-save middleware for Activity Log
ConstructionActivityLogSchema.pre('save', function(next) {
  // Calculate totals
  this.totalMaterialCost = this.materials.reduce((sum, item) => sum + (item.amount || 0), 0);
  this.totalLabourCost = this.labour.reduce((sum, item) => sum + (item.amount || 0), 0);
  this.totalMachineryCost = this.machinery.reduce((sum, item) => sum + (item.amount || 0), 0);
  
  // Calculate total cost
  this.totalCost = this.totalMaterialCost + this.totalLabourCost + this.totalMachineryCost + this.overheadAmount;
  
  this.updatedAt = new Date();
  next();
});

// Create Models
const ConcreteGrade = mongoose.model('ConcreteGrade', ConcreteGradeSchema);
const RateCard = mongoose.model('RateCard', RateCardSchema);
const ConstructionActivityLog = mongoose.model('ConstructionActivityLog', ConstructionActivityLogSchema);

module.exports = {
  ConcreteGrade,
  RateCard,
  ConstructionActivityLog
};
