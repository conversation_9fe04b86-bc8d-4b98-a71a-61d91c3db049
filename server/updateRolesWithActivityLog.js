const mongoose = require('mongoose');
const Role = require('./models/Role');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/ims', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Update roles with activityLog permission
const updateRolesWithActivityLog = async () => {
  try {
    console.log('🔄 Updating roles with Activity Log permissions...');

    // Define activityLog permissions for each role
    const roleUpdates = {
      superadmin: { view: true, manage: true },
      admin: { view: true, manage: true },
      siteengineer: { view: true, manage: true },
      storekeeper: { view: true, manage: true },
      user: { view: true, manage: false }
    };

    // Update each role
    for (const [roleName, activityLogPermission] of Object.entries(roleUpdates)) {
      const result = await Role.updateOne(
        { name: roleName },
        { 
          $set: { 
            'permissions.activityLog': activityLogPermission,
            updatedAt: new Date()
          }
        }
      );

      if (result.matchedCount > 0) {
        console.log(`✅ Updated ${roleName} role with activityLog permission:`, activityLogPermission);
      } else {
        console.log(`⚠️  Role ${roleName} not found in database`);
      }
    }

    // Verify the updates
    console.log('\n🔍 Verifying updates...');
    const updatedRoles = await Role.find({}, 'name permissions.activityLog');
    
    updatedRoles.forEach(role => {
      console.log(`📋 ${role.name}: activityLog =`, role.permissions.activityLog || 'NOT SET');
    });

    console.log('\n🎉 Role updates completed successfully!');
    console.log('🚀 Activity Log menu should now be visible for all users.');

  } catch (error) {
    console.error('❌ Error updating roles:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run the update
const runUpdate = async () => {
  await connectDB();
  await updateRolesWithActivityLog();
};

if (require.main === module) {
  runUpdate();
}

module.exports = { updateRolesWithActivityLog };
