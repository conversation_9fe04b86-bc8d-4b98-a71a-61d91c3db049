const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const { ConcreteGrade, RateCard, ConstructionActivityLog } = require('./models/ConstructionActivityLog');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/ims', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample data
const sampleRateCard = {
  id: uuidv4(),
  effectiveDate: new Date(),
  materials: [
    { name: 'Cement', unit: 'ton', rate: 3780.00, category: 'material' },
    { name: 'Coarse sand', unit: 'CubicMeter', rate: 1765.00, category: 'material' },
    { name: '20 mm aggregate', unit: 'CubicMeter', rate: 882.75, category: 'material' },
    { name: '10 mm aggregate', unit: 'CubicMeter', rate: 882.75, category: 'material' },
    { name: 'Admixture', unit: 'Kg', rate: 140.00, category: 'material' }
  ],
  labour: [
    { name: 'Mason (1st Class)', unit: 'day', rate: 400.00, category: 'labour' },
    { name: 'Mazdoor (Unskilled)', unit: 'day', rate: 350.00, category: 'labour' }
  ],
  machinery: [
    { name: 'Concrete mixer 0.4/0.28 cum capacity', unit: 'hour', rate: 500.00, category: 'machinery' },
    { name: 'Generator 33 KVA', unit: 'hour', rate: 800.00, category: 'machinery' },
    { name: 'Vibrator', unit: 'hour', rate: 0.00, category: 'machinery' },
    { name: 'Concrete Pump', unit: 'hour', rate: 605.00, category: 'machinery' }
  ],
  createdBy: 'system',
  isActive: true
};

const sampleGrades = [
  {
    id: uuidv4(),
    name: 'P.C.C M10 Grade',
    unit: 'Cum',
    description: 'Plain Cement Concrete M10 Grade for foundation work',
    materials: [
      { id: uuidv4(), name: 'Cement', unit: 'ton', quantity: 0.281, rate: 3780.00, amount: 1062.18, category: 'material', isActive: true, order: 0 },
      { id: uuidv4(), name: 'Coarse sand', unit: 'CubicMeter', quantity: 0.458, rate: 1765.00, category: 'material', isActive: true, order: 1 },
      { id: uuidv4(), name: '20 mm aggregate', unit: 'CubicMeter', quantity: 0.458, rate: 882.75, category: 'material', isActive: true, order: 2 },
      { id: uuidv4(), name: '10 mm aggregate', unit: 'CubicMeter', quantity: 0.458, rate: 882.75, category: 'material', isActive: true, order: 3 }
    ],
    labour: [
      { id: uuidv4(), name: 'Mason (1st Class)', unit: 'day', quantity: 0.25, rate: 400.00, category: 'labour', isActive: true, order: 0 },
      { id: uuidv4(), name: 'Mazdoor (Unskilled)', unit: 'day', quantity: 0.75, rate: 350.00, category: 'labour', isActive: true, order: 1 }
    ],
    machinery: [
      { id: uuidv4(), name: 'Concrete mixer 0.4/0.28 cum capacity', unit: 'hour', quantity: 1.00, rate: 500.00, category: 'machinery', isActive: true, order: 0 },
      { id: uuidv4(), name: 'Generator 33 KVA', unit: 'hour', quantity: 0.5, rate: 800.00, category: 'machinery', isActive: true, order: 1 }
    ],
    overheadPercentage: 10.0,
    createdBy: 'system',
    isActive: true
  },
  {
    id: uuidv4(),
    name: 'R.C.C M25 Grade',
    unit: 'Cum',
    description: 'Reinforced Cement Concrete M25 Grade for structural work',
    materials: [
      { id: uuidv4(), name: 'Cement', unit: 'ton', quantity: 0.394, rate: 3780.00, category: 'material', isActive: true, order: 0 },
      { id: uuidv4(), name: 'Coarse sand', unit: 'CubicMeter', quantity: 0.449, rate: 1765.00, category: 'material', isActive: true, order: 1 },
      { id: uuidv4(), name: '20 mm aggregate', unit: 'CubicMeter', quantity: 0.898, rate: 882.75, category: 'material', isActive: true, order: 2 },
      { id: uuidv4(), name: 'Admixture', unit: 'Kg', quantity: 1.97, rate: 140.00, category: 'material', isActive: true, order: 3 }
    ],
    labour: [
      { id: uuidv4(), name: 'Mason (1st Class)', unit: 'day', quantity: 0.35, rate: 400.00, category: 'labour', isActive: true, order: 0 },
      { id: uuidv4(), name: 'Mazdoor (Unskilled)', unit: 'day', quantity: 1.05, rate: 350.00, category: 'labour', isActive: true, order: 1 }
    ],
    machinery: [
      { id: uuidv4(), name: 'Concrete mixer 0.4/0.28 cum capacity', unit: 'hour', quantity: 1.25, rate: 500.00, category: 'machinery', isActive: true, order: 0 },
      { id: uuidv4(), name: 'Generator 33 KVA', unit: 'hour', quantity: 0.75, rate: 800.00, category: 'machinery', isActive: true, order: 1 },
      { id: uuidv4(), name: 'Concrete Pump', unit: 'hour', quantity: 0.5, rate: 605.00, category: 'machinery', isActive: true, order: 2 }
    ],
    overheadPercentage: 12.0,
    createdBy: 'system',
    isActive: true
  }
];

// Calculate amounts for items
const calculateItemAmounts = (items) => {
  return items.map(item => ({
    ...item,
    amount: (item.quantity || 0) * (item.rate || 0)
  }));
};

// Process grades with calculated amounts
const processedGrades = sampleGrades.map(grade => ({
  ...grade,
  materials: calculateItemAmounts(grade.materials),
  labour: calculateItemAmounts(grade.labour),
  machinery: calculateItemAmounts(grade.machinery)
}));

const sampleActivityLogs = [
  {
    id: uuidv4(),
    date: new Date('2024-01-15'),
    gradeId: processedGrades[0].id,
    gradeName: processedGrades[0].name,
    workDescription: 'Foundation concrete work for Building A',
    quantityCompleted: 15.5,
    unit: 'Cum',
    projectId: 'PROJ-001',
    projectName: 'Residential Complex Phase 1',
    location: 'Site A - Block 1',
    contractor: 'ABC Construction Ltd.',
    createdBy: 'site-engineer-1',
    status: 'completed'
  },
  {
    id: uuidv4(),
    date: new Date('2024-01-16'),
    gradeId: processedGrades[1].id,
    gradeName: processedGrades[1].name,
    workDescription: 'Column concrete work for Ground Floor',
    quantityCompleted: 8.2,
    unit: 'Cum',
    projectId: 'PROJ-001',
    projectName: 'Residential Complex Phase 1',
    location: 'Site A - Block 1',
    contractor: 'ABC Construction Ltd.',
    createdBy: 'site-engineer-1',
    status: 'completed'
  },
  {
    id: uuidv4(),
    date: new Date('2024-01-17'),
    gradeId: processedGrades[0].id,
    gradeName: processedGrades[0].name,
    workDescription: 'Footing concrete work for Building B',
    quantityCompleted: 12.3,
    unit: 'Cum',
    projectId: 'PROJ-002',
    projectName: 'Commercial Complex',
    location: 'Site B - Main Building',
    contractor: 'XYZ Builders Pvt. Ltd.',
    createdBy: 'site-engineer-2',
    status: 'verified'
  }
];

// Seed function
const seedActivityLog = async () => {
  try {
    console.log('🌱 Starting Activity Log seeding...');

    // Clear existing data
    await ConcreteGrade.deleteMany({});
    await RateCard.deleteMany({});
    await ConstructionActivityLog.deleteMany({});
    console.log('✅ Cleared existing data');

    // Insert rate card
    const rateCard = new RateCard(sampleRateCard);
    await rateCard.save();
    console.log('✅ Rate card created');

    // Insert grades
    const grades = await ConcreteGrade.insertMany(processedGrades);
    console.log(`✅ ${grades.length} concrete grades created`);

    // Process activity logs with calculated costs
    const processedActivityLogs = sampleActivityLogs.map(log => {
      const grade = processedGrades.find(g => g.id === log.gradeId);
      if (!grade) return log;

      const quantity = log.quantityCompleted;
      
      // Calculate actual consumption
      const materials = grade.materials.map(item => ({
        ...item,
        quantity: item.quantity * quantity,
        amount: (item.quantity * quantity) * item.rate
      }));

      const labour = grade.labour.map(item => ({
        ...item,
        quantity: item.quantity * quantity,
        amount: (item.quantity * quantity) * item.rate
      }));

      const machinery = grade.machinery.map(item => ({
        ...item,
        quantity: item.quantity * quantity,
        amount: (item.quantity * quantity) * item.rate
      }));

      const totalMaterialCost = materials.reduce((sum, item) => sum + item.amount, 0);
      const totalLabourCost = labour.reduce((sum, item) => sum + item.amount, 0);
      const totalMachineryCost = machinery.reduce((sum, item) => sum + item.amount, 0);
      const subtotal = totalMaterialCost + totalLabourCost + totalMachineryCost;
      const overheadAmount = (subtotal * grade.overheadPercentage) / 100;

      return {
        ...log,
        materials,
        labour,
        machinery,
        totalMaterialCost,
        totalLabourCost,
        totalMachineryCost,
        overheadAmount,
        totalCost: subtotal + overheadAmount
      };
    });

    // Insert activity logs
    const activityLogs = await ConstructionActivityLog.insertMany(processedActivityLogs);
    console.log(`✅ ${activityLogs.length} activity logs created`);

    console.log('🎉 Activity Log seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • Rate Cards: 1`);
    console.log(`   • Concrete Grades: ${grades.length}`);
    console.log(`   • Activity Logs: ${activityLogs.length}`);
    console.log('\n🚀 You can now access the Activity Log module in the application!');

  } catch (error) {
    console.error('❌ Error seeding Activity Log:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run seeding
const runSeed = async () => {
  await connectDB();
  await seedActivityLog();
};

if (require.main === module) {
  runSeed();
}

module.exports = { seedActivityLog };
