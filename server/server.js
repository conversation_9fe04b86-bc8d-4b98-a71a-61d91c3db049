const express = require('express');
const cors = require('cors');
const connectDB = require('./utils/db');
const { Server } = require('socket.io');

const authorization = require('./routes/authRoute');
const productRoutes = require('./routes/productRoutes');
const inventoryRoutes = require('./routes/inventoryRoutes');
const userRoutes = require('./routes/userRoutes');
const billRoutes = require('./routes/billRoutes')
const requestRoutes = require('./routes/requestRoutes')
const storeRoutes = require('./routes/storeRoutes');
const transferRoutes = require('./routes/transferRoutes');
const transferRoutesNew = require('./routes/transferRoutesNew');
const machineryWorkRoutes = require('./routes/machineryWorkRoutes');
const machineRoutes = require('./routes/machineRoutes');
const roleRoutes = require('./routes/roleRoutes');
const activityLogRoutes = require('./routes/activityLog');

const app = express();
const PORT = process.env.PORT || 5017;

// Create an HTTP server and attach the Express app
const server = require('http').createServer(app);

// Create a Socket.IO instance and attach it to the HTTP server
const io = new Server(server);

app.use(cors());
app.use(express.json());

connectDB();

// Routes
app.use('/api/auth', authorization);
app.use('/api/product/', productRoutes);
app.use('/api/inventory/', inventoryRoutes);
app.use('/api/user/', userRoutes);
app.use('/api/bill', billRoutes)
app.use('/api/store', storeRoutes);
app.use('/api', requestRoutes)
app.use('/api/transfers', transferRoutes);
app.use('/api/transfers-new', transferRoutesNew);
app.use('/api/machinery-work', machineryWorkRoutes);
app.use('/api/machinery', machineRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/activity-log', activityLogRoutes);

// Initialize default roles when server starts
const initializeRoles = async () => {
  try {
    const response = await fetch(`http://localhost:${PORT}/api/roles/initialize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    console.log('Role initialization:', data.message);
  } catch (error) {
    console.error('Error initializing roles:', error);
  }
};

// Handle Socket.IO connections
io.on('connection', (socket) => {
  console.log('New client connected:', socket.id);

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Start the server
server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  // Initialize roles after server starts
  initializeRoles();
});
