const mongoose = require('mongoose');
const Role = require('./models/Role');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/ims');
    console.log('✅ MongoDB connected for role update');
  } catch (err) {
    console.error('❌ MongoDB connection error:', err);
    process.exit(1);
  }
};

// Update roles with DPR permission
const updateRolesWithDPR = async () => {
  try {
    console.log('🔄 Updating roles with Daily Progress Report (DPR) permissions...');

    // Define DPR permissions for each role
    const roleUpdates = {
      superadmin: { view: true, manage: true },
      admin: { view: true, manage: true },
      siteengineer: { view: true, manage: true },
      storekeeper: { view: true, manage: true },
      user: { view: false, manage: false } // Users cannot access DPR
    };

    // Update each role
    for (const [roleName, dprPermission] of Object.entries(roleUpdates)) {
      const result = await Role.updateOne(
        { name: roleName },
        { 
          $set: { 
            'permissions.dpr': dprPermission,
            updatedAt: new Date()
          }
        }
      );

      if (result.matchedCount > 0) {
        console.log(`✅ Updated ${roleName} role with DPR permission:`, dprPermission);
      } else {
        console.log(`⚠️  Role ${roleName} not found in database`);
      }
    }

    // Verify the updates
    console.log('\n🔍 Verifying DPR permission updates...');
    const updatedRoles = await Role.find({}, 'name permissions.dpr');
    
    updatedRoles.forEach(role => {
      console.log(`📋 ${role.name}: DPR =`, role.permissions.dpr || 'NOT SET');
    });

    console.log('\n✅ DPR permissions update completed successfully!');
    console.log('\n📝 Summary:');
    console.log('   - superadmin: Full DPR access (view + manage)');
    console.log('   - admin: Full DPR access (view + manage)');
    console.log('   - siteengineer: Full DPR access (view + manage)');
    console.log('   - storekeeper: Full DPR access (view + manage)');
    console.log('   - user: No DPR access (restricted)');

  } catch (error) {
    console.error('❌ Error updating roles with DPR permissions:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the update
const runUpdate = async () => {
  await connectDB();
  await updateRolesWithDPR();
};

// Execute if run directly
if (require.main === module) {
  runUpdate().catch(console.error);
}

module.exports = { updateRolesWithDPR };
