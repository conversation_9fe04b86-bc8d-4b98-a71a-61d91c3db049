const express = require('express');
const router = express.Router();
const Role = require('../models/Role');
const User = require('../models/User');
const jwt = require('jsonwebtoken');

// Simple auth middleware
const auth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(401).json({ message: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (err) {
    return res.status(401).json({ message: 'Invalid token' });
  }
};

// Get user permissions based on their role
router.get('/user-permissions', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get user with their role
    const user = await User.findById(userId).select('role');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get role permissions from database
    const role = await Role.findOne({ name: user.role });
    if (!role) {
      return res.status(404).json({ message: 'Role not found' });
    }

    res.json({
      userRole: user.role,
      permissions: role.permissions
    });

  } catch (error) {
    console.error('Error fetching user permissions:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get all available permissions (for admin use)
router.get('/available-permissions', auth, async (req, res) => {
  try {
    // Only allow superadmin to view all permissions
    const user = await User.findById(req.user.id);
    if (user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const roles = await Role.find({}, 'name displayName permissions');
    res.json(roles);

  } catch (error) {
    console.error('Error fetching available permissions:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
