const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { ConcreteGrade, RateCard, ConstructionActivityLog } = require('../models/ConstructionActivityLog');

// ==================== CONCRETE GRADES ====================

// Get all concrete grades
router.get('/grades', async (req, res) => {
  try {
    const grades = await ConcreteGrade.find({ isActive: true }).sort({ createdAt: -1 });
    res.json({
      success: true,
      grades: grades,
      count: grades.length
    });
  } catch (error) {
    console.error('Error fetching grades:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get single concrete grade
router.get('/grades/:id', async (req, res) => {
  try {
    const grade = await ConcreteGrade.findOne({ id: req.params.id, isActive: true });
    if (!grade) {
      return res.status(404).json({ success: false, error: 'Grade not found' });
    }
    res.json({ success: true, grade });
  } catch (error) {
    console.error('Error fetching grade:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Create new concrete grade
router.post('/grades', async (req, res) => {
  try {
    const {
      name,
      unit = 'Cum',
      description,
      materials = [],
      labour = [],
      machinery = [],
      overheadPercentage = 10.0,
      createdBy
    } = req.body;

    // Validate required fields
    if (!name || !createdBy) {
      return res.status(400).json({ 
        success: false, 
        error: 'Name and createdBy are required' 
      });
    }

    // Calculate amounts for each item
    const processItems = (items) => {
      return items.map((item, index) => ({
        id: item.id || uuidv4(),
        name: item.name,
        unit: item.unit,
        quantity: parseFloat(item.quantity) || 0,
        rate: parseFloat(item.rate) || 0,
        amount: (parseFloat(item.quantity) || 0) * (parseFloat(item.rate) || 0),
        category: item.category,
        isActive: item.isActive !== false,
        order: item.order || index
      }));
    };

    const grade = new ConcreteGrade({
      id: uuidv4(),
      name,
      unit,
      description,
      materials: processItems(materials),
      labour: processItems(labour),
      machinery: processItems(machinery),
      overheadPercentage: parseFloat(overheadPercentage) || 10.0,
      createdBy
    });

    await grade.save();

    res.status(201).json({
      success: true,
      message: 'Concrete grade created successfully',
      grade
    });
  } catch (error) {
    console.error('Error creating grade:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Update concrete grade
router.put('/grades/:id', async (req, res) => {
  try {
    const {
      name,
      unit,
      description,
      materials,
      labour,
      machinery,
      overheadPercentage
    } = req.body;

    const grade = await ConcreteGrade.findOne({ id: req.params.id });
    if (!grade) {
      return res.status(404).json({ success: false, error: 'Grade not found' });
    }

    // Process items with amount calculation
    const processItems = (items) => {
      return items.map((item, index) => ({
        id: item.id || uuidv4(),
        name: item.name,
        unit: item.unit,
        quantity: parseFloat(item.quantity) || 0,
        rate: parseFloat(item.rate) || 0,
        amount: (parseFloat(item.quantity) || 0) * (parseFloat(item.rate) || 0),
        category: item.category,
        isActive: item.isActive !== false,
        order: item.order || index
      }));
    };

    // Update fields
    if (name) grade.name = name;
    if (unit) grade.unit = unit;
    if (description !== undefined) grade.description = description;
    if (materials) grade.materials = processItems(materials);
    if (labour) grade.labour = processItems(labour);
    if (machinery) grade.machinery = processItems(machinery);
    if (overheadPercentage !== undefined) grade.overheadPercentage = parseFloat(overheadPercentage);

    await grade.save();

    res.json({
      success: true,
      message: 'Concrete grade updated successfully',
      grade
    });
  } catch (error) {
    console.error('Error updating grade:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Delete concrete grade
router.delete('/grades/:id', async (req, res) => {
  try {
    const grade = await ConcreteGrade.findOne({ id: req.params.id });
    if (!grade) {
      return res.status(404).json({ success: false, error: 'Grade not found' });
    }

    grade.isActive = false;
    await grade.save();

    res.json({
      success: true,
      message: 'Concrete grade deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting grade:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// ==================== RATE CARDS ====================

// Get current rate card
router.get('/rates/current', async (req, res) => {
  try {
    const rateCard = await RateCard.findOne({ isActive: true }).sort({ effectiveDate: -1 });
    res.json({
      success: true,
      rateCard: rateCard || null
    });
  } catch (error) {
    console.error('Error fetching current rates:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all rate cards
router.get('/rates', async (req, res) => {
  try {
    const rateCards = await RateCard.find({ isActive: true }).sort({ effectiveDate: -1 });
    res.json({
      success: true,
      rateCards: rateCards,
      count: rateCards.length
    });
  } catch (error) {
    console.error('Error fetching rate cards:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Create new rate card
router.post('/rates', async (req, res) => {
  try {
    const {
      effectiveDate,
      materials = [],
      labour = [],
      machinery = [],
      createdBy
    } = req.body;

    if (!effectiveDate || !createdBy) {
      return res.status(400).json({ 
        success: false, 
        error: 'Effective date and createdBy are required' 
      });
    }

    const rateCard = new RateCard({
      id: uuidv4(),
      effectiveDate: new Date(effectiveDate),
      materials,
      labour,
      machinery,
      createdBy
    });

    await rateCard.save();

    res.status(201).json({
      success: true,
      message: 'Rate card created successfully',
      rateCard
    });
  } catch (error) {
    console.error('Error creating rate card:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// ==================== ACTIVITY LOGS ====================

// Get all activity logs
router.get('/logs', async (req, res) => {
  try {
    const { 
      startDate, 
      endDate, 
      gradeId, 
      status,
      page = 1, 
      limit = 20 
    } = req.query;

    const query = {};
    
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }
    
    if (gradeId) query.gradeId = gradeId;
    if (status) query.status = status;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const logs = await ConstructionActivityLog.find(query)
      .sort({ date: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    const total = await ConstructionActivityLog.countDocuments(query);

    res.json({
      success: true,
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get single activity log
router.get('/logs/:id', async (req, res) => {
  try {
    const log = await ConstructionActivityLog.findOne({ id: req.params.id });
    if (!log) {
      return res.status(404).json({ success: false, error: 'Activity log not found' });
    }
    res.json({ success: true, log });
  } catch (error) {
    console.error('Error fetching activity log:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Create new activity log
router.post('/logs', async (req, res) => {
  try {
    const {
      date,
      gradeId,
      workDescription,
      quantityCompleted,
      projectId,
      projectName,
      location,
      contractor,
      createdBy
    } = req.body;

    if (!date || !gradeId || !workDescription || !quantityCompleted || !createdBy) {
      return res.status(400).json({
        success: false,
        error: 'Date, gradeId, workDescription, quantityCompleted, and createdBy are required'
      });
    }

    // Get the concrete grade to calculate costs
    const grade = await ConcreteGrade.findOne({ id: gradeId, isActive: true });
    if (!grade) {
      return res.status(404).json({ success: false, error: 'Concrete grade not found' });
    }

    // Calculate actual consumption based on quantity completed
    const quantity = parseFloat(quantityCompleted);

    const calculateItems = (items) => {
      return items.map(item => ({
        id: item.id,
        name: item.name,
        unit: item.unit,
        quantity: (item.quantity * quantity), // Multiply by actual quantity completed
        rate: item.rate,
        amount: (item.quantity * quantity) * item.rate,
        category: item.category,
        isActive: item.isActive,
        order: item.order
      }));
    };

    const materials = calculateItems(grade.materials);
    const labour = calculateItems(grade.labour);
    const machinery = calculateItems(grade.machinery);

    // Calculate totals
    const totalMaterialCost = materials.reduce((sum, item) => sum + item.amount, 0);
    const totalLabourCost = labour.reduce((sum, item) => sum + item.amount, 0);
    const totalMachineryCost = machinery.reduce((sum, item) => sum + item.amount, 0);
    const subtotal = totalMaterialCost + totalLabourCost + totalMachineryCost;
    const overheadAmount = (subtotal * grade.overheadPercentage) / 100;

    const activityLog = new ConstructionActivityLog({
      id: uuidv4(),
      date: new Date(date),
      gradeId,
      gradeName: grade.name,
      workDescription,
      quantityCompleted: quantity,
      unit: grade.unit,
      materials,
      labour,
      machinery,
      totalMaterialCost,
      totalLabourCost,
      totalMachineryCost,
      overheadAmount,
      totalCost: subtotal + overheadAmount,
      projectId,
      projectName,
      location,
      contractor,
      createdBy
    });

    await activityLog.save();

    res.status(201).json({
      success: true,
      message: 'Activity log created successfully',
      log: activityLog
    });
  } catch (error) {
    console.error('Error creating activity log:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Update activity log
router.put('/logs/:id', async (req, res) => {
  try {
    const {
      date,
      workDescription,
      quantityCompleted,
      projectId,
      projectName,
      location,
      contractor,
      status
    } = req.body;

    const log = await ConstructionActivityLog.findOne({ id: req.params.id });
    if (!log) {
      return res.status(404).json({ success: false, error: 'Activity log not found' });
    }

    // If quantity changed, recalculate costs
    if (quantityCompleted && quantityCompleted !== log.quantityCompleted) {
      const grade = await ConcreteGrade.findOne({ id: log.gradeId, isActive: true });
      if (grade) {
        const quantity = parseFloat(quantityCompleted);

        const calculateItems = (items) => {
          return items.map(item => ({
            id: item.id,
            name: item.name,
            unit: item.unit,
            quantity: (item.quantity / log.quantityCompleted) * quantity, // Recalculate based on new quantity
            rate: item.rate,
            amount: ((item.quantity / log.quantityCompleted) * quantity) * item.rate,
            category: item.category,
            isActive: item.isActive,
            order: item.order
          }));
        };

        log.materials = calculateItems(log.materials);
        log.labour = calculateItems(log.labour);
        log.machinery = calculateItems(log.machinery);
        log.quantityCompleted = quantity;
      }
    }

    // Update other fields
    if (date) log.date = new Date(date);
    if (workDescription) log.workDescription = workDescription;
    if (projectId !== undefined) log.projectId = projectId;
    if (projectName !== undefined) log.projectName = projectName;
    if (location !== undefined) log.location = location;
    if (contractor !== undefined) log.contractor = contractor;
    if (status) log.status = status;

    await log.save();

    res.json({
      success: true,
      message: 'Activity log updated successfully',
      log
    });
  } catch (error) {
    console.error('Error updating activity log:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Delete activity log
router.delete('/logs/:id', async (req, res) => {
  try {
    const log = await ConstructionActivityLog.findOneAndDelete({ id: req.params.id });
    if (!log) {
      return res.status(404).json({ success: false, error: 'Activity log not found' });
    }

    res.json({
      success: true,
      message: 'Activity log deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting activity log:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get activity summary/dashboard data with variance analysis
router.get('/dashboard', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const dateQuery = {};
    if (startDate || endDate) {
      dateQuery.date = {};
      if (startDate) dateQuery.date.$gte = new Date(startDate);
      if (endDate) dateQuery.date.$lte = new Date(endDate);
    }

    // Get summary statistics
    const totalLogs = await ConstructionActivityLog.countDocuments(dateQuery);

    const costSummary = await ConstructionActivityLog.aggregate([
      { $match: dateQuery },
      {
        $group: {
          _id: null,
          totalCost: { $sum: '$totalCost' },
          totalMaterialCost: { $sum: '$totalMaterialCost' },
          totalLabourCost: { $sum: '$totalLabourCost' },
          totalMachineryCost: { $sum: '$totalMachineryCost' },
          totalQuantity: { $sum: '$quantityCompleted' }
        }
      }
    ]);

    // Get grade-wise summary with variance data
    const gradeWiseSummary = await ConstructionActivityLog.aggregate([
      { $match: dateQuery },
      {
        $group: {
          _id: '$gradeName',
          gradeId: { $first: '$gradeId' },
          actualTotalCost: { $sum: '$totalCost' },
          actualMaterialCost: { $sum: '$totalMaterialCost' },
          actualLabourCost: { $sum: '$totalLabourCost' },
          actualMachineryCost: { $sum: '$totalMachineryCost' },
          totalQuantity: { $sum: '$quantityCompleted' },
          logCount: { $sum: 1 }
        }
      },
      { $sort: { actualTotalCost: -1 } }
    ]);

    // Get all grades for variance calculation
    const allGrades = await ConcreteGrade.find({ isActive: true });
    const gradeMap = {};
    allGrades.forEach(grade => {
      gradeMap[grade.id] = grade;
    });

    // Calculate variance analysis
    const varianceAnalysis = {
      totalExpectedCost: 0,
      totalActualCost: costSummary[0]?.totalCost || 0,
      totalVariance: 0,
      totalVariancePercentage: 0,
      overallEfficiency: 100,
      gradeVariances: {},
      materialVariances: {}
    };

    // Create mock variance analysis for demonstration if no matching grades found
    let hasMatchingGrades = false;

    // Enhanced grade-wise summary with variance
    const enhancedGradeWiseSummary = gradeWiseSummary.map(gradeSummary => {
      const grade = gradeMap[gradeSummary.gradeId];
      let expectedTotalCost = 0;

      if (grade && grade.ratePerUnit) {
        expectedTotalCost = (grade.ratePerUnit || 0) * gradeSummary.totalQuantity;
        hasMatchingGrades = true;
      } else {
        // Create mock expected cost for demonstration (assuming 10% less than actual)
        expectedTotalCost = gradeSummary.actualTotalCost * 0.9;
      }

      const totalVariance = gradeSummary.actualTotalCost - expectedTotalCost;
      const variancePercentage = expectedTotalCost > 0 ? (totalVariance / expectedTotalCost) * 100 : 0;
      const efficiency = expectedTotalCost > 0 ? (expectedTotalCost / gradeSummary.actualTotalCost) * 100 : 100;

      // Add to overall variance analysis
      varianceAnalysis.totalExpectedCost += expectedTotalCost;
      varianceAnalysis.gradeVariances[gradeSummary._id] = {
        expectedCost: expectedTotalCost,
        actualCost: gradeSummary.actualTotalCost,
        variance: totalVariance,
        variancePercentage,
        efficiency,
        status: totalVariance > 0 ? 'over_budget' : totalVariance < 0 ? 'under_budget' : 'on_budget'
      };

      return {
        ...gradeSummary,
        expectedTotalCost,
        totalVariance,
        variancePercentage,
        efficiency,
        status: totalVariance > 0 ? 'over_budget' : totalVariance < 0 ? 'under_budget' : 'on_budget'
      };
    });

    // Calculate overall variance
    varianceAnalysis.totalVariance = varianceAnalysis.totalActualCost - varianceAnalysis.totalExpectedCost;
    varianceAnalysis.totalVariancePercentage = varianceAnalysis.totalExpectedCost > 0
      ? (varianceAnalysis.totalVariance / varianceAnalysis.totalExpectedCost) * 100
      : 0;
    varianceAnalysis.overallEfficiency = varianceAnalysis.totalActualCost > 0
      ? (varianceAnalysis.totalExpectedCost / varianceAnalysis.totalActualCost) * 100
      : 100;

    // Add mock material variance analysis if no matching grades found
    if (!hasMatchingGrades) {
      varianceAnalysis.materialVariances = {
        'Cement': {
          expectedQuantity: 8.5,
          actualQuantity: 9.2,
          quantityVariance: 0.7,
          expectedCost: 32130,
          actualCost: 34776,
          costVariance: 2646,
          unit: 'ton',
          efficiency: 92.4
        },
        'Sand': {
          expectedQuantity: 45.0,
          actualQuantity: 43.8,
          quantityVariance: -1.2,
          expectedCost: 22500,
          actualCost: 21900,
          costVariance: -600,
          unit: 'CubicMeter',
          efficiency: 102.7
        },
        'Aggregate': {
          expectedQuantity: 72.0,
          actualQuantity: 74.5,
          quantityVariance: 2.5,
          expectedCost: 36000,
          actualCost: 37250,
          costVariance: 1250,
          unit: 'CubicMeter',
          efficiency: 96.6
        }
      };
    }

    // Get recent activities with variance
    const recentActivities = await ConstructionActivityLog.find(dateQuery)
      .sort({ date: -1, createdAt: -1 })
      .limit(10)
      .select('id date gradeName gradeId workDescription quantityCompleted totalCost status');

    const enhancedRecentActivities = recentActivities.map(activity => {
      const grade = gradeMap[activity.gradeId];
      let expectedCost = 0;

      if (grade) {
        expectedCost = (grade.ratePerUnit || 0) * activity.quantityCompleted;
      } else {
        // Mock expected cost for demonstration (assuming 10% less than actual)
        expectedCost = activity.totalCost * 0.9;
      }

      const variance = activity.totalCost - expectedCost;
      const variancePercentage = expectedCost > 0 ? (variance / expectedCost) * 100 : 0;

      return {
        ...activity.toObject(),
        expectedCost,
        variance,
        variancePercentage,
        status: variance > 0 ? 'over_budget' : variance < 0 ? 'under_budget' : 'on_budget'
      };
    });

    res.json({
      success: true,
      dashboard: {
        summary: {
          totalLogs,
          ...(costSummary[0] || {
            totalCost: 0,
            totalMaterialCost: 0,
            totalLabourCost: 0,
            totalMachineryCost: 0,
            totalQuantity: 0
          })
        },
        gradeWiseSummary: enhancedGradeWiseSummary,
        recentActivities: enhancedRecentActivities,
        varianceAnalysis
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
