"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ptPT = void 0;
var _getPickersLocalization = require("./utils/getPickersLocalization");
const timeViews = {
  hours: 'horas',
  minutes: 'minutos',
  seconds: 'segundos',
  meridiem: 'meridiano'
};
const ptPTPickers = {
  // Calendar navigation
  previousMonth: 'Mês anterior',
  nextMonth: 'Próximo mês',
  // View navigation
  openPreviousView: 'Abrir seleção anterior',
  openNextView: 'Abrir próxima seleção',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'A seleção do ano está aberta, altere para a seleção do calendário' : 'A seleção do calendários está aberta, altere para a seleção do ano',
  // DateRange labels
  start: 'Início',
  end: 'Fim',
  startDate: 'Data de início',
  startTime: 'Hora de início',
  endDate: 'Data de fim',
  endTime: 'Hora de fim',
  // Action bar
  cancelButtonLabel: 'Cancelar',
  clearButtonLabel: 'Limpar',
  okButtonLabel: 'OK',
  todayButtonLabel: 'Hoje',
  nextStepButtonLabel: 'Próximo',
  // Toolbar titles
  datePickerToolbarTitle: 'Selecione a data',
  dateTimePickerToolbarTitle: 'Selecione a data e a hora',
  timePickerToolbarTitle: 'Selecione a hora',
  dateRangePickerToolbarTitle: 'Selecione o intervalo de datas',
  // timeRangePickerToolbarTitle: 'Select time range',

  // Clock labels
  clockLabelText: (view, formattedTime) => `Selecione ${timeViews[view]}. ${!formattedTime ? 'Hora não selecionada' : `Selecionado a hora ${formattedTime}`}`,
  hoursClockNumberText: hours => `${hours} horas`,
  minutesClockNumberText: minutes => `${minutes} minutos`,
  secondsClockNumberText: seconds => `${seconds} segundos`,
  // Digital clock labels
  selectViewText: view => `Selecione ${timeViews[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Número da semana',
  calendarWeekNumberHeaderText: 'N.º',
  calendarWeekNumberAriaLabelText: weekNumber => `Semana ${weekNumber}`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: formattedDate => formattedDate ? `Escolha uma data, a data selecionada é ${formattedDate}` : 'Escolha uma data',
  openTimePickerDialogue: formattedTime => formattedTime ? `Escolha uma hora, a hora selecionada é ${formattedTime}` : 'Escolha uma hora',
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: 'Limpar valor',
  // Table labels
  timeTableLabel: 'escolha uma hora',
  dateTableLabel: 'escolha uma data',
  // Field section placeholders
  fieldYearPlaceholder: params => 'A'.repeat(params.digitAmount),
  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  fieldDayPlaceholder: () => 'DD',
  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'SSSS' : 'SS',
  fieldHoursPlaceholder: () => 'hh',
  fieldMinutesPlaceholder: () => 'mm',
  fieldSecondsPlaceholder: () => 'ss',
  fieldMeridiemPlaceholder: () => 'aa',
  // View names
  year: 'Ano',
  month: 'Mês',
  day: 'Dia',
  weekDay: 'Dia da Semana',
  hours: 'Horas',
  minutes: 'Minutos',
  seconds: 'Segundos',
  meridiem: 'Meridiano',
  // Common
  empty: 'Vazio'
};
const ptPT = exports.ptPT = (0, _getPickersLocalization.getPickersLocalization)(ptPTPickers);