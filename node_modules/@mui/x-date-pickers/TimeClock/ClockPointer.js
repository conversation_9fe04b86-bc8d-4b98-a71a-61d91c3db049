"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ClockPointer = ClockPointer;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _clsx = _interopRequireDefault(require("clsx"));
var _styles = require("@mui/material/styles");
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _shared = require("./shared");
var _clockPointerClasses = require("./clockPointerClasses");
var _usePickerPrivateContext = require("../internals/hooks/usePickerPrivateContext");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["className", "classes", "isBetweenTwoClockValues", "isInner", "type", "viewValue"];
const useUtilityClasses = classes => {
  const slots = {
    root: ['root'],
    thumb: ['thumb']
  };
  return (0, _composeClasses.default)(slots, _clockPointerClasses.getClockPointerUtilityClass, classes);
};
const ClockPointerRoot = (0, _styles.styled)('div', {
  name: 'MuiClockPointer',
  slot: 'Root'
})(({
  theme
}) => ({
  width: 2,
  backgroundColor: (theme.vars || theme).palette.primary.main,
  position: 'absolute',
  left: 'calc(50% - 1px)',
  bottom: '50%',
  transformOrigin: 'center bottom 0px',
  variants: [{
    props: {
      isClockPointerAnimated: true
    },
    style: {
      transition: theme.transitions.create(['transform', 'height'])
    }
  }]
}));
const ClockPointerThumb = (0, _styles.styled)('div', {
  name: 'MuiClockPointer',
  slot: 'Thumb'
})(({
  theme
}) => ({
  width: 4,
  height: 4,
  backgroundColor: (theme.vars || theme).palette.primary.contrastText,
  borderRadius: '50%',
  position: 'absolute',
  top: -21,
  left: `calc(50% - ${_shared.CLOCK_HOUR_WIDTH / 2}px)`,
  border: `${(_shared.CLOCK_HOUR_WIDTH - 4) / 2}px solid ${(theme.vars || theme).palette.primary.main}`,
  boxSizing: 'content-box',
  variants: [{
    props: {
      isClockPointerBetweenTwoValues: false
    },
    style: {
      backgroundColor: (theme.vars || theme).palette.primary.main
    }
  }]
}));

/**
 * @ignore - internal component.
 */
function ClockPointer(inProps) {
  const props = (0, _styles.useThemeProps)({
    props: inProps,
    name: 'MuiClockPointer'
  });
  const {
      className,
      classes: classesProp,
      isBetweenTwoClockValues,
      isInner,
      type,
      viewValue
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const previousType = React.useRef(type);
  React.useEffect(() => {
    previousType.current = type;
  }, [type]);
  const {
    ownerState: pickerOwnerState
  } = (0, _usePickerPrivateContext.usePickerPrivateContext)();
  const ownerState = (0, _extends2.default)({}, pickerOwnerState, {
    isClockPointerAnimated: previousType.current !== type,
    isClockPointerBetweenTwoValues: isBetweenTwoClockValues
  });
  const classes = useUtilityClasses(classesProp);
  const getAngleStyle = () => {
    const max = type === 'hours' ? 12 : 60;
    let angle = 360 / max * viewValue;
    if (type === 'hours' && viewValue > 12) {
      angle -= 360; // round up angle to max 360 degrees
    }
    return {
      height: Math.round((isInner ? 0.26 : 0.4) * _shared.CLOCK_WIDTH),
      transform: `rotateZ(${angle}deg)`
    };
  };
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(ClockPointerRoot, (0, _extends2.default)({
    style: getAngleStyle(),
    className: (0, _clsx.default)(classes.root, className),
    ownerState: ownerState
  }, other, {
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(ClockPointerThumb, {
      ownerState: ownerState,
      className: classes.thumb
    })
  }));
}