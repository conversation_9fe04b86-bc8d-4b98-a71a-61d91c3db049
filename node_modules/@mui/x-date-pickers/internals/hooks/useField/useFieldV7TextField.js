"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useFieldV7TextField = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _useForkRef = _interopRequireDefault(require("@mui/utils/useForkRef"));
var _useEventCallback = _interopRequireDefault(require("@mui/utils/useEventCallback"));
var _useEnhancedEffect = _interopRequireDefault(require("@mui/utils/useEnhancedEffect"));
var _useField = require("./useField.utils");
var _utils = require("../../utils/utils");
var _hooks = require("../../../hooks");
var _useFieldCharacterEditing = require("./useFieldCharacterEditing");
var _useFieldState = require("./useFieldState");
var _useFieldInternalPropsWithDefaults = require("./useFieldInternalPropsWithDefaults");
var _syncSelectionToDOM = require("./syncSelectionToDOM");
var _useFieldRootProps = require("./useFieldRootProps");
var _useFieldHiddenInputProps = require("./useFieldHiddenInputProps");
var _useFieldSectionContainerProps = require("./useFieldSectionContainerProps");
var _useFieldSectionContentProps = require("./useFieldSectionContentProps");
const useFieldV7TextField = parameters => {
  const {
    props,
    manager,
    skipContextFieldRefAssignment,
    manager: {
      valueType,
      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel
    }
  } = parameters;
  const {
    internalProps,
    forwardedProps
  } = (0, _hooks.useSplitFieldProps)(props, valueType);
  const internalPropsWithDefaults = (0, _useFieldInternalPropsWithDefaults.useFieldInternalPropsWithDefaults)({
    manager,
    internalProps,
    skipContextFieldRefAssignment
  });
  const {
    sectionListRef: sectionListRefProp,
    onBlur,
    onClick,
    onFocus,
    onInput,
    onPaste,
    onKeyDown,
    onClear,
    clearable
  } = forwardedProps;
  const {
    disabled = false,
    readOnly = false,
    autoFocus = false,
    focused: focusedProp,
    unstableFieldRef
  } = internalPropsWithDefaults;
  const sectionListRef = React.useRef(null);
  const handleSectionListRef = (0, _useForkRef.default)(sectionListRefProp, sectionListRef);
  const domGetters = React.useMemo(() => ({
    isReady: () => sectionListRef.current != null,
    getRoot: () => sectionListRef.current.getRoot(),
    getSectionContainer: sectionIndex => sectionListRef.current.getSectionContainer(sectionIndex),
    getSectionContent: sectionIndex => sectionListRef.current.getSectionContent(sectionIndex),
    getSectionIndexFromDOMElement: element => sectionListRef.current.getSectionIndexFromDOMElement(element)
  }), [sectionListRef]);
  const stateResponse = (0, _useFieldState.useFieldState)({
    manager,
    internalPropsWithDefaults,
    forwardedProps
  });
  const {
    // States and derived states
    areAllSectionsEmpty,
    error,
    parsedSelectedSections,
    sectionOrder,
    state,
    value,
    // Methods to update the states
    clearValue,
    setSelectedSections
  } = stateResponse;
  const applyCharacterEditing = (0, _useFieldCharacterEditing.useFieldCharacterEditing)({
    stateResponse
  });
  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);
  const [focused, setFocused] = React.useState(false);
  function focusField(newSelectedSections = 0) {
    if (disabled || !sectionListRef.current ||
    // if the field is already focused, we don't need to focus it again
    getActiveSectionIndex(sectionListRef) != null) {
      return;
    }
    const newParsedSelectedSections = (0, _useField.parseSelectedSections)(newSelectedSections, state.sections);
    setFocused(true);
    sectionListRef.current.getSectionContent(newParsedSelectedSections).focus();
  }
  const rootProps = (0, _useFieldRootProps.useFieldRootProps)({
    manager,
    internalPropsWithDefaults,
    stateResponse,
    applyCharacterEditing,
    focused,
    setFocused,
    domGetters
  });
  const hiddenInputProps = (0, _useFieldHiddenInputProps.useFieldHiddenInputProps)({
    manager,
    stateResponse
  });
  const createSectionContainerProps = (0, _useFieldSectionContainerProps.useFieldSectionContainerProps)({
    stateResponse,
    internalPropsWithDefaults
  });
  const createSectionContentProps = (0, _useFieldSectionContentProps.useFieldSectionContentProps)({
    manager,
    stateResponse,
    applyCharacterEditing,
    internalPropsWithDefaults,
    domGetters,
    focused
  });
  const handleRootKeyDown = (0, _useEventCallback.default)(event => {
    onKeyDown?.(event);
    rootProps.onKeyDown(event);
  });
  const handleRootBlur = (0, _useEventCallback.default)(event => {
    onBlur?.(event);
    rootProps.onBlur(event);
  });
  const handleRootFocus = (0, _useEventCallback.default)(event => {
    onFocus?.(event);
    rootProps.onFocus(event);
  });
  const handleRootClick = (0, _useEventCallback.default)(event => {
    // The click event on the clear or open button would propagate to the input, trigger this handler and result in an inadvertent section selection.
    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a propagated call, which should be skipped.
    if (event.isDefaultPrevented()) {
      return;
    }
    onClick?.(event);
    rootProps.onClick(event);
  });
  const handleRootPaste = (0, _useEventCallback.default)(event => {
    onPaste?.(event);
    rootProps.onPaste(event);
  });
  const handleRootInput = (0, _useEventCallback.default)(event => {
    onInput?.(event);
    rootProps.onInput(event);
  });
  const handleClear = (0, _useEventCallback.default)((event, ...args) => {
    event.preventDefault();
    onClear?.(event, ...args);
    clearValue();
    if (!isFieldFocused(sectionListRef)) {
      // setSelectedSections is called internally
      focusField(0);
    } else {
      setSelectedSections(sectionOrder.startIndex);
    }
  });
  const elements = React.useMemo(() => {
    return state.sections.map((section, sectionIndex) => {
      const content = createSectionContentProps(section, sectionIndex);
      return {
        container: createSectionContainerProps(sectionIndex),
        content: createSectionContentProps(section, sectionIndex),
        before: {
          children: section.startSeparator
        },
        after: {
          children: section.endSeparator,
          'data-range-position': section.isEndFormatSeparator ? content['data-range-position'] : undefined
        }
      };
    });
  }, [state.sections, createSectionContainerProps, createSectionContentProps]);
  React.useEffect(() => {
    if (sectionListRef.current == null) {
      throw new Error(['MUI X: The `sectionListRef` prop has not been initialized by `PickersSectionList`', 'You probably tried to pass a component to the `textField` slot that contains an `<input />` element instead of a `PickersSectionList`.', '', 'If you want to keep using an `<input />` HTML element for the editing, please add the `enableAccessibleFieldDOMStructure={false}` prop to your Picker or Field component:', '', '<DatePicker enableAccessibleFieldDOMStructure={false} slots={{ textField: MyCustomTextField }} />', '', 'Learn more about the field accessible DOM structure on the MUI documentation: https://mui.com/x/react-date-pickers/fields/#fields-to-edit-a-single-element'].join('\n'));
    }
    if (autoFocus && !disabled && sectionListRef.current) {
      sectionListRef.current.getSectionContent(sectionOrder.startIndex).focus();
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  (0, _useEnhancedEffect.default)(() => {
    if (!focused || !sectionListRef.current) {
      return;
    }
    if (parsedSelectedSections === 'all') {
      sectionListRef.current.getRoot().focus();
    } else if (typeof parsedSelectedSections === 'number') {
      const domElement = sectionListRef.current.getSectionContent(parsedSelectedSections);
      if (domElement) {
        domElement.focus();
      }
    }
  }, [parsedSelectedSections, focused]);
  (0, _useEnhancedEffect.default)(() => {
    (0, _syncSelectionToDOM.syncSelectionToDOM)({
      focused,
      domGetters,
      stateResponse
    });
  });
  React.useImperativeHandle(unstableFieldRef, () => ({
    getSections: () => state.sections,
    getActiveSectionIndex: () => getActiveSectionIndex(sectionListRef),
    setSelectedSections: newSelectedSections => {
      if (disabled || !sectionListRef.current) {
        return;
      }
      const newParsedSelectedSections = (0, _useField.parseSelectedSections)(newSelectedSections, state.sections);
      const newActiveSectionIndex = newParsedSelectedSections === 'all' ? 0 : newParsedSelectedSections;
      setFocused(newActiveSectionIndex !== null);
      setSelectedSections(newSelectedSections);
    },
    focusField,
    isFieldFocused: () => isFieldFocused(sectionListRef)
  }));
  return (0, _extends2.default)({}, forwardedProps, rootProps, {
    onBlur: handleRootBlur,
    onClick: handleRootClick,
    onFocus: handleRootFocus,
    onInput: handleRootInput,
    onPaste: handleRootPaste,
    onKeyDown: handleRootKeyDown,
    onClear: handleClear
  }, hiddenInputProps, {
    error,
    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),
    focused: focusedProp ?? focused,
    sectionListRef: handleSectionListRef,
    // Additional
    enableAccessibleFieldDOMStructure: true,
    elements,
    areAllSectionsEmpty,
    disabled,
    readOnly,
    autoFocus,
    openPickerAriaLabel
  });
};
exports.useFieldV7TextField = useFieldV7TextField;
function getActiveSectionIndex(sectionListRef) {
  const activeElement = (0, _utils.getActiveElement)(document);
  if (!activeElement || !sectionListRef.current || !sectionListRef.current.getRoot().contains(activeElement)) {
    return null;
  }
  return sectionListRef.current.getSectionIndexFromDOMElement(activeElement);
}
function isFieldFocused(sectionListRef) {
  const activeElement = (0, _utils.getActiveElement)(document);
  return !!sectionListRef.current && sectionListRef.current.getRoot().contains(activeElement);
}