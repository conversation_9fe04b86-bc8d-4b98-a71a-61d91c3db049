# Change Log
This project adheres to [Semantic Versioning](http://semver.org/).

## 2.2.17
* Fixed CVE warning by moving to Nano ID 3.

## 2.2.16
* Add deprecation notice to project readme (by <PERSON>).

## 2.2.15
* Update `REDUCE_TIME` to generate smaller ID.

## 2.2.14
* Reduce npm package size by `clean-publish`.

## 2.2.13
* Fix `browser` config for webpack.

## 2.2.12
* Fix cluster worker ID for non-Node environments.

## 2.2.11
* Fix React Native support.

## 2.2.10
* Fix theoretical ID limit length.

## 2.2.9
* Fix symbols probability uniformity by using Nano ID (by @shashkovdanil).
* Improve ID generation perfomance.
* Fix `shortid.isValid` with custom alphabet.
* Improve `shortid.isValid` perfomance (by @s7b5en).

## 2.2.8
* Clean npm package from unnecessary files.
