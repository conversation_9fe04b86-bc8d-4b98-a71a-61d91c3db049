import React from "react";
import { BrowserRouter as Router, Route, Routes, Navigate } from "react-router-dom";
import HomePage from "./pages/HomePage";
import Dashboard from "./pages/Dashboard";
import LoginPage from "./pages/LoginPage";
import Inventory from "./pages/Inventory"
import ChartsPage from "./pages/ChartsPage";
import BillDetails from "./pages/BillDetails"
import LandingPage from "./pages/LandingPage"
import RequestPage from "./pages/RequestPage"
import UserManagement from "./pages/UserManagement"
import AdvancedReports from "./pages/AdvancedReports"
import Recommendation from "./pages/Recommendation"
import CustomerAnalysis from "./pages/CustomerAnalysis"
import SupplierAnalysis from "./pages/SupplierAnalysis"
import RegistrationPage from "./pages/RegistrationPage"
import ProductsPage from "./pages/ProductsPage"
import ProductRegistration from "./pages/ProductRegistration"
import StoreLocationsPage from "./pages/StoreLocationsPage"
import StoreRegistrationPage from "./pages/StoreRegistrationPage"
import ProtectedRoute from './utils/ProtectedRoute';

import PlantMachineryManager from "./pages/PlantMachineryManager";
import RoleManagement from "./pages/RoleManagement";
import SiteEngineerRequests from "./pages/SiteEngineerRequests";
import HeadOfficeTransfers from "./pages/HeadOfficeTransfers";
import StoreKeeperReview from "./pages/StoreKeeperReview";
import TransferActivityLogs from "./pages/TransferActivityLogs";
import ProcurementSystem from "./pages/ProcurementSystem";
import ProcurementRequestManager from "./pages/ProcurementRequestManager";
import QuotationManager from "./pages/QuotationManager";
import PurchaseOrderManager from "./pages/PurchaseOrderManager";
import VendorRegistration from "./pages/VendorRegistration";
import ActivityLog from "./pages/ActivityLog";


function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegistrationPage />} />
        <Route path="/request" element={<RequestPage />} />

        <Route
          path="/app"
          element={
            <ProtectedRoute allowedRoles={['superadmin', 'admin', 'user', 'siteengineer', 'storekeeper']}>
              <HomePage />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="inventory" element={<Inventory />} />
          <Route path="products" element={<ProductsPage />} />
          <Route path="product-registration" element={<ProductRegistration />} />
          <Route path="store-locations" element={<StoreLocationsPage />} />
          <Route path="store-registration" element={<StoreRegistrationPage />} />
          <Route path="charts" element={<ChartsPage />} />
          <Route path="bill-details" element={<BillDetails />} />
          <Route path="user-management" element={<UserManagement />} />
          <Route path="role-management" element={<RoleManagement />} />
          <Route path="advanced-reports" element={<AdvancedReports />} />
          <Route path="recommendation" element={<Recommendation />} />
          <Route path="customer-analysis" element={<CustomerAnalysis />} />
          <Route path="supplier-analysis" element={<SupplierAnalysis />} />

          <Route path="site-engineer-requests" element={<SiteEngineerRequests />} />
          <Route path="head-office-transfers" element={<HeadOfficeTransfers />} />
          <Route path="store-keeper-review" element={<StoreKeeperReview />} />
          <Route path="transfer-activity-logs" element={<TransferActivityLogs />} />
          <Route path="machinery-work" element={<PlantMachineryManager />} />
          <Route path="activity-log" element={<ActivityLog />} />
          <Route path="procurement-system" element={<ProcurementRequestManager />} />
          <Route path="procurement-requests" element={<ProcurementRequestManager />} />
          <Route path="quotation-manager" element={<QuotationManager />} />
          <Route path="purchase-orders" element={<PurchaseOrderManager />} />
          <Route path="vendor-registration" element={<VendorRegistration />} />
        </Route>

        {/* Redirect old paths to new structure */}
        <Route path="/manager/*" element={<Navigate to="/app" replace />} />
        <Route path="/user/*" element={<Navigate to="/app" replace />} />

        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;