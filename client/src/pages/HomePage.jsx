import React, { useState, useEffect, useMemo } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  useTheme,
  Stack,
  Collapse,
  CircularProgress,
  Tooltip,
  alpha
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  AccountCircle,
  Dashboard as DashboardIcon,
  BarChart,
  Assessment,
  Equalizer,
  ReceiptLong,
  Logout as LogoutIcon,
  ExpandMore,
  ExpandLess,
  Warehouse,
  Category,

  Store as StoreIcon,
  LocationOn as LocationOnIcon,
  AppRegistration as AppRegistrationIcon,
  Construction as ConstructionIcon,
  Group,
  VpnKey,
  Engineering,
  Security as SecurityIcon,
  Business,
  Store,
  SmartToy as SmartToyIcon,
  Description as DescriptionIcon,
  Upload as UploadIcon,
  Assignment as AssignmentIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import Cookies from 'js-cookie';
import axios from 'axios';
import { getApiUrl } from '../config';
import PremiumLanguageSwitcher from '../components/PremiumLanguageSwitcher';
import '../components/PremiumLanguageSwitcher.css';
import { useTranslation } from 'react-i18next';

// Memoize static values
const drawerWidth = 300;

// Premium drawer styles
const getDrawerItemStyles = (theme, isSelected = false, isSubItem = false) => ({
  borderRadius: 3,
  mb: 1,
  mx: isSubItem ? 2 : 1,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: isSelected ? 4 : 0,
    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
    transition: 'width 0.3s ease',
  },
  '&.Mui-selected': {
    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.15) 0%, rgba(21, 101, 192, 0.1) 100%)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(25, 118, 210, 0.2)',
    boxShadow: '0 4px 20px rgba(25, 118, 210, 0.15)',
    '&:hover': {
      background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.2) 0%, rgba(21, 101, 192, 0.15) 100%)',
      transform: 'translateX(4px)',
      boxShadow: '0 6px 25px rgba(25, 118, 210, 0.2)',
    },
    '&::before': {
      width: 4,
    }
  },
  '&:hover': {
    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%)',
    transform: 'translateX(2px)',
    boxShadow: '0 2px 10px rgba(25, 118, 210, 0.1)',
  }
});

const getSubItemStyles = (theme, isSelected = false) => ({
  borderRadius: 2,
  mb: 0.5,
  ml: 4,
  mr: 2,
  transition: 'all 0.2s ease',
  '&.Mui-selected': {
    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
    '&:hover': {
      background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.15) 0%, rgba(21, 101, 192, 0.1) 100%)',
    }
  },
  '&:hover': {
    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
    transform: 'translateX(4px)',
  }
});

const getNestedItemStyles = (theme) => ({
  borderRadius: 2, 
  mb: 1,
  pl: 4,
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.15),
    }
  }
});

const HomePage = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const userRole = Cookies.get('userRole');
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [inventoryOpen, setInventoryOpen] = useState(false);
  const [securityOpen, setSecurityOpen] = useState(false);
  const [procurementOpen, setProcurementOpen] = useState(false);
  const [roleInfo, setRoleInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sync current page with URL
  useEffect(() => {
    const path = location.pathname;
    const page = path.split('/').pop(); // Get the last part of the path
    if (page && page !== 'app') {
      setCurrentPage(page);

      // Auto-open relevant submenus based on current page
      if (['inventory', 'site-engineer-requests', 'head-office-transfers', 'store-keeper-review', 'store-registration', 'product-management', 'product-registration'].includes(page)) {
        setInventoryOpen(true);
      }
      if (['user-management', 'role-management', 'transfer-activity-logs'].includes(page)) {
        setSecurityOpen(true);
      }
      if (['vendor-registration', 'procurement-system', 'quotation-manager', 'purchase-orders'].includes(page)) {
        setProcurementOpen(true);
      }
      // DPR doesn't need any submenu opening
    }
  }, [location.pathname]);

  // Fetch role info and permissions when component mounts
  useEffect(() => {
    const fetchRoleInfo = async () => {
      try {
        setLoading(true);
        const response = await axios.get(getApiUrl(`api/roles`));
        if (response.data && response.data.length > 0) {
          // Find the role that matches the current user's role
          const userRoleInfo = response.data.find(role => role.name === userRole);
          if (userRoleInfo) {
            setRoleInfo(userRoleInfo);
          }
        }
      } catch (error) {
        console.error('Error fetching role info:', error);
        // If role info can't be fetched, use default permissions
        setRoleInfo({
          permissions: {
            inventory: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
            storeRegistration: { view: userRole !== 'user' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
            products: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
            users: { view: userRole === 'admin' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
            billing: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' },
            machinery: { view: userRole !== 'user' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'siteengineer' },
            reports: { view: userRole !== 'user' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
            roles: { view: userRole === 'superadmin', manage: userRole === 'superadmin' },
            activityLog: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' || userRole === 'siteengineer' }
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRoleInfo();
  }, [userRole]);

  // Debug permissions after roleInfo is loaded
  useEffect(() => {
    if (roleInfo) {
      console.log('🔍 Debug Info:', {
        userRole,
        permissions: roleInfo?.permissions,
        activityLogPermission: roleInfo?.permissions?.activityLog,
        hasActivityLogView: roleInfo?.permissions?.activityLog?.view
      });
    }
  }, [roleInfo, userRole]);

  // Memoize handlers
  const handleDrawerToggle = () => setMobileOpen(prev => !prev);

  const handleLogout = () => {
    Cookies.remove('jwtToken');
    Cookies.remove('email');
    Cookies.remove('userRole');
    navigate('/login');
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // If page is product-management, navigate to inventory instead
    if (page === 'product-management') {
      navigate(`/app/inventory`);
    } else {
      navigate(`/app/${page}`);
    }
  };

  const handleInventoryClick = () => {
    // Only toggle the submenu without navigating
    setInventoryOpen(!inventoryOpen);
  };

  const handleProcurementClick = () => {
    // Only toggle the submenu without navigating
    setProcurementOpen(!procurementOpen);
  };

  // Memoize drawer content
  const drawer = useMemo(() => {
    // If still loading permissions, show a loading indicator
    if (loading) {
      return (
        <Box sx={{ 
          height: '100%', 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center' 
        }}>
          <CircularProgress />
        </Box>
      );
    }

    // Extract permissions or set defaults if not available
    const permissions = roleInfo?.permissions || {
      inventory: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
      storeRegistration: { view: userRole !== 'user' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
      products: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
      users: { view: userRole === 'admin' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
      billing: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' },
      machinery: { view: userRole !== 'user' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'siteengineer' },
      reports: { view: userRole !== 'user' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
      roles: { view: userRole === 'superadmin', manage: userRole === 'superadmin' },
      activityLog: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' || userRole === 'siteengineer' }
    };

    return (
      <Box sx={{
        height: '100%',
        background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%)',
        backdropFilter: 'blur(20px)',
        boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Box sx={{
          p: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
          position: 'relative',
          overflow: 'hidden',
          mb: 2,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
          }
        }}>
          <Avatar sx={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
            backdropFilter: 'blur(20px)',
            border: '2px solid rgba(255, 255, 255, 0.3)',
            width: 52,
            height: 52,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
            position: 'relative',
            zIndex: 1,
          }} src='https://scontent.fhyd7-1.fna.fbcdn.net/v/t39.30808-6/303292734_447599477389457_5103831908007527092_n.jpg?_nc_cat=105&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=ZaF_9rvxcBkQ7kNvwEXFFtz&_nc_oc=AdlgL9TKgV3cfMPclvhzSH8zUVX2otf_-MeMATSe48uheHzgTMcDMugsyjCt8k5X77M&_nc_zt=23&_nc_ht=scontent.fhyd7-1.fna&_nc_gid=G7GKJlur4fuu1vU8taHtyA&oh=00_AfLddV1fl5FLCSyHoq-eKkcmkrG_moljP0HJxiJon1WokQ&oe=68464B20'/>
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: 'white',
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                fontSize: '1.2rem'
              }}
            >
              {t('app.title')}
            </Typography>
          </Box>
        </Box>

        <List sx={{ px: 2, py: 1 }}>
          <ListItemButton
            selected={currentPage === 'dashboard'}
            onClick={() => handlePageChange('dashboard')}
            sx={getDrawerItemStyles(theme, currentPage === 'dashboard')}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              <DashboardIcon
                sx={{
                  color: currentPage === 'dashboard' ? '#1976d2' : 'text.secondary',
                  fontSize: 24,
                  transition: 'all 0.2s ease'
                }}
              />
            </ListItemIcon>
            <ListItemText
              primary={t('menu.dashboard')}
              primaryTypographyProps={{
                fontWeight: currentPage === 'dashboard' ? 700 : 500,
                color: currentPage === 'dashboard' ? '#1976d2' : 'text.primary',
                fontSize: '0.95rem'
              }}
            />
          </ListItemButton>
          
          {/* Inventory Section - Only show if user has permission to view inventory */}
          {permissions.inventory.view && (
            <>
              <ListItemButton
                selected={currentPage === 'inventory' ||
                           currentPage === 'site-engineer-requests' ||
                           currentPage === 'head-office-transfers' ||
                           currentPage === 'store-keeper-review' ||
                           currentPage === 'store-registration' ||
                           currentPage === 'product-management' ||
                           currentPage === 'product-registration' ||
                           currentPage === 'procurement-system'}
                onClick={handleInventoryClick}
                sx={getDrawerItemStyles(theme, currentPage === 'inventory' ||
                                              currentPage === 'site-engineer-requests' ||
                                              currentPage === 'head-office-transfers' ||
                                              currentPage === 'store-keeper-review' ||
                                              currentPage === 'store-registration' ||
                                              currentPage === 'product-management' ||
                                              currentPage === 'product-registration' ||
                                              currentPage === 'procurement-system')}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <Warehouse
                    sx={{
                      color: (currentPage === 'inventory' ||
                              currentPage === 'site-engineer-requests' ||
                              currentPage === 'head-office-transfers' ||
                              currentPage === 'store-keeper-review' ||
                              currentPage === 'store-registration' ||
                              currentPage === 'product-management' ||
                              currentPage === 'product-registration') ? '#1976d2' : 'text.secondary',
                      fontSize: 24,
                      transition: 'all 0.2s ease'
                    }}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={t('menu.inventory')}
                  primaryTypographyProps={{
                    fontWeight: (currentPage === 'inventory' ||
                                 currentPage === 'site-engineer-requests' ||
                                 currentPage === 'head-office-transfers' ||
                                 currentPage === 'store-keeper-review' ||
                                 currentPage === 'store-registration' ||
                                 currentPage === 'product-management' ||
                                 currentPage === 'product-registration') ? 700 : 500,
                    color: (currentPage === 'inventory' ||
                            currentPage === 'site-engineer-requests' ||
                            currentPage === 'head-office-transfers' ||
                            currentPage === 'store-keeper-review' ||
                            currentPage === 'store-registration' ||
                            currentPage === 'product-management' ||
                            currentPage === 'product-registration') ? '#1976d2' : 'text.primary',
                    fontSize: '0.95rem'
                  }}
                />
                <Box sx={{
                  color: (currentPage === 'inventory' ||
                          currentPage === 'site-engineer-requests' ||
                          currentPage === 'head-office-transfers' ||
                          currentPage === 'store-keeper-review' ||
                          currentPage === 'store-registration' ||
                          currentPage === 'product-management' ||
                          currentPage === 'product-registration') ? '#1976d2' : 'text.secondary',
                  transition: 'transform 0.2s ease',
                  transform: inventoryOpen ? 'rotate(180deg)' : 'rotate(0deg)'
                }}>
                  {inventoryOpen ? <ExpandLess /> : <ExpandMore />}
                </Box>
              </ListItemButton>
              
              <Collapse in={inventoryOpen} timeout={300} unmountOnExit>
                <Box sx={{
                  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.03) 0%, rgba(21, 101, 192, 0.01) 100%)',
                  borderRadius: 2,
                  mx: 1,
                  my: 1,
                  border: '1px solid rgba(25, 118, 210, 0.1)'
                }}>
                  <List component="div" disablePadding sx={{ py: 1 }}>
                    {/* Product Management - Show for users with product view permissions */}
                    {permissions.products.view && (
                      <ListItemButton
                        selected={currentPage === 'product-management'}
                        onClick={() => handlePageChange('product-management')}
                        sx={getSubItemStyles(theme, currentPage === 'product-management')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Category
                            sx={{
                              color: currentPage === 'product-management' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={t('menu.productManagement')}
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'product-management' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'product-management' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}

                    {/* Product Registration - Show for users with product manage permissions */}
                    {permissions.products.manage && (
                      <ListItemButton
                        selected={currentPage === 'product-registration'}
                        onClick={() => handlePageChange('product-registration')}
                        sx={getSubItemStyles(theme, currentPage === 'product-registration')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <AppRegistrationIcon
                            sx={{
                              color: currentPage === 'product-registration' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={t('menu.productRegistration')}
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'product-registration' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'product-registration' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}



                    {/* Site Engineer Requests - Show for site engineers and superadmin */}
                    {(userRole === 'siteengineer' || userRole === 'superadmin') && (
                      <ListItemButton
                        selected={currentPage === 'site-engineer-requests'}
                        onClick={() => handlePageChange('site-engineer-requests')}
                        sx={getSubItemStyles(theme, currentPage === 'site-engineer-requests')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Engineering
                            sx={{
                              color: currentPage === 'site-engineer-requests' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Site Engineer Requests"
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'site-engineer-requests' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'site-engineer-requests' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}

                    {/* Head Office Transfers - Show for admin and superadmin */}
                    {(userRole === 'admin' || userRole === 'superadmin') && (
                      <ListItemButton
                        selected={currentPage === 'head-office-transfers'}
                        onClick={() => handlePageChange('head-office-transfers')}
                        sx={getSubItemStyles(theme, currentPage === 'head-office-transfers')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Business
                            sx={{
                              color: currentPage === 'head-office-transfers' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Head Office Transfers"
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'head-office-transfers' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'head-office-transfers' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}

                    {/* Store Keeper Review - Show for store keepers and superadmin */}
                    {(userRole === 'storekeeper' || userRole === 'superadmin') && (
                      <ListItemButton
                        selected={currentPage === 'store-keeper-review'}
                        onClick={() => handlePageChange('store-keeper-review')}
                        sx={getSubItemStyles(theme, currentPage === 'store-keeper-review')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Store
                            sx={{
                              color: currentPage === 'store-keeper-review' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Store Keeper Review"
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'store-keeper-review' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'store-keeper-review' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}

                    {/* Store Registration - Show for users with store registration view permissions */}
                    {permissions.storeRegistration.view && (
                      <ListItemButton
                        selected={currentPage === 'store-registration'}
                        onClick={() => handlePageChange('store-registration')}
                        sx={getSubItemStyles(theme, currentPage === 'store-registration')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <AppRegistrationIcon
                            sx={{
                              color: currentPage === 'store-registration' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={t('menu.storeRegistration')}
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'store-registration' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'store-registration' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}


                  </List>
                </Box>
              </Collapse>
            </>
          )}

          {/* Procurement System - Show for users with procurement permissions */}
          <ListItemButton
            selected={currentPage === 'vendor-registration' ||
                       currentPage === 'procurement-system' ||
                       currentPage === 'quotation-manager' ||
                       currentPage === 'purchase-orders'}
            onClick={handleProcurementClick}
            sx={getDrawerItemStyles(theme, currentPage === 'vendor-registration' ||
                                          currentPage === 'procurement-system' ||
                                          currentPage === 'quotation-manager' ||
                                          currentPage === 'purchase-orders')}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              <SmartToyIcon
                sx={{
                  color: (currentPage === 'vendor-registration' ||
                          currentPage === 'procurement-system' ||
                          currentPage === 'quotation-manager' ||
                          currentPage === 'purchase-orders') ? '#1976d2' : 'text.secondary',
                  fontSize: 24,
                  transition: 'all 0.2s ease'
                }}
              />
            </ListItemIcon>
            <ListItemText
              primary="Procurement System"
              primaryTypographyProps={{
                fontWeight: (currentPage === 'vendor-registration' ||
                             currentPage === 'procurement-system' ||
                             currentPage === 'quotation-manager' ||
                             currentPage === 'purchase-orders') ? 700 : 500,
                color: (currentPage === 'vendor-registration' ||
                        currentPage === 'procurement-system' ||
                        currentPage === 'quotation-manager' ||
                        currentPage === 'purchase-orders') ? '#1976d2' : 'text.primary',
                fontSize: '0.95rem'
              }}
            />
            <Box sx={{
              color: (currentPage === 'vendor-registration' ||
                      currentPage === 'procurement-system' ||
                      currentPage === 'quotation-manager' ||
                      currentPage === 'purchase-orders') ? '#1976d2' : 'text.secondary',
              transition: 'transform 0.2s ease',
              transform: procurementOpen ? 'rotate(180deg)' : 'rotate(0deg)'
            }}>
              {procurementOpen ? <ExpandLess /> : <ExpandMore />}
            </Box>
          </ListItemButton>

          <Collapse in={procurementOpen} timeout={300} unmountOnExit>
            <Box sx={{
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.03) 0%, rgba(21, 101, 192, 0.01) 100%)',
              borderRadius: 2,
              mx: 1,
              my: 1,
              border: '1px solid rgba(25, 118, 210, 0.1)'
            }}>
              <List component="div" disablePadding sx={{ py: 1 }}>
                {/* Vendor Registration */}
                <ListItemButton
                  selected={currentPage === 'vendor-registration'}
                  onClick={() => handlePageChange('vendor-registration')}
                  sx={getSubItemStyles(theme, currentPage === 'vendor-registration')}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <Business
                      sx={{
                        color: currentPage === 'vendor-registration' ? '#1976d2' : 'text.secondary',
                        fontSize: 20
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="Vendor Registration"
                    primaryTypographyProps={{
                      fontWeight: currentPage === 'vendor-registration' ? 600 : 500,
                      fontSize: '0.9rem',
                      color: currentPage === 'vendor-registration' ? '#1976d2' : 'text.primary'
                    }}
                  />
                </ListItemButton>

                {/* Procurement Requests */}
                <ListItemButton
                  selected={currentPage === 'procurement-system'}
                  onClick={() => handlePageChange('procurement-system')}
                  sx={getSubItemStyles(theme, currentPage === 'procurement-system')}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <DescriptionIcon
                      sx={{
                        color: currentPage === 'procurement-system' ? '#1976d2' : 'text.secondary',
                        fontSize: 20
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="Procurement Requests"
                    primaryTypographyProps={{
                      fontWeight: currentPage === 'procurement-system' ? 600 : 500,
                      fontSize: '0.9rem',
                      color: currentPage === 'procurement-system' ? '#1976d2' : 'text.primary'
                    }}
                  />
                </ListItemButton>

                {/* Quotation Manager */}
                <ListItemButton
                  selected={currentPage === 'quotation-manager'}
                  onClick={() => handlePageChange('quotation-manager')}
                  sx={getSubItemStyles(theme, currentPage === 'quotation-manager')}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <UploadIcon
                      sx={{
                        color: currentPage === 'quotation-manager' ? '#1976d2' : 'text.secondary',
                        fontSize: 20
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="Quotation Manager"
                    primaryTypographyProps={{
                      fontWeight: currentPage === 'quotation-manager' ? 600 : 500,
                      fontSize: '0.9rem',
                      color: currentPage === 'quotation-manager' ? '#1976d2' : 'text.primary'
                    }}
                  />
                </ListItemButton>

                {/* Purchase Order Manager */}
                <ListItemButton
                  selected={currentPage === 'purchase-orders'}
                  onClick={() => handlePageChange('purchase-orders')}
                  sx={getSubItemStyles(theme, currentPage === 'purchase-orders')}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <AssignmentIcon
                      sx={{
                        color: currentPage === 'purchase-orders' ? '#1976d2' : 'text.secondary',
                        fontSize: 20
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="Purchase Orders"
                    primaryTypographyProps={{
                      fontWeight: currentPage === 'purchase-orders' ? 600 : 500,
                      fontSize: '0.9rem',
                      color: currentPage === 'purchase-orders' ? '#1976d2' : 'text.primary'
                    }}
                  />
                </ListItemButton>
              </List>
            </Box>
          </Collapse>

          {/* Plant & Machinery - Only show if user has permission to view machinery */}
          {permissions.machinery.view && (
            <ListItemButton
              selected={currentPage === 'machinery-work'}
              onClick={() => handlePageChange('machinery-work')}
              sx={getDrawerItemStyles(theme, currentPage === 'machinery-work')}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                <ConstructionIcon
                  sx={{
                    color: currentPage === 'machinery-work' ? '#1976d2' : 'text.secondary',
                    fontSize: 24,
                    transition: 'all 0.2s ease'
                  }}
                />
              </ListItemIcon>
              <ListItemText
                primary={t('menu.machineryWork')}
                primaryTypographyProps={{
                  fontWeight: currentPage === 'machinery-work' ? 700 : 500,
                  color: currentPage === 'machinery-work' ? '#1976d2' : 'text.primary',
                  fontSize: '0.95rem'
                }}
              />
            </ListItemButton>
          )}

          {/* Activity Log - Only show if user has permission to view activityLog */}
          {permissions.activityLog?.view && (
            <ListItemButton
              selected={currentPage === 'activity-log'}
              onClick={() => handlePageChange('activity-log')}
              sx={getDrawerItemStyles(theme, currentPage === 'activity-log')}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                <TimelineIcon
                  sx={{
                    color: currentPage === 'activity-log' ? '#1976d2' : 'text.secondary',
                    fontSize: 24,
                    transition: 'all 0.2s ease'
                  }}
                />
              </ListItemIcon>
              <ListItemText
                primary={t('menu.activityLog') || 'Activity Log'}
                primaryTypographyProps={{
                  fontWeight: currentPage === 'activity-log' ? 700 : 500,
                  color: currentPage === 'activity-log' ? '#1976d2' : 'text.primary',
                  fontSize: '0.95rem'
                }}
              />
            </ListItemButton>
          )}

          {/* Security Section - Only show if user has permission to view users or roles */}
          {(permissions.users.view || permissions.roles.view) && (
            <>
              <ListItemButton
                selected={currentPage === 'user-management' || currentPage === 'role-management' || currentPage === 'transfer-activity-logs'}
                onClick={() => setSecurityOpen(!securityOpen)}
                sx={getDrawerItemStyles(theme, currentPage === 'user-management' || currentPage === 'role-management')}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <SecurityIcon
                    sx={{
                      color: (currentPage === 'user-management' || currentPage === 'role-management') ? '#1976d2' : 'text.secondary',
                      fontSize: 24,
                      transition: 'all 0.2s ease'
                    }}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={t('menu.security')}
                  primaryTypographyProps={{
                    fontWeight: (currentPage === 'user-management' || currentPage === 'role-management') ? 700 : 500,
                    color: (currentPage === 'user-management' || currentPage === 'role-management') ? '#1976d2' : 'text.primary',
                    fontSize: '0.95rem'
                  }}
                />
                <Box sx={{
                  color: (currentPage === 'user-management' || currentPage === 'role-management') ? '#1976d2' : 'text.secondary',
                  transition: 'transform 0.2s ease',
                  transform: securityOpen ? 'rotate(180deg)' : 'rotate(0deg)'
                }}>
                  {securityOpen ? <ExpandLess /> : <ExpandMore />}
                </Box>
              </ListItemButton>
              
              <Collapse in={securityOpen} timeout={300} unmountOnExit>
                <Box sx={{
                  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.03) 0%, rgba(21, 101, 192, 0.01) 100%)',
                  borderRadius: 2,
                  mx: 1,
                  my: 1,
                  border: '1px solid rgba(25, 118, 210, 0.1)'
                }}>
                  <List component="div" disablePadding sx={{ py: 1 }}>
                    {/* User Management - Show for users with user view permissions */}
                    {permissions.users.view && (
                      <ListItemButton
                        selected={currentPage === 'user-management'}
                        onClick={() => handlePageChange('user-management')}
                        sx={getSubItemStyles(theme, currentPage === 'user-management')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Group
                            sx={{
                              color: currentPage === 'user-management' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={t('menu.userManagement')}
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'user-management' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'user-management' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}

                    {/* Role Management - Show for users with role view permissions */}
                    {permissions.roles.view && (
                      <ListItemButton
                        selected={currentPage === 'role-management'}
                        onClick={() => handlePageChange('role-management')}
                        sx={getSubItemStyles(theme, currentPage === 'role-management')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <VpnKey
                            sx={{
                              color: currentPage === 'role-management' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={t('menu.roleManagement')}
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'role-management' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'role-management' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}

                    {/* Transfer Activity Logs - Show for superadmin only */}
                    {userRole === 'superadmin' && (
                      <ListItemButton
                        selected={currentPage === 'transfer-activity-logs'}
                        onClick={() => handlePageChange('transfer-activity-logs')}
                        sx={getSubItemStyles(theme, currentPage === 'transfer-activity-logs')}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <SecurityIcon
                            sx={{
                              color: currentPage === 'transfer-activity-logs' ? '#1976d2' : 'text.secondary',
                              fontSize: 20
                            }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Transfer Activity Logs"
                          primaryTypographyProps={{
                            fontWeight: currentPage === 'transfer-activity-logs' ? 600 : 500,
                            fontSize: '0.9rem',
                            color: currentPage === 'transfer-activity-logs' ? '#1976d2' : 'text.primary'
                          }}
                        />
                      </ListItemButton>
                    )}
                  </List>
                </Box>
              </Collapse>
            </>
          )}



          {/* Charts - Available to all users */}
          <ListItemButton
            selected={currentPage === 'charts'}
            onClick={() => handlePageChange('charts')}
            sx={getDrawerItemStyles(theme, currentPage === 'charts')}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              <BarChart
                sx={{
                  color: currentPage === 'charts' ? '#1976d2' : 'text.secondary',
                  fontSize: 24,
                  transition: 'all 0.2s ease'
                }}
              />
            </ListItemIcon>
            <ListItemText
              primary={t('menu.charts')}
              primaryTypographyProps={{
                fontWeight: currentPage === 'charts' ? 700 : 500,
                color: currentPage === 'charts' ? '#1976d2' : 'text.primary',
                fontSize: '0.95rem'
              }}
            />
          </ListItemButton>


        </List>

        <Box sx={{ flexGrow: 1 }} />

        <Box sx={{
          p: 2,
          borderTop: '1px solid rgba(25, 118, 210, 0.1)',
          background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(21, 101, 192, 0.01) 100%)'
        }}>
          <ListItemButton
            onClick={handleLogout}
            sx={{
              borderRadius: 3,
              background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(211, 47, 47, 0.05) 100%)',
              border: '1px solid rgba(244, 67, 54, 0.2)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.15) 0%, rgba(211, 47, 47, 0.1) 100%)',
                transform: 'translateX(4px)',
                boxShadow: '0 4px 20px rgba(244, 67, 54, 0.2)',
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              <LogoutIcon
                sx={{
                  color: '#f44336',
                  fontSize: 24,
                  transition: 'all 0.2s ease'
                }}
              />
            </ListItemIcon>
            <ListItemText
              primary={t('menu.logout')}
              primaryTypographyProps={{
                fontWeight: 600,
                color: '#f44336',
                fontSize: '0.95rem'
              }}
            />
          </ListItemButton>
        </Box>
      </Box>
    );
  }, [currentPage, inventoryOpen, securityOpen, procurementOpen, loading, roleInfo, userRole, handlePageChange, handleInventoryClick, theme, t]);

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar 
        position="fixed"
        sx={{ 
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
          backdropFilter: 'blur(8px)'
        }}
        elevation={0}
      >
        <Toolbar>
          <IconButton
            color="primary"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Box sx={{ flexGrow: 1 }} />
          <Box sx={{ mr: 2 }}>
            <PremiumLanguageSwitcher />
          </Box>
          <Avatar sx={{ 
            bgcolor: theme.palette.primary.main,
            cursor: 'pointer',
            transition: '0.3s',
            '&:hover': {
              transform: 'scale(1.1)',
            }
          }}>
            {userRole ? userRole.charAt(0).toUpperCase() : 'U'}
          </Avatar>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: 'none',
              background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)'
            },
          }}
        >
          {drawer}
        </Drawer>

        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: 'none',
              background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          pt: 4,
          bgcolor: alpha(theme.palette.primary.main, 0.02),
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto',
          height: 'calc(100vh - 64px)',
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default HomePage;