import React, { useState, useEffect } from 'react';
import AutoEmailProcessor from '../components/AutoEmailProcessor';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  LinearProgress,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';

import {
  Upload as UploadIcon,
  Analytics as AnalyticsIcon,
  CheckCircle as CheckCircleIcon,
  Visibility as ViewIcon,
  ArrowBack as ArrowBackIcon,
  Assessment as AssessmentIcon,
  Email as EmailIcon
} from '@mui/icons-material';

// Landing page component for selecting procurement requests
const QuotationManagerLanding = () => {
  const [procurementRequests, setProcurementRequests] = useState([]);
  const [loading, setLoading] = useState(false);

  const categories = [
    { id: 'cement', name: 'Cement & Concrete', icon: '🏗️' },
    { id: 'steel', name: 'Steel & TMT Bars', icon: '🔩' },
    { id: 'pipes', name: 'Pipes & Fittings', icon: '🚰' },
    { id: 'electrical', name: 'Electrical Equipment', icon: '⚡' },
    { id: 'machinery', name: 'Machinery & Tools', icon: '🔧' },
    { id: 'aggregates', name: 'Aggregates & Sand', icon: '🪨' },
    { id: 'wood', name: 'Wood & Timber', icon: '🪵' }
  ];

  const urgencyLevels = [
    { value: 'low', label: 'Low (30+ days)', color: 'success' },
    { value: 'medium', label: 'Medium (15-30 days)', color: 'warning' },
    { value: 'high', label: 'High (7-15 days)', color: 'error' },
    { value: 'urgent', label: 'Urgent (< 7 days)', color: 'error' }
  ];

  const statusColors = {
    'draft': 'default',
    'rfq_sent': 'info',
    'quotations_received': 'warning',
    'analysis_complete': 'success',
    'pending_approval': 'warning',
    'approved': 'success',
    'po_generated': 'success',
    'completed': 'success'
  };

  // Load procurement requests on component mount
  useEffect(() => {
    loadProcurementRequests();
  }, []);

  const loadProcurementRequests = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5002/api/procurement/requests');
      const data = await response.json();
      setProcurementRequests(data.requests || []);
    } catch (error) {
      console.error('Error loading procurement requests:', error);
    }
    setLoading(false);
  };

  const handleSelectRequest = (requestId) => {
    window.location.href = `/app/quotation-manager?request_id=${requestId}`;
  };

  const handleBackToRequests = () => {
    window.location.href = '/app/procurement-system';
  };

  return (
    <Box
      sx={{
        background: `
          radial-gradient(ellipse at top left, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
          radial-gradient(ellipse at top right, rgba(240, 147, 251, 0.15) 0%, transparent 50%),
          radial-gradient(ellipse at bottom left, rgba(79, 172, 254, 0.15) 0%, transparent 50%),
          radial-gradient(ellipse at bottom right, rgba(245, 87, 108, 0.15) 0%, transparent 50%),
          linear-gradient(135deg,
            #0f172a 0%,
            #1e293b 25%,
            #334155 50%,
            #475569 75%,
            #64748b 100%
          )
        `,
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        minHeight: '100vh',
        p: 0,
        overflow: 'auto',
        position: 'relative',
      }}
    >
      <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 1 }}>
        {/* Premium Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.9) 50%,
                rgba(248, 250, 252, 0.95) 100%
              )
            `,
            backdropFilter: 'blur(20px) saturate(180%)',
            borderRadius: '24px',
            p: 4,
            mb: 4,
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: `
              0 25px 50px -12px rgba(0, 0, 0, 0.25),
              0 0 0 1px rgba(255, 255, 255, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.1)
            `,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '2px',
              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
            },
          }}
        >
          {/* Premium Header Section */}
          <Box sx={{ position: 'relative' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flex: 1, minWidth: 0 }}>
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: 4,
                    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 12px 40px rgba(59, 130, 246, 0.4)',
                    position: 'relative',
                    flexShrink: 0,
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      inset: 0,
                      borderRadius: 4,
                      padding: '2px',
                      background: 'linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4, #10b981)',
                      mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                      maskComposite: 'xor',
                    },
                  }}
                >
                  <Typography variant="h4" sx={{ color: 'white', fontSize: '1.8rem' }}>📤</Typography>
                </Box>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant="h3"
                    fontWeight={800}
                    sx={{
                      color: '#0f172a',
                      mb: 0.5,
                      fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem', lg: '2.6rem', xl: '3rem' },
                      letterSpacing: '-0.02em',
                      lineHeight: 1.1,
                      fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: 'none',
                    }}
                  >
                    Select Procurement Request
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: '#64748b',
                        fontWeight: 600,
                        fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem', lg: '1.2rem' },
                        letterSpacing: '0.01em',
                        lineHeight: 1.4,
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      }}
                    >
                      Choose a procurement request to manage quotations
                    </Typography>
                    <Chip
                      label="Step 2 of 2"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                        color: '#166534',
                        fontWeight: 700,
                        fontSize: '0.75rem',
                        border: '1px solid #4ade80',
                        '& .MuiChip-label': {
                          px: 1.5,
                        },
                      }}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                        boxShadow: '0 0 12px rgba(59, 130, 246, 0.5)',
                      }}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#3b82f6',
                        fontWeight: 600,
                        fontSize: '0.85rem',
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      }}
                    >
                      Upload • Analyze • Compare • Approve
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Back Button - Positioned on the right */}
              <Box sx={{ flexShrink: 0, ml: { xs: 2, md: 3 } }}>
                <Button
                  variant="outlined"
                  onClick={handleBackToRequests}
                  startIcon={<ArrowBackIcon />}
                  sx={{
                    borderRadius: '16px',
                    textTransform: 'none',
                    fontWeight: 700,
                    fontSize: { xs: '0.9rem', md: '1rem' },
                    px: { xs: 3, md: 4 },
                    py: 2,
                    border: '2px solid #e2e8f0',
                    color: '#475569',
                    whiteSpace: 'nowrap',
                    background: 'rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(10px)',
                    '&:hover': {
                      border: '2px solid #3b82f6',
                      color: '#3b82f6',
                      background: 'rgba(59, 130, 246, 0.05)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(59, 130, 246, 0.15)',
                    },
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                >
                  Back to Requests
                </Button>
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Premium Procurement Requests Table */}
        <Paper
          elevation={0}
          sx={{
            borderRadius: 4,
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px) saturate(180%)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: `
              0 20px 40px -12px rgba(0, 0, 0, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.1)
            `,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '3px',
              background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%)',
            },
          }}
        >
          <Box sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 8px 24px rgba(59, 130, 246, 0.3)',
                }}
              >
                <Typography variant="h5" sx={{ color: 'white' }}>📋</Typography>
              </Box>
              <Box>
                <Typography
                  variant="h5"
                  fontWeight={700}
                  sx={{
                    color: '#1e293b',
                    fontSize: '1.4rem',
                    letterSpacing: '-0.01em',
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                  }}
                >
                  Available Procurement Requests
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: '#64748b',
                    fontWeight: 500,
                    mt: 0.5,
                  }}
                >
                  Select a request to upload and manage vendor quotations
                </Typography>
              </Box>
            </Box>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 6 }}>
                <CircularProgress size={40} sx={{ color: '#3b82f6' }} />
              </Box>
            ) : procurementRequests.length === 0 ? (
              <Paper
                sx={{
                  p: 6,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                  border: '2px dashed #cbd5e1',
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3,
                  }}
                >
                  <Typography variant="h3" sx={{ color: '#6366f1' }}>📋</Typography>
                </Box>
                <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mb: 1 }}>
                  No Procurement Requests Found
                </Typography>
                <Typography variant="body1" sx={{ color: '#64748b', mb: 3, maxWidth: 400, mx: 'auto' }}>
                  Create a procurement request first to manage quotations and vendor responses.
                </Typography>
                <Button
                  variant="contained"
                  onClick={handleBackToRequests}
                  startIcon={<ArrowBackIcon />}
                  sx={{
                    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 3,
                    py: 1.5,
                  }}
                >
                  Create Request
                </Button>
              </Paper>
            ) : (
              <TableContainer
                component={Paper}
                sx={{
                  borderRadius: 3,
                  overflow: 'hidden',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
                }}
              >
                <Table>
                  <TableHead>
                    <TableRow sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      '& .MuiTableCell-head': {
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.9rem',
                        borderBottom: 'none',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                      }
                    }}>
                      <TableCell>Request ID</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Items</TableCell>
                      <TableCell>Urgency</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {procurementRequests.map((request, index) => (
                      <TableRow
                        key={request.id}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'rgba(59, 130, 246, 0.04)',
                          },
                          borderLeft: index % 2 === 0 ? '4px solid #3b82f6' : '4px solid #8b5cf6',
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                            <Typography variant="body2" fontFamily="monospace" fontWeight={700} sx={{ color: '#1e293b' }}>
                              {request.id.slice(0, 8)}...
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>
                              ID: {request.id.slice(-4)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={categories.find(c => c.id === request.category)?.name || request.category}
                            size="small"
                            sx={{
                              background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
                              color: '#1e40af',
                              fontWeight: 600,
                              border: '1px solid #93c5fd',
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
                              {request.items?.length || 0}
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#64748b' }}>
                              items
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={urgencyLevels.find(u => u.value === request.urgency)?.label || request.urgency}
                            size="small"
                            sx={{
                              fontWeight: 600,
                              ...(request.urgency === 'urgent' && {
                                background: 'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)',
                                color: '#dc2626',
                                border: '1px solid #f87171',
                              }),
                              ...(request.urgency === 'high' && {
                                background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                                color: '#d97706',
                                border: '1px solid #fbbf24',
                              }),
                              ...(request.urgency === 'medium' && {
                                background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
                                color: '#2563eb',
                                border: '1px solid #93c5fd',
                              }),
                              ...(request.urgency === 'low' && {
                                background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                                color: '#16a34a',
                                border: '1px solid #4ade80',
                              }),
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={request.status?.replace('_', ' ').toUpperCase() || 'DRAFT'}
                            size="small"
                            sx={{
                              fontWeight: 600,
                              ...(request.status === 'completed' && {
                                background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                                color: '#16a34a',
                                border: '1px solid #4ade80',
                              }),
                              ...(request.status === 'approved' && {
                                background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                                color: '#16a34a',
                                border: '1px solid #4ade80',
                              }),
                              ...(request.status === 'pending_approval' && {
                                background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                                color: '#d97706',
                                border: '1px solid #fbbf24',
                              }),
                              ...(request.status === 'rfq_sent' && {
                                background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
                                color: '#2563eb',
                                border: '1px solid #93c5fd',
                              }),
                              ...(!request.status && {
                                background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',
                                color: '#475569',
                                border: '1px solid #cbd5e1',
                              }),
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                            <Typography variant="body2" fontWeight={600} sx={{ color: '#1e293b' }}>
                              {new Date(request.created_at).toLocaleDateString()}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>
                              {new Date(request.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Button
                            onClick={() => handleSelectRequest(request.id)}
                            variant="contained"
                            size="small"
                            startIcon={<ViewIcon />}
                            sx={{
                              background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                              borderRadius: 2,
                              textTransform: 'none',
                              fontWeight: 600,
                              px: 2,
                              py: 1,
                              boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                              '&:hover': {
                                background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
                                boxShadow: '0 6px 16px rgba(59, 130, 246, 0.4)',
                                transform: 'translateY(-1px)',
                              },
                              transition: 'all 0.2s ease-in-out',
                            }}
                          >
                            Manage Quotations
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

const QuotationManager = () => {
  const [currentRequestId, setCurrentRequestId] = useState(null);
  const [procurementRequest, setProcurementRequest] = useState(null);
  const [quotations, setQuotations] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedVendor, setSelectedVendor] = useState('');
  const [analysisResults, setAnalysisResults] = useState(null);
  const [comparisonDialogOpen, setComparisonDialogOpen] = useState(false);
  const [autoEmailDialogOpen, setAutoEmailDialogOpen] = useState(false);

  // Bulk upload functionality (single upload removed)
  const [vendorFileGrid, setVendorFileGrid] = useState([]);
  const [bulkUploadResults, setBulkUploadResults] = useState(null);

  // Two-step approval process
  const [approvedQuotations, setApprovedQuotations] = useState(new Set());
  const [releasedPOs, setReleasedPOs] = useState(new Set());

  // Get request ID from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const requestId = urlParams.get('request_id');
    if (requestId) {
      setCurrentRequestId(requestId);
      loadProcurementRequest(requestId);
      loadQuotations(requestId);
    }
  }, []);

  // Load vendors when component mounts and when procurement request is loaded
  useEffect(() => {
    const loadVendors = async () => {
      try {
        let response, data;

        if (procurementRequest?.category) {
          // Load vendors that match the procurement request category
          console.log(`🔍 Loading vendors for category: ${procurementRequest.category}`);
          response = await fetch(`http://localhost:5002/api/procurement/vendors?category=${procurementRequest.category}`);
          data = await response.json();

          if (data.vendors && data.vendors.length > 0) {
            setVendors(data.vendors);
            console.log(`📊 Loaded ${data.vendors.length} vendors for category: ${procurementRequest.category}`);
            return;
          }
        }

        // Fallback: Load all vendors if category filtering fails or no category
        console.log('🔍 Loading all vendors as fallback');
        response = await fetch('http://localhost:5002/api/procurement/vendors');
        data = await response.json();
        setVendors(data.vendors || []);
        console.log(`📊 Loaded ${data.vendors?.length || 0} total vendors`);

      } catch (error) {
        console.error('Error loading vendors:', error);
        // Try to load all vendors as final fallback
        try {
          const response = await fetch('http://localhost:5002/api/procurement/vendors');
          const data = await response.json();
          setVendors(data.vendors || []);
          console.log(`📊 Fallback: Loaded ${data.vendors?.length || 0} vendors`);
        } catch (fallbackError) {
          console.error('Fallback vendor loading also failed:', fallbackError);
        }
      }
    };

    loadVendors();
  }, [procurementRequest]);

  // Also load vendors immediately when component mounts (for immediate availability)
  useEffect(() => {
    const loadAllVendors = async () => {
      try {
        console.log('🔍 Initial vendor loading...');
        const response = await fetch('http://localhost:5002/api/procurement/vendors');
        const data = await response.json();
        setVendors(data.vendors || []);
        console.log(`📊 Initial load: ${data.vendors?.length || 0} vendors available`);
      } catch (error) {
        console.error('Error in initial vendor loading:', error);
      }
    };

    loadAllVendors();
  }, []);

  // Initialize vendor grid when dialog opens
  useEffect(() => {
    if (uploadDialogOpen && vendors.length > 0) {
      console.log('🔄 Initializing vendor grid...', { vendors: vendors.length, quotations: quotations.length });
      initializeVendorGrid();
    }
  }, [uploadDialogOpen, vendors, quotations]);

  // Also initialize when vendors are loaded
  useEffect(() => {
    if (vendorFileGrid.length === 0 && vendors.length > 0 && uploadDialogOpen) {
      console.log('🔄 Auto-initializing vendor grid...');
      initializeVendorGrid();
    }
  }, [vendors, uploadDialogOpen]);

  const loadProcurementRequest = async (requestId) => {
    try {
      const response = await fetch(`http://localhost:5002/api/procurement/request/${requestId}`);
      const data = await response.json();
      setProcurementRequest(data.request);
    } catch (error) {
      console.error('Error loading procurement request:', error);
    }
  };

  const loadQuotations = async (requestId) => {
    try {
      const response = await fetch(`http://localhost:5002/api/procurement/quotations/${requestId}`);
      const data = await response.json();
      setQuotations(data.quotations || []);
    } catch (error) {
      console.error('Error loading quotations:', error);
    }
  };



  const handleBulkUpload = async () => {
    const validEntries = vendorFileGrid.filter(entry => entry.vendorId && entry.file);

    if (validEntries.length === 0 || !currentRequestId) {
      alert('❌ Please select at least one vendor-file pair and ensure you have an active request');
      return;
    }

    setLoading(true);
    setBulkUploadResults(null);

    try {
      const formData = new FormData();

      // Append all files and vendor IDs
      validEntries.forEach((entry) => {
        formData.append('files', entry.file);
        formData.append('vendor_ids', entry.vendorId);
      });

      formData.append('request_id', currentRequestId);

      const response = await fetch('http://localhost:5002/api/procurement/quotation/bulk-upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setBulkUploadResults(result);
        alert(`✅ Bulk upload completed!\n📊 ${result.successful_uploads} successful, ${result.failed_uploads} failed\n📋 Total quotations: ${result.total_quotations}`);
        // Refresh quotations list
        loadQuotations(currentRequestId);
        // Close the dialog
        resetUploadDialog();
      } else {
        alert(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      alert(`❌ Error uploading quotations: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const initializeVendorGrid = () => {
    const availableVendors = vendors.filter(vendor =>
      !quotations.some(q => q.vendor_id === vendor.id)
    );

    // Initialize with 3 empty rows, or number of available vendors if less, but always at least 1
    const initialRows = Math.max(1, Math.min(3, availableVendors.length || 1));
    const grid = Array.from({ length: initialRows }, (_, index) => ({
      id: Date.now() + index,
      vendorId: '',
      file: null,
      fileName: ''
    }));

    console.log('📋 Initializing vendor grid:', {
      availableVendors: availableVendors.length,
      initialRows,
      gridLength: grid.length
    });
    setVendorFileGrid(grid);
  };

  const addVendorRow = () => {
    const newRow = {
      id: Date.now(),
      vendorId: '',
      file: null,
      fileName: ''
    };
    setVendorFileGrid([...vendorFileGrid, newRow]);
  };

  const removeVendorRow = (id) => {
    if (vendorFileGrid.length > 1) {
      setVendorFileGrid(vendorFileGrid.filter(row => row.id !== id));
    }
  };

  const updateVendorRow = (id, field, value) => {
    console.log('🔄 Updating vendor row:', { id, field, value: field === 'file' ? value?.name : value });
    const newGrid = vendorFileGrid.map(row =>
      row.id === id ? { ...row, [field]: value } : row
    );
    setVendorFileGrid(newGrid);

    // Log the updated grid
    const validEntries = newGrid.filter(row => row.vendorId && row.file);
    console.log('📊 Grid updated:', {
      totalRows: newGrid.length,
      validEntries: validEntries.length
    });
  };

  const handleFileSelect = (id, file) => {
    console.log('📁 File selected:', { id, fileName: file?.name, fileSize: file?.size });

    // Update both file and fileName in a single state update
    setVendorFileGrid(prevGrid => {
      const newGrid = prevGrid.map(row =>
        row.id === id ? {
          ...row,
          file: file,
          fileName: file ? file.name : ''
        } : row
      );

      // Log immediately after state update
      const validEntries = newGrid.filter(row => row.vendorId && row.file);
      console.log('📊 File selection - Grid updated:', {
        totalRows: newGrid.length,
        validEntries: validEntries.length,
        updatedRow: newGrid.find(row => row.id === id),
        allRows: newGrid.map(row => ({
          id: row.id,
          vendorId: row.vendorId,
          hasFile: !!row.file,
          fileName: row.fileName
        }))
      });

      return newGrid;
    });
  };

  const resetUploadDialog = () => {
    setUploadDialogOpen(false);
    setVendorFileGrid([]);
    setBulkUploadResults(null);
  };

  const handleRunAnalysis = async () => {
    if (quotations.length === 0) {
      alert('❌ No quotations uploaded yet. Please upload quotations first.');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`http://localhost:5002/api/procurement/analyze/${currentRequestId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      const data = await response.json();
      setAnalysisResults(data);
      alert(`🤖 AI Analysis completed! Recommended: ${data.analysis_summary.recommended_vendor}`);
    } catch (error) {
      alert(`❌ Error running analysis: ${error.message}`);
    }
    setLoading(false);
  };

  const handleViewComparison = async () => {
    // If analysis results don't exist, run AI analysis first
    if (!analysisResults) {
      console.log('🤖 No analysis results found, running AI analysis first...');

      if (quotations.length < 2) {
        alert('❌ Need at least 2 quotations to run analysis');
        return;
      }

      setLoading(true);
      try {
        // Run AI analysis first
        await handleRunAnalysis();

        // After analysis completes, open the comparison dialog
        setComparisonDialogOpen(true);
      } catch (error) {
        console.error('❌ Error running analysis:', error);
        alert(`❌ Error running analysis: ${error.message}`);
      }
      setLoading(false);
    } else {
      // Analysis results already exist, just open the dialog
      setComparisonDialogOpen(true);
    }
  };

  const handleApproveQuotation = async (quotationId) => {
    if (!currentRequestId) {
      alert('❌ No active procurement request');
      return;
    }

    // Just mark as approved, don't generate PO yet
    setApprovedQuotations(prev => new Set([...prev, quotationId]));
    alert('✅ Quotation approved! You can now release the Purchase Order.');
  };

  const handleReleasePO = async (quotationId) => {
    if (!currentRequestId) {
      alert('❌ No active procurement request');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`http://localhost:5002/api/procurement/approve/${currentRequestId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quotation_id: quotationId,
          comments: 'Purchase Order released after approval'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setReleasedPOs(prev => new Set([...prev, quotationId]));
        alert(`✅ Purchase Order released successfully! PO Number: ${result.po_number}`);
      } else {
        alert(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      alert(`❌ Error releasing Purchase Order: ${error.message}`);
    }
    setLoading(false);
  };

  const handleBackToRequests = () => {
    window.location.href = '/app/procurement-system';
  };

  const forceReloadVendors = async () => {
    console.log('🔄 Force reloading vendors...');
    try {
      const response = await fetch('http://localhost:5002/api/procurement/vendors');
      const data = await response.json();
      setVendors(data.vendors || []);
      console.log(`✅ Force reload: ${data.vendors?.length || 0} vendors loaded`);
    } catch (error) {
      console.error('❌ Error force reloading vendors:', error);
    }
  };

  const handleOpenUploadDialog = () => {
    console.log(`🔍 Opening upload dialog. Current vendors: ${vendors.length}`);
    if (vendors.length === 0) {
      console.log('⚠️ No vendors loaded, force reloading...');
      forceReloadVendors();
    }
    setUploadDialogOpen(true);
  };

  // If no request ID, show request selection page
  if (!currentRequestId) {
    return <QuotationManagerLanding />;
  }

  return (
    <Box
      sx={{
        background: `
          radial-gradient(ellipse at top left, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
          radial-gradient(ellipse at top right, rgba(240, 147, 251, 0.15) 0%, transparent 50%),
          radial-gradient(ellipse at bottom left, rgba(79, 172, 254, 0.15) 0%, transparent 50%),
          radial-gradient(ellipse at bottom right, rgba(245, 87, 108, 0.15) 0%, transparent 50%),
          linear-gradient(135deg, 
            #0f172a 0%, 
            #1e293b 25%, 
            #334155 50%, 
            #475569 75%, 
            #64748b 100%
          )
        `,
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        minHeight: '100vh',
        p: 0,
        overflow: 'auto',
        position: 'relative',
      }}
    >
      <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 1 }}>
        {/* Premium Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.9) 50%,
                rgba(248, 250, 252, 0.95) 100%
              )
            `,
            backdropFilter: 'blur(20px) saturate(180%)',
            borderRadius: '24px',
            p: 4,
            mb: 4,
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: `
              0 25px 50px -12px rgba(0, 0, 0, 0.25),
              0 0 0 1px rgba(255, 255, 255, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.1)
            `,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '2px',
              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
            },
          }}
        >
          <Box sx={{ position: 'relative', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: 4,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 12px 40px rgba(102, 126, 234, 0.4)',
                    position: 'relative',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      inset: 0,
                      borderRadius: 4,
                      padding: '2px',
                      background: 'linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c)',
                      mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                      maskComposite: 'xor',
                    },
                  }}
                >
                  <Typography variant="h4" sx={{ color: 'white', fontSize: '1.8rem' }}>📊</Typography>
                </Box>
                <Box>
                  <Typography
                    variant="h3"
                    fontWeight={800}
                    sx={{
                      color: '#0f172a',
                      mb: 0.5,
                      fontSize: { xs: '1.8rem', md: '2.2rem', lg: '2.6rem', xl: '3rem' },
                      letterSpacing: '-0.02em',
                      lineHeight: 1.1,
                      fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: 'none',
                    }}
                  >
                    Quotation & Analysis Manager
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: '#64748b',
                        fontWeight: 600,
                        fontSize: { xs: '1rem', md: '1.1rem', lg: '1.2rem' },
                        letterSpacing: '0.01em',
                        lineHeight: 1.4,
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      }}
                    >
                      AI-Powered Vendor Comparison & Smart Procurement
                    </Typography>
                    <Chip
                      label="Step 2 of 2"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%)',
                        color: '#3730a3',
                        fontWeight: 700,
                        fontSize: '0.75rem',
                        border: '1px solid #a5b4fc',
                        '& .MuiChip-label': {
                          px: 1.5,
                        },
                      }}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                        boxShadow: '0 0 12px rgba(16, 185, 129, 0.5)',
                      }}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#059669',
                        fontWeight: 600,
                        fontSize: '0.85rem',
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      }}
                    >
                      Upload • Analyze • Compare • Approve
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Button
              variant="outlined"
              onClick={handleBackToRequests}
              startIcon={<ArrowBackIcon />}
              sx={{
                borderRadius: '12px',
                textTransform: 'none',
                fontWeight: 600,
                px: 3,
                py: 1.5,
              }}
            >
              Back to Requests
            </Button>
          </Box>
        </Paper>

        {loading && (
          <Paper sx={{ p: 2, mb: 4, borderRadius: 3 }}>
            <Typography variant="h6" gutterBottom>🔄 Processing...</Typography>
            <LinearProgress />
          </Paper>
        )}

        {/* Premium Request Info */}
        {procurementRequest && (
          <Paper
            elevation={0}
            sx={{
              mb: 4,
              borderRadius: 4,
              background: `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.9) 0%,
                  rgba(248, 250, 252, 0.8) 100%
                )
              `,
              backdropFilter: 'blur(20px) saturate(180%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `
                0 20px 40px -12px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1)
              `,
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '3px',
                background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%)',
              },
            }}
          >
            <Box sx={{ p: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: 3,
                    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 8px 24px rgba(59, 130, 246, 0.3)',
                  }}
                >
                  <Typography variant="h5" sx={{ color: 'white' }}>📋</Typography>
                </Box>
                <Box>
                  <Typography
                    variant="h5"
                    fontWeight={700}
                    sx={{
                      color: '#1e293b',
                      fontSize: '1.4rem',
                      letterSpacing: '-0.01em',
                      fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    }}
                  >
                    Procurement Request Details
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#64748b',
                      fontWeight: 500,
                      mt: 0.5,
                    }}
                  >
                    Active procurement request information
                  </Typography>
                </Box>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      background: 'rgba(59, 130, 246, 0.08)',
                      border: '1px solid rgba(59, 130, 246, 0.2)',
                    }}
                  >
                    <Typography variant="caption" color="primary.main" fontWeight={600} sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
                      Request ID
                    </Typography>
                    <Typography variant="h6" fontFamily="monospace" fontWeight={700} sx={{ color: '#1e293b', mt: 0.5 }}>
                      {currentRequestId?.slice(0, 12)}...
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      background: 'rgba(139, 92, 246, 0.08)',
                      border: '1px solid rgba(139, 92, 246, 0.2)',
                    }}
                  >
                    <Typography variant="caption" color="secondary.main" fontWeight={600} sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
                      Category
                    </Typography>
                    <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mt: 0.5, textTransform: 'capitalize' }}>
                      {procurementRequest.category}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      background: 'rgba(6, 182, 212, 0.08)',
                      border: '1px solid rgba(6, 182, 212, 0.2)',
                    }}
                  >
                    <Typography variant="caption" sx={{ color: '#0891b2' }} fontWeight={600} sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
                      Items Count
                    </Typography>
                    <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mt: 0.5 }}>
                      {procurementRequest.items?.length || 0} Items
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      background: 'rgba(16, 185, 129, 0.08)',
                      border: '1px solid rgba(16, 185, 129, 0.2)',
                    }}
                  >
                    <Typography variant="caption" color="success.main" fontWeight={600} sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
                      Specifications
                    </Typography>
                    <Typography variant="body1" fontWeight={500} sx={{ color: '#1e293b', mt: 1, lineHeight: 1.6 }}>
                      {procurementRequest.specifications || 'No specific requirements mentioned'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        <Grid container spacing={4}>
          {/* Left Column - Premium Upload Quotations */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                mb: 4,
                borderRadius: 4,
                background: `
                  linear-gradient(145deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(255, 248, 240, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: `
                  0 20px 40px -12px rgba(0, 0, 0, 0.15),
                  0 0 0 1px rgba(255, 255, 255, 0.05),
                  inset 0 1px 0 rgba(255, 255, 255, 0.1)
                `,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '3px',
                  background: 'linear-gradient(90deg, #f59e0b 0%, #f97316 50%, #ef4444 100%)',
                },
              }}
            >
              <Box sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: 3,
                        background: 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 8px 24px rgba(245, 158, 11, 0.3)',
                      }}
                    >
                      <Typography variant="h5" sx={{ color: 'white' }}>📤</Typography>
                    </Box>
                    <Box>
                      <Typography
                        variant="h5"
                        fontWeight={700}
                        sx={{
                          color: '#1e293b',
                          fontSize: '1.3rem',
                          letterSpacing: '-0.01em',
                          fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                        }}
                      >
                        Upload Quotations
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          fontWeight: 500,
                          mt: 0.5,
                        }}
                      >
                        Upload vendor quotations for AI analysis
                      </Typography>
                    </Box>
                  </Box>
                  {quotations.length > 0 && (
                    <Chip
                      label={`${quotations.length} uploaded`}
                      sx={{
                        background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                        color: '#166534',
                        fontWeight: 700,
                        border: '1px solid #22c55e',
                      }}
                    />
                  )}
                </Box>

                {/* Uploaded Quotations List */}
                {quotations.length > 0 ? (
                  <Paper sx={{ p: 2, mb: 2, bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
                    <Typography variant="subtitle2" gutterBottom color="success.dark">
                      ✅ Uploaded Quotations ({quotations.length}/{vendors.length})
                    </Typography>
                    {quotations.map((quotation, index) => (
                      <Box key={quotation.id} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Chip
                          label={`${index + 1}`}
                          size="small"
                          color="success"
                          sx={{ mr: 1, minWidth: 30 }}
                        />
                        <Typography variant="body2" sx={{ flexGrow: 1 }}>
                          {quotation.vendor_name}
                        </Typography>
                        <Typography variant="body2" color="success.dark" sx={{ fontWeight: 'bold' }}>
                          ₹{quotation.total_amount?.toLocaleString() || 'N/A'}
                        </Typography>
                      </Box>
                    ))}
                  </Paper>
                ) : (
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      📤 No quotations uploaded yet. Upload quotations from multiple vendors to compare and get the best deal.
                    </Typography>
                  </Alert>
                )}

                <Button
                  variant="contained"
                  onClick={() => setAutoEmailDialogOpen(true)}
                  startIcon={<EmailIcon />}
                  disabled={loading}
                  fullWidth
                  color="success"
                  sx={{ mb: 2 }}
                >
                  📧 Auto-Read Email Quotations
                </Button>

                <Button
                  variant="contained"
                  onClick={handleOpenUploadDialog}
                  startIcon={<UploadIcon />}
                  disabled={loading}
                  fullWidth
                  color="warning"
                  sx={{ mb: 2 }}
                >
                  📤 Upload Manually ({vendors.length} vendors available)
                </Button>

                {vendors.length === 0 && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    No vendors available. Please register vendors first in the Vendor Registration section.
                  </Alert>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Right Column - Premium AI Analysis */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                mb: 4,
                borderRadius: 4,
                background: `
                  linear-gradient(145deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(240, 253, 244, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: `
                  0 20px 40px -12px rgba(0, 0, 0, 0.15),
                  0 0 0 1px rgba(255, 255, 255, 0.05),
                  inset 0 1px 0 rgba(255, 255, 255, 0.1)
                `,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '3px',
                  background: 'linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%)',
                },
              }}
            >
              <Box sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 8px 24px rgba(16, 185, 129, 0.3)',
                    }}
                  >
                    <Typography variant="h5" sx={{ color: 'white' }}>🤖</Typography>
                  </Box>
                  <Box>
                    <Typography
                      variant="h5"
                      fontWeight={700}
                      sx={{
                        color: '#1e293b',
                        fontSize: '1.3rem',
                        letterSpacing: '-0.01em',
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      }}
                    >
                      AI Analysis Engine
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        fontWeight: 500,
                        mt: 0.5,
                      }}
                    >
                      Smart vendor comparison & recommendations
                    </Typography>
                  </Box>
                </Box>
                {quotations.length === 0 ? (
                  <Alert severity="info" sx={{ textAlign: 'center' }}>
                    <Typography variant="body2">
                      <strong>🤖 Step 2: AI Analysis</strong><br/>
                      Upload quotations from multiple vendors first, then run AI analysis to compare and get recommendations
                    </Typography>
                  </Alert>
                ) : quotations.length === 1 ? (
                  <Alert severity="warning" sx={{ textAlign: 'center' }}>
                    <Typography variant="body2">
                      <strong>📊 {quotations.length} quotation uploaded</strong><br/>
                      Upload more vendors for better comparison, or run analysis with current quotations
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={handleRunAnalysis}
                      startIcon={<AnalyticsIcon />}
                      disabled={loading}
                      color="success"
                      size="large"
                      sx={{ mt: 2, py: 1.5 }}
                    >
                      🤖 Run AI Analysis ({quotations.length} quotation)
                    </Button>
                  </Alert>
                ) : (
                  <>
                    <Alert severity="success" sx={{ mb: 2, textAlign: 'center' }}>
                      <Typography variant="body2">
                        <strong>✅ {quotations.length} quotations ready for comparison!</strong><br/>
                        Run AI analysis to compare vendors and get the best recommendation
                      </Typography>
                    </Alert>

                    <Button
                      variant="contained"
                      onClick={handleRunAnalysis}
                      startIcon={<AnalyticsIcon />}
                      disabled={loading}
                      fullWidth
                      color="success"
                      size="large"
                      sx={{ mb: 2, py: 1.5 }}
                    >
                      🤖 Run AI Analysis & Compare ({quotations.length} quotations)
                    </Button>

                    {quotations.length >= 2 && (
                      <Button
                        variant="outlined"
                        onClick={handleViewComparison}
                        startIcon={<AssessmentIcon />}
                        disabled={loading}
                        fullWidth
                        color="primary"
                      >
                        {analysisResults
                          ? '📊 View Detailed Comparison'
                          : '🤖 Run AI Analysis & View Report'
                        }
                      </Button>
                    )}
                  </>
                )}
              </Box>
            </Paper>


          </Grid>
        </Grid>

        {/* Horizontal Detailed Comparison Results */}
        {analysisResults && analysisResults.detailed_results && (
          <Paper
            elevation={0}
            sx={{
              mb: 4,
              borderRadius: 4,
              background: `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.95) 0%,
                  rgba(255, 251, 235, 0.9) 100%
                )
              `,
              backdropFilter: 'blur(20px) saturate(180%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `
                0 20px 40px -12px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1)
              `,
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '3px',
                background: 'linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%)',
              },
            }}
          >
            <Box sx={{ p: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 8px 24px rgba(245, 158, 11, 0.3)',
                    }}
                  >
                    <Typography variant="h5" sx={{ color: 'white' }}>🏆</Typography>
                  </Box>
                  <Box>
                    <Typography
                      variant="h4"
                      fontWeight={700}
                      sx={{
                        color: '#1e293b',
                        fontSize: '1.5rem',
                        letterSpacing: '-0.01em',
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      }}
                    >
                      AI Multi-Factor Vendor Analysis Results
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: '#64748b',
                        fontWeight: 500,
                        mt: 0.5,
                      }}
                    >
                      💰 Price + 🚚 Delivery + ⭐ Quality + 🛡️ Risk Analysis •
                      🏆 Recommended: {analysisResults.analysis_summary.recommended_vendor} •
                      Score: {(analysisResults.analysis_summary.top_score * 100).toFixed(1)}/100
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label="💡 You can approve any vendor below"
                  sx={{
                    background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                    color: '#166534',
                    fontWeight: 700,
                    border: '1px solid #22c55e',
                    fontSize: '0.8rem',
                    px: 2,
                  }}
                />
              </Box>

              {/* Premium Classic Comparison Table */}
              <TableContainer
                component={Paper}
                sx={{
                  borderRadius: 4,
                  overflow: 'hidden',
                  background: `
                    linear-gradient(135deg,
                      rgba(255, 255, 255, 0.98) 0%,
                      rgba(248, 250, 252, 0.95) 50%,
                      rgba(241, 245, 249, 0.98) 100%
                    )
                  `,
                  backdropFilter: 'blur(40px) saturate(200%)',
                  border: '2px solid rgba(148, 163, 184, 0.15)',
                  boxShadow: `
                    0 32px 64px -12px rgba(15, 23, 42, 0.15),
                    0 0 0 1px rgba(255, 255, 255, 0.8),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9)
                  `,
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '4px',
                    background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
                    zIndex: 1,
                  }
                }}
              >
                <Table sx={{ minWidth: 1200, width: '100%' }}>
                  <TableHead>
                    <TableRow sx={{
                      background: `
                        linear-gradient(135deg,
                          #667eea 0%,
                          #764ba2 25%,
                          #667eea 50%,
                          #764ba2 75%,
                          #667eea 100%
                        )
                      `,
                      position: 'relative',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: '2px',
                        background: 'linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0.3) 100%)',
                      },
                      '& .MuiTableCell-head': {
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.95rem',
                        borderBottom: 'none',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                        textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                        py: 2.5,
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          bottom: 0,
                          right: 0,
                          width: '1px',
                          background: 'rgba(255,255,255,0.2)',
                        },
                        '&:last-child::before': {
                          display: 'none',
                        }
                      }
                    }}>
                      <TableCell align="center">Rank</TableCell>
                      <TableCell>Vendor Details</TableCell>
                      <TableCell align="center">Total Amount</TableCell>
                      <TableCell align="center">Delivery Time</TableCell>
                      <TableCell align="center">Quality Rating</TableCell>
                      <TableCell align="center">Final Score</TableCell>
                      <TableCell align="center">Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analysisResults.detailed_results.map((result, index) => {
                      const quotation = quotations.find(q => q.id === result.quotation_id);
                      const isWinner = result.rank === 1;
                      const isSecond = result.rank === 2;

                      return (
                        <TableRow
                          key={result.quotation_id}
                          sx={{
                            background: isWinner
                              ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(34, 197, 94, 0.04) 100%)'
                              : isSecond
                              ? 'linear-gradient(135deg, rgba(251, 146, 60, 0.08) 0%, rgba(251, 146, 60, 0.04) 100%)'
                              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%)',
                            borderBottom: '1px solid rgba(148, 163, 184, 0.1)',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              background: isWinner
                                ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.08) 100%)'
                                : isSecond
                                ? 'linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(251, 146, 60, 0.08) 100%)'
                                : 'linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 100%)',
                              transform: 'translateY(-1px)',
                              boxShadow: '0 8px 25px -8px rgba(0, 0, 0, 0.1)',
                            },

                            '& .MuiTableCell-root': {
                              borderBottom: 'none',
                              py: 2.5,
                              fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                            }
                          }}
                        >
                          {/* Premium Rank Column */}
                          <TableCell align="center">
                            <Box sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              gap: 1,
                              minHeight: 80,
                              justifyContent: 'center',
                              width: '100%'
                            }}>
                              <Box
                                sx={{
                                  width: 48,
                                  height: 48,
                                  borderRadius: '50%',
                                  background: isWinner
                                    ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'
                                    : isSecond
                                    ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
                                    : 'linear-gradient(135deg, #64748b 0%, #475569 100%)',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'white',
                                  fontWeight: 700,
                                  fontSize: '1.1rem',
                                  boxShadow: isWinner
                                    ? '0 8px 25px rgba(34, 197, 94, 0.4)'
                                    : isSecond
                                    ? '0 8px 25px rgba(245, 158, 11, 0.4)'
                                    : '0 8px 25px rgba(100, 116, 139, 0.3)',
                                  border: '3px solid rgba(255, 255, 255, 0.9)',
                                  position: 'relative',
                                  '&::before': {
                                    content: '""',
                                    position: 'absolute',
                                    inset: -2,
                                    borderRadius: '50%',
                                    background: isWinner
                                      ? 'linear-gradient(135deg, #22c55e, #16a34a)'
                                      : isSecond
                                      ? 'linear-gradient(135deg, #f59e0b, #d97706)'
                                      : 'linear-gradient(135deg, #64748b, #475569)',
                                    zIndex: -1,
                                    opacity: 0.2,
                                  }
                                }}
                              >
                                #{result.rank}
                              </Box>
                              {isWinner && (
                                <Typography variant="caption" sx={{
                                  color: '#22c55e',
                                  fontWeight: 700,
                                  fontSize: '0.75rem',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                  textAlign: 'center'
                                }}>
                                  Best
                                </Typography>
                              )}
                            </Box>
                          </TableCell>

                          {/* Premium Vendor Details Column */}
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: 2,
                                  background: `linear-gradient(135deg,
                                    ${isWinner ? '#22c55e' : isSecond ? '#f59e0b' : '#64748b'} 0%,
                                    ${isWinner ? '#16a34a' : isSecond ? '#d97706' : '#475569'} 100%
                                  )`,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'white',
                                  fontWeight: 700,
                                  fontSize: '1.2rem',
                                  boxShadow: `0 4px 12px ${
                                    isWinner ? 'rgba(34, 197, 94, 0.3)' :
                                    isSecond ? 'rgba(245, 158, 11, 0.3)' :
                                    'rgba(100, 116, 139, 0.2)'
                                  }`,
                                }}
                              >
                                {(result.vendor_name || quotation?.vendor_name || 'U').charAt(0).toUpperCase()}
                              </Box>
                              <Box>
                                <Typography
                                  variant="subtitle1"
                                  fontWeight={700}
                                  sx={{
                                    color: '#1e293b',
                                    fontSize: '1rem',
                                    lineHeight: 1.2,
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                  }}
                                >
                                  {result.vendor_name || quotation?.vendor_name || 'Unknown Vendor'}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: '#64748b',
                                    fontWeight: 500,
                                    fontSize: '0.8rem',
                                    fontFamily: 'monospace',
                                    background: 'rgba(148, 163, 184, 0.1)',
                                    px: 1,
                                    py: 0.25,
                                    borderRadius: 1,
                                    display: 'inline-block',
                                  }}
                                >
                                  ID: {result.quotation_id.slice(0, 8)}...
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>

                          {/* Premium Total Amount Column */}
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                              <Typography
                                variant="h5"
                                fontWeight={700}
                                sx={{
                                  color: '#1e293b',
                                  fontSize: '1.25rem',
                                  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                  letterSpacing: '-0.01em',
                                }}
                              >
                                ₹{(result.total_amount || quotation?.total_amount || 0).toLocaleString()}
                              </Typography>
                              <Box
                                sx={{
                                  px: 2,
                                  py: 0.5,
                                  borderRadius: 2,
                                  background: (result.cost_score || 0) >= 0.8
                                    ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.1) 100%)'
                                    : (result.cost_score || 0) >= 0.6
                                    ? 'linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.1) 100%)'
                                    : 'linear-gradient(135deg, rgba(148, 163, 184, 0.15) 0%, rgba(148, 163, 184, 0.1) 100%)',
                                  border: `1px solid ${
                                    (result.cost_score || 0) >= 0.8 ? 'rgba(34, 197, 94, 0.3)' :
                                    (result.cost_score || 0) >= 0.6 ? 'rgba(245, 158, 11, 0.3)' :
                                    'rgba(148, 163, 184, 0.3)'
                                  }`,
                                }}
                              >
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: (result.cost_score || 0) >= 0.8 ? '#16a34a' :
                                           (result.cost_score || 0) >= 0.6 ? '#d97706' : '#475569',
                                    fontWeight: 700,
                                    fontSize: '0.75rem',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                  }}
                                >
                                  {((result.cost_score || 0) * 100).toFixed(0)}% Price Score
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>

                          {/* Delivery Time Column */}
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                              <Typography variant="body2" fontWeight={600}>
                                {(result.delivery_days || quotation?.delivery_days) ? `${result.delivery_days || quotation.delivery_days} days` : quotation?.delivery_time || 'Not specified'}
                              </Typography>
                              <Chip
                                label={`${((result.delivery_score || 0) * 100).toFixed(0)}% Delivery Score`}
                                size="small"
                                color={(result.delivery_score || 0) >= 0.8 ? 'success' : (result.delivery_score || 0) >= 0.6 ? 'warning' : 'default'}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {(result.delivery_score || 0) >= 0.8 ? '⚡ Fast' :
                                 (result.delivery_score || 0) >= 0.6 ? '⏱️ Standard' : '🐌 Slow'}
                              </Typography>
                            </Box>
                          </TableCell>

                          {/* Quality Rating Column */}
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                              <Typography variant="body2" fontWeight={600}>
                                {((result.quality_score || 0) * 100).toFixed(0)}%
                              </Typography>
                              <Chip
                                label={`Quality Score`}
                                size="small"
                                color={(result.quality_score || 0) >= 0.8 ? 'success' : (result.quality_score || 0) >= 0.6 ? 'warning' : 'default'}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {(result.quality_score || 0) >= 0.8 ? '⭐ Excellent' :
                                 (result.quality_score || 0) >= 0.6 ? '✅ Good' : '⚠️ Basic'}
                              </Typography>
                            </Box>
                          </TableCell>

                          {/* Final Score Column */}
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography
                                  variant="h5"
                                  fontWeight={700}
                                  color={isWinner ? 'success.main' : isSecond ? 'warning.main' : 'text.primary'}
                                >
                                  {((result.overall_score || 0) * 100).toFixed(1)}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={(result.overall_score || 0) * 100}
                                sx={{
                                  width: 80,
                                  height: 8,
                                  borderRadius: 4,
                                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    borderRadius: 4,
                                    background: isWinner ? 'linear-gradient(90deg, #4caf50, #66bb6a)' :
                                               isSecond ? 'linear-gradient(90deg, #ff9800, #ffb74d)' :
                                               'linear-gradient(90deg, #9e9e9e, #bdbdbd)',
                                  }
                                }}
                              />
                              <Typography variant="caption" color="text.secondary">
                                Risk: {((result.risk_score || 0) * 100).toFixed(0)}%
                              </Typography>
                            </Box>
                          </TableCell>

                          {/* Action Column - Approve Button for All Vendors */}
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                              {releasedPOs.has(result.quotation_id) ? (
                                // PO Already Released
                                <Button
                                  variant="contained"
                                  color="success"
                                  size="small"
                                  disabled
                                  startIcon={<CheckCircleIcon />}
                                  sx={{
                                    minWidth: 120,
                                    fontWeight: 600,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                                  }}
                                >
                                  PO Released
                                </Button>
                              ) : approvedQuotations.has(result.quotation_id) ? (
                                // Approved - Show Release PO Button
                                <Button
                                  variant="contained"
                                  color="warning"
                                  size="small"
                                  onClick={() => handleReleasePO(result.quotation_id)}
                                  startIcon={<CheckCircleIcon />}
                                  disabled={loading}
                                  sx={{
                                    minWidth: 120,
                                    fontWeight: 600,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
                                    boxShadow: '0 4px 12px rgba(255, 152, 0, 0.3)',
                                    '&:hover': {
                                      background: 'linear-gradient(135deg, #f57c00 0%, #ff9800 100%)',
                                      boxShadow: '0 6px 16px rgba(255, 152, 0, 0.4)',
                                    }
                                  }}
                                >
                                  Release PO
                                </Button>
                              ) : (
                                // Not Approved Yet - Show Approve Button
                                <Button
                                  variant={isWinner ? "contained" : "outlined"}
                                  color={isWinner ? "success" : "primary"}
                                  size="small"
                                  onClick={() => handleApproveQuotation(result.quotation_id)}
                                  startIcon={<CheckCircleIcon />}
                                  disabled={loading}
                                  sx={{
                                    minWidth: 120,
                                    fontWeight: 600,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    ...(isWinner && {
                                      background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                                      boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                                      '&:hover': {
                                        background: 'linear-gradient(135deg, #43a047 0%, #5cb85c 100%)',
                                        boxShadow: '0 6px 16px rgba(76, 175, 80, 0.4)',
                                      }
                                    }),
                                    ...(!isWinner && {
                                      borderColor: 'primary.main',
                                      '&:hover': {
                                        background: 'rgba(25, 118, 210, 0.08)',
                                        borderColor: 'primary.dark',
                                      }
                                    })
                                  }}
                                >
                                  {isWinner ? 'Approve Best' : 'Approve'}
                                </Button>
                              )}




                            </Box>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* AI Multi-Factor Analysis Breakdown */}
              <Paper sx={{ p: 3, mt: 3, borderRadius: 3, background: 'linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%)', border: '1px solid #0ea5e9' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 8px 24px rgba(14, 165, 233, 0.3)',
                    }}
                  >
                    <Typography variant="h5" sx={{ color: 'white' }}>🧠</Typography>
                  </Box>
                  <Box>
                    <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
                      AI Multi-Factor Analysis Breakdown
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      How the AI considers multiple factors beyond just price
                    </Typography>
                  </Box>
                </Box>

                <Grid container spacing={3}>
                  {analysisResults.detailed_results.map((result, index) => {
                    const quotation = quotations.find(q => q.id === result.quotation_id);
                    const isWinner = result.rank === 1;

                    return (
                      <Grid item xs={12} md={6} lg={4} key={result.quotation_id}>
                        <Paper
                          sx={{
                            p: 3,
                            borderRadius: 3,
                            background: isWinner ? 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)' : 'rgba(255, 255, 255, 0.8)',
                            border: isWinner ? '2px solid #22c55e' : '1px solid #e2e8f0',
                            position: 'relative',
                            overflow: 'hidden',
                          }}
                        >
                          {isWinner && (
                            <Box
                              sx={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                                color: 'white',
                                px: 2,
                                py: 0.5,
                                borderBottomLeftRadius: 2,
                                fontSize: '0.75rem',
                                fontWeight: 700,
                              }}
                            >
                              🏆 BEST
                            </Box>
                          )}

                          <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#1e293b' }}>
                              #{result.rank} {quotation?.vendor_name || 'Unknown Vendor'}
                            </Typography>
                            <Typography variant="h5" fontWeight={700} color={isWinner ? 'success.main' : 'text.primary'}>
                              {((result.overall_score || 0) * 100).toFixed(1)}% Overall
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                            {/* Cost Factor */}
                            <Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="body2" fontWeight={600} sx={{ color: '#374151' }}>
                                  💰 Cost (35% weight)
                                </Typography>
                                <Typography variant="body2" fontWeight={700} color={(result.cost_score || 0) >= 0.8 ? 'success.main' : (result.cost_score || 0) >= 0.6 ? 'warning.main' : 'text.secondary'}>
                                  {((result.cost_score || 0) * 100).toFixed(0)}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={(result.cost_score || 0) * 100}
                                sx={{
                                  height: 6,
                                  borderRadius: 3,
                                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    background: (result.cost_score || 0) >= 0.8 ? 'linear-gradient(90deg, #22c55e, #16a34a)' :
                                               (result.cost_score || 0) >= 0.6 ? 'linear-gradient(90deg, #f59e0b, #d97706)' :
                                               'linear-gradient(90deg, #ef4444, #dc2626)',
                                  }
                                }}
                              />
                            </Box>

                            {/* Delivery Factor */}
                            <Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="body2" fontWeight={600} sx={{ color: '#374151' }}>
                                  🚚 Delivery (25% weight)
                                </Typography>
                                <Typography variant="body2" fontWeight={700} color={(result.delivery_score || 0) >= 0.8 ? 'success.main' : (result.delivery_score || 0) >= 0.6 ? 'warning.main' : 'text.secondary'}>
                                  {((result.delivery_score || 0) * 100).toFixed(0)}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={(result.delivery_score || 0) * 100}
                                sx={{
                                  height: 6,
                                  borderRadius: 3,
                                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    background: (result.delivery_score || 0) >= 0.8 ? 'linear-gradient(90deg, #22c55e, #16a34a)' :
                                               (result.delivery_score || 0) >= 0.6 ? 'linear-gradient(90deg, #f59e0b, #d97706)' :
                                               'linear-gradient(90deg, #ef4444, #dc2626)',
                                  }
                                }}
                              />
                            </Box>

                            {/* Quality Factor */}
                            <Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="body2" fontWeight={600} sx={{ color: '#374151' }}>
                                  ⭐ Quality (20% weight)
                                </Typography>
                                <Typography variant="body2" fontWeight={700} color={(result.quality_score || 0) >= 0.8 ? 'success.main' : (result.quality_score || 0) >= 0.6 ? 'warning.main' : 'text.secondary'}>
                                  {((result.quality_score || 0) * 100).toFixed(0)}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={(result.quality_score || 0) * 100}
                                sx={{
                                  height: 6,
                                  borderRadius: 3,
                                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    background: (result.quality_score || 0) >= 0.8 ? 'linear-gradient(90deg, #22c55e, #16a34a)' :
                                               (result.quality_score || 0) >= 0.6 ? 'linear-gradient(90deg, #f59e0b, #d97706)' :
                                               'linear-gradient(90deg, #ef4444, #dc2626)',
                                  }
                                }}
                              />
                            </Box>

                            {/* Risk Factor */}
                            <Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="body2" fontWeight={600} sx={{ color: '#374151' }}>
                                  🛡️ Risk (15% weight)
                                </Typography>
                                <Typography variant="body2" fontWeight={700} color={(result.risk_score || 0) >= 0.8 ? 'success.main' : (result.risk_score || 0) >= 0.6 ? 'warning.main' : 'text.secondary'}>
                                  {((result.risk_score || 0) * 100).toFixed(0)}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={(result.risk_score || 0) * 100}
                                sx={{
                                  height: 6,
                                  borderRadius: 3,
                                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    background: (result.risk_score || 0) >= 0.8 ? 'linear-gradient(90deg, #22c55e, #16a34a)' :
                                               (result.risk_score || 0) >= 0.6 ? 'linear-gradient(90deg, #f59e0b, #d97706)' :
                                               'linear-gradient(90deg, #ef4444, #dc2626)',
                                  }
                                }}
                              />
                            </Box>
                          </Box>
                        </Paper>
                      </Grid>
                    );
                  })}
                </Grid>
              </Paper>

              {/* Quick Comparison Summary */}
              <Paper sx={{ p: 3, mt: 3, borderRadius: 3, background: 'linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%)' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    📊 Multi-Factor Comparison Summary
                  </Typography>
                  <Chip
                    label="💡 AI considers Price + Delivery + Quality + Risk - not just lowest price"
                    size="small"
                    sx={{
                      background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                      color: '#1976d2',
                      fontWeight: 600,
                      border: '1px solid #2196f3',
                    }}
                  />
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(76, 175, 80, 0.1)' }}>
                      <Typography variant="subtitle2" color="success.main" fontWeight={600}>
                        💰 Best Price
                      </Typography>
                      <Typography variant="h6" fontWeight={700}>
                        ₹{Math.min(...quotations.map(q => q.total_amount || Infinity)).toLocaleString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {quotations.find(q => q.total_amount === Math.min(...quotations.map(q => q.total_amount || Infinity)))?.vendor_name}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(33, 150, 243, 0.1)' }}>
                      <Typography variant="subtitle2" color="primary.main" fontWeight={600}>
                        🏆 AI Recommended
                      </Typography>
                      <Typography variant="h6" fontWeight={700}>
                        {analysisResults.analysis_summary.recommended_vendor}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {(analysisResults.analysis_summary.top_score * 100).toFixed(1)}% Overall Score
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(255, 152, 0, 0.1)' }}>
                      <Typography variant="subtitle2" color="warning.main" fontWeight={600}>
                        🚚 Fastest Delivery
                      </Typography>
                      <Typography variant="h6" fontWeight={700}>
                        {Math.min(...quotations.map(q => q.delivery_days || Infinity))} days
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {quotations.find(q => q.delivery_days === Math.min(...quotations.map(q => q.delivery_days || Infinity)))?.vendor_name || 'N/A'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(156, 39, 176, 0.1)' }}>
                      <Typography variant="subtitle2" color="secondary.main" fontWeight={600}>
                        ⭐ Highest Quality
                      </Typography>
                      <Typography variant="h6" fontWeight={700}>
                        {Math.max(...analysisResults.detailed_results.map(r => (r.quality_score || 0) * 100)).toFixed(0)}%
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Quality Score
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* AI Analysis Explanation */}
                <Box sx={{ mt: 3, p: 3, borderRadius: 2, background: 'rgba(59, 130, 246, 0.05)', border: '1px solid rgba(59, 130, 246, 0.2)' }}>
                  <Typography variant="subtitle2" fontWeight={700} sx={{ color: '#1e40af', mb: 1 }}>
                    🧠 How AI Analysis Works:
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b', lineHeight: 1.6 }}>
                    The AI doesn't just pick the cheapest option. It weighs multiple factors: <strong>Cost (35%)</strong> for budget efficiency,
                    <strong> Delivery (25%)</strong> for timeline compliance, <strong>Quality (20%)</strong> for vendor reliability,
                    and <strong>Risk (15%)</strong> for project safety. This ensures you get the best overall value, not just the lowest price.
                  </Typography>
                </Box>
              </Paper>
            </Box>
          </Paper>
        )}

        {/* Premium Bulk Upload Dialog */}
        <Dialog
          open={uploadDialogOpen}
          onClose={resetUploadDialog}
          maxWidth="xl"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.95) 0%,
                  rgba(248, 250, 252, 0.9) 100%
                )
              `,
              backdropFilter: 'blur(20px) saturate(180%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05)
              `,
            }
          }}
        >
          <DialogTitle sx={{ pb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
              <Box
                sx={{
                  width: 56,
                  height: 56,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 8px 24px rgba(59, 130, 246, 0.3)',
                }}
              >
                <Typography variant="h4" sx={{ color: 'white', fontSize: '1.5rem' }}>📤</Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="h4"
                  fontWeight={700}
                  sx={{
                    color: '#1e293b',
                    fontSize: '1.5rem',
                    letterSpacing: '-0.01em',
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    mb: 0.5,
                  }}
                >
                  Bulk Upload Vendor Quotations
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#64748b',
                      fontWeight: 500,
                      fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    }}
                  >
                    Upload multiple vendor quotations simultaneously for AI analysis
                  </Typography>
                  {quotations.length > 0 && (
                    <Chip
                      label={`${quotations.length} already uploaded`}
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                        color: '#166534',
                        fontWeight: 600,
                        border: '1px solid #4ade80',
                      }}
                    />
                  )}
                </Box>
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ p: 4 }}>
            <Grid container spacing={4} sx={{ mt: 0 }}>
              {/* Show already uploaded vendors */}
              {quotations.length > 0 && (
                <Grid item xs={12}>
                  <Paper
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                      border: '1px solid #4ade80',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Box
                        sx={{
                          width: 32,
                          height: 32,
                          borderRadius: 2,
                          background: 'linear-gradient(135deg, #16a34a 0%, #15803d 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="body1" sx={{ color: 'white' }}>✅</Typography>
                      </Box>
                      <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#166534' }}>
                        Already Uploaded Quotations
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {quotations.map((q) => (
                        <Chip
                          key={q.id}
                          label={q.vendor_name}
                          size="small"
                          sx={{
                            background: 'rgba(255, 255, 255, 0.8)',
                            color: '#166534',
                            fontWeight: 600,
                            border: '1px solid rgba(22, 101, 52, 0.2)',
                          }}
                        />
                      ))}
                    </Box>
                  </Paper>
                </Grid>
              )}

              {/* Premium Bulk Upload Grid Interface */}
              <Grid item xs={12}>
                <Paper
                  sx={{
                    p: 3,
                    borderRadius: 3,
                    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                    border: '1px solid #cbd5e1',
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 2,
                          background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h6" sx={{ color: 'white' }}>📋</Typography>
                      </Box>
                      <Box>
                        <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
                          Vendor-File Upload Grid
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#64748b' }}>
                          Select vendors and upload their quotation files
                        </Typography>
                      </Box>
                    </Box>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={addVendorRow}
                      startIcon={<span>➕</span>}
                      sx={{
                        background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                        borderRadius: 2,
                        textTransform: 'none',
                        fontWeight: 600,
                        px: 3,
                        py: 1,
                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
                          boxShadow: '0 6px 16px rgba(59, 130, 246, 0.4)',
                          transform: 'translateY(-1px)',
                        },
                        transition: 'all 0.2s ease-in-out',
                      }}
                    >
                      Add Row
                    </Button>
                  </Box>

                  <Paper
                    sx={{
                      p: 3,
                      maxHeight: 500,
                      overflow: 'auto',
                      borderRadius: 3,
                      background: 'rgba(255, 255, 255, 0.8)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
                    }}
                  >
                    {/* Premium Grid Header */}
                    <Grid container spacing={3} sx={{ mb: 3 }}>
                      <Grid item xs={4}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: 1,
                              background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Typography variant="caption" sx={{ color: 'white', fontWeight: 700 }}>V</Typography>
                          </Box>
                          <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#1e293b' }}>
                            Vendor
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: 1,
                              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Typography variant="caption" sx={{ color: 'white', fontWeight: 700 }}>F</Typography>
                          </Box>
                          <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#1e293b' }}>
                            Quotation File
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={2}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: 1,
                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Typography variant="caption" sx={{ color: 'white', fontWeight: 700 }}>A</Typography>
                          </Box>
                          <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#1e293b' }}>
                            Action
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>

                    {/* Premium Grid Rows */}
                    {vendorFileGrid.map((row, index) => (
                      <Paper
                        key={row.id}
                        sx={{
                          p: 2,
                          mb: 2,
                          borderRadius: 2,
                          background: index % 2 === 0
                            ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%)'
                            : 'linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(59, 130, 246, 0.02) 100%)',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                          '&:hover': {
                            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08)',
                            transform: 'translateY(-1px)',
                          },
                          transition: 'all 0.2s ease-in-out',
                        }}
                      >
                        <Grid container spacing={3} sx={{ alignItems: 'center' }}>
                          <Grid item xs={4}>
                            <FormControl
                              fullWidth
                              size="small"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: 2,
                                  background: 'rgba(255, 255, 255, 0.8)',
                                  backdropFilter: 'blur(10px)',
                                  '&:hover': {
                                    background: 'rgba(255, 255, 255, 0.9)',
                                  },
                                  '&.Mui-focused': {
                                    background: 'rgba(255, 255, 255, 1)',
                                    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
                                  },
                                },
                                '& .MuiInputLabel-root': {
                                  fontWeight: 600,
                                  color: '#64748b',
                                  '&.Mui-focused': {
                                    color: '#3b82f6',
                                  },
                                },
                              }}
                            >
                              <InputLabel>Select Vendor</InputLabel>
                              <Select
                                value={row.vendorId}
                                onChange={(e) => {
                                  const vendorId = e.target.value;
                                  console.log('👤 Vendor selected:', { rowId: row.id, vendorId });

                                  setVendorFileGrid(prevGrid => {
                                    const newGrid = prevGrid.map(gridRow =>
                                      gridRow.id === row.id ? { ...gridRow, vendorId } : gridRow
                                    );

                                    const validEntries = newGrid.filter(gridRow => gridRow.vendorId && gridRow.file);
                                    console.log('📊 Vendor selection - Grid updated:', {
                                      totalRows: newGrid.length,
                                      validEntries: validEntries.length,
                                      updatedRow: newGrid.find(gridRow => gridRow.id === row.id),
                                    });

                                    return newGrid;
                                  });
                                }}
                                MenuProps={{
                                  PaperProps: {
                                    sx: {
                                      borderRadius: 2,
                                      mt: 1,
                                      boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.25)',
                                      border: '1px solid rgba(255, 255, 255, 0.2)',
                                      background: 'rgba(255, 255, 255, 0.95)',
                                      backdropFilter: 'blur(20px)',
                                    }
                                  }
                                }}
                              >
                                {vendors.map((vendor) => {
                                  const alreadyUploaded = quotations.some(q => q.vendor_id === vendor.id);
                                  const alreadySelectedInGrid = vendorFileGrid.some(gridRow =>
                                    gridRow.id !== row.id && gridRow.vendorId === vendor.id
                                  );
                                  const isDisabled = alreadyUploaded || alreadySelectedInGrid;

                                  return (
                                    <MenuItem key={vendor.id} value={vendor.id} disabled={isDisabled}>
                                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.5 }}>
                                        <Box sx={{ flex: 1, minWidth: 0 }}>
                                          <Typography
                                            variant="body2"
                                            fontWeight={600}
                                            sx={{
                                              color: isDisabled ? '#94a3b8' : '#1e293b',
                                              fontSize: '0.9rem',
                                              mb: 0.25,
                                            }}
                                          >
                                            {vendor.name}
                                          </Typography>
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: isDisabled ? '#cbd5e1' : '#64748b',
                                              fontSize: '0.75rem',
                                            }}
                                          >
                                            {vendor.email}
                                          </Typography>
                                        </Box>
                                        {alreadyUploaded && (
                                          <Chip
                                            label="✅ Uploaded"
                                            size="small"
                                            sx={{
                                              background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                                              color: '#166534',
                                              fontWeight: 600,
                                              fontSize: '0.7rem',
                                              height: 20,
                                            }}
                                          />
                                        )}
                                        {alreadySelectedInGrid && (
                                          <Chip
                                            label="⚠️ Selected"
                                            size="small"
                                            sx={{
                                              background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                                              color: '#d97706',
                                              fontWeight: 600,
                                              fontSize: '0.7rem',
                                              height: 20,
                                            }}
                                          />
                                        )}
                                      </Box>
                                    </MenuItem>
                                  );
                                })}
                              </Select>
                            </FormControl>
                          </Grid>

                          <Grid item xs={6}>
                            <input
                              type="file"
                              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                              onChange={(e) => handleFileSelect(row.id, e.target.files[0])}
                              style={{ display: 'none' }}
                              id={`file-input-${row.id}`}
                            />
                            <label htmlFor={`file-input-${row.id}`}>
                              <Button
                                variant="outlined"
                                component="span"
                                startIcon={<UploadIcon />}
                                fullWidth
                                size="small"
                                sx={{
                                  justifyContent: 'flex-start',
                                  textTransform: 'none',
                                  borderRadius: 2,
                                  py: 1.5,
                                  background: row.file
                                    ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(21, 128, 61, 0.1) 100%)'
                                    : 'rgba(255, 255, 255, 0.8)',
                                  backdropFilter: 'blur(10px)',
                                  border: row.file
                                    ? '2px solid #22c55e'
                                    : '2px solid #e2e8f0',
                                  color: row.file ? '#166534' : '#64748b',
                                  fontWeight: 600,
                                  '&:hover': {
                                    background: row.file
                                      ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(21, 128, 61, 0.15) 100%)'
                                      : 'rgba(255, 255, 255, 0.9)',
                                    border: row.file
                                      ? '2px solid #16a34a'
                                      : '2px solid #3b82f6',
                                    transform: 'translateY(-1px)',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                                  },
                                  transition: 'all 0.2s ease-in-out',
                                }}
                              >
                                {row.fileName || 'Choose File...'}
                              </Button>
                            </label>
                          </Grid>

                          <Grid item xs={2}>
                            <Button
                              size="small"
                              onClick={() => removeVendorRow(row.id)}
                              disabled={vendorFileGrid.length === 1}
                              sx={{
                                minWidth: 40,
                                width: 40,
                                height: 40,
                                borderRadius: 2,
                                background: vendorFileGrid.length === 1
                                  ? 'rgba(148, 163, 184, 0.1)'
                                  : 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%)',
                                border: vendorFileGrid.length === 1
                                  ? '2px solid #cbd5e1'
                                  : '2px solid #ef4444',
                                color: vendorFileGrid.length === 1 ? '#94a3b8' : '#dc2626',
                                '&:hover': {
                                  background: vendorFileGrid.length === 1
                                    ? 'rgba(148, 163, 184, 0.1)'
                                    : 'linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%)',
                                  transform: vendorFileGrid.length === 1 ? 'none' : 'translateY(-1px)',
                                  boxShadow: vendorFileGrid.length === 1 ? 'none' : '0 4px 12px rgba(239, 68, 68, 0.2)',
                                },
                                transition: 'all 0.2s ease-in-out',
                              }}
                            >
                              🗑️
                            </Button>
                          </Grid>
                        </Grid>
                      </Paper>
                    ))}
                  </Paper>

                </Paper>
              </Grid>

              {/* Premium Upload Summary */}
              <Grid item xs={12}>
                <Paper
                  sx={{
                    p: 3,
                    borderRadius: 3,
                    background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
                    border: '1px solid #93c5fd',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: 2,
                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="body1" sx={{ color: 'white' }}>📊</Typography>
                    </Box>
                    <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#1e40af' }}>
                      Upload Summary
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h4" fontWeight={700} sx={{ color: '#1e40af' }}>
                        {vendorFileGrid.filter(row => row.vendorId && row.file).length}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#1e40af' }}>
                        quotations ready
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h6" fontWeight={600} sx={{ color: '#64748b' }}>
                        {vendorFileGrid.length}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        total rows
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h6" fontWeight={600} sx={{ color: '#64748b' }}>
                        {vendors.length}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        vendors available
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>

              {/* Premium Bulk Upload Results */}
              {bulkUploadResults && (
                <Grid item xs={12}>
                  <Paper
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
                      border: '1px solid #4ade80',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 2,
                          background: 'linear-gradient(135deg, #16a34a 0%, #15803d 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h6" sx={{ color: 'white' }}>📊</Typography>
                      </Box>
                      <Typography variant="h6" fontWeight={700} sx={{ color: '#166534' }}>
                        Bulk Upload Results
                      </Typography>
                    </Box>
                    <Grid container spacing={3} sx={{ mb: 2 }}>
                      <Grid item xs={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h3" fontWeight={700} sx={{ color: '#16a34a' }}>
                            {bulkUploadResults.successful_uploads}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#166534' }}>
                            ✅ Successful
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h3" fontWeight={700} sx={{ color: '#dc2626' }}>
                            {bulkUploadResults.failed_uploads}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#166534' }}>
                            ❌ Failed
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h3" fontWeight={700} sx={{ color: '#d97706' }}>
                            {bulkUploadResults.skipped_uploads}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#166534' }}>
                            ⏭️ Skipped
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                    {bulkUploadResults.results && (
                      <Box sx={{ mt: 2 }}>
                        {bulkUploadResults.results.map((result, index) => (
                          <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                            <Typography variant="body1">
                              {result.status === 'success' ? '✅' : result.status === 'failed' ? '❌' : '⏭️'}
                            </Typography>
                            <Typography variant="body2" fontWeight={600} sx={{ color: '#166534' }}>
                              {result.vendor_name}:
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#166534' }}>
                              {result.message}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    )}
                  </Paper>
                </Grid>
              )}

              {/* Premium Upload Tips */}
              <Grid item xs={12}>
                <Paper
                  sx={{
                    p: 3,
                    borderRadius: 3,
                    background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                    border: '1px solid #fbbf24',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: 2,
                        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="body1" sx={{ color: 'white' }}>💡</Typography>
                    </Box>
                    <Typography variant="subtitle1" fontWeight={700} sx={{ color: '#d97706' }}>
                      Upload Tips & Guidelines
                    </Typography>
                  </Box>
                  <Box sx={{ pl: 1 }}>
                    <Typography variant="body2" sx={{ color: '#d97706', mb: 1, fontWeight: 500 }}>
                      • Use the grid to select vendor and file for each row
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#d97706', mb: 1, fontWeight: 500 }}>
                      • Click "Add Row" to add more vendor-file pairs
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#d97706', mb: 1, fontWeight: 500 }}>
                      • Each vendor can have only one quotation per request
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#d97706', mb: 1, fontWeight: 500 }}>
                      • Upload all quotations with a single click
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#d97706', mb: 1, fontWeight: 500 }}>
                      • AI will process all files automatically
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#d97706', fontWeight: 500 }}>
                      • Supported formats: PDF, JPG, PNG, DOC, DOCX
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ p: 3, gap: 2 }}>
            <Button
              onClick={resetUploadDialog}
              variant="outlined"
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600,
                px: 3,
                py: 1.5,
                border: '2px solid #e2e8f0',
                color: '#475569',
                '&:hover': {
                  border: '2px solid #cbd5e1',
                  background: 'rgba(148, 163, 184, 0.05)',
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                const validEntries = vendorFileGrid.filter(row => row.vendorId && row.file);
                console.log('🔍 Bulk upload button clicked:', {
                  vendorFileGrid: vendorFileGrid.length,
                  validEntries: validEntries.length,
                  loading,
                  gridData: vendorFileGrid.map(row => ({
                    id: row.id,
                    vendorId: row.vendorId,
                    hasFile: !!row.file,
                    fileName: row.fileName
                  }))
                });
                handleBulkUpload();
              }}
              variant="contained"
              disabled={loading || vendorFileGrid.filter(row => row.vendorId && row.file).length === 0}
              startIcon={<UploadIcon />}
              sx={{
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 700,
                fontSize: '1rem',
                px: 4,
                py: 1.5,
                boxShadow: '0 8px 25px rgba(245, 158, 11, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #d97706 0%, #b45309 100%)',
                  boxShadow: '0 12px 35px rgba(245, 158, 11, 0.4)',
                  transform: 'translateY(-2px)',
                },
                '&:disabled': {
                  background: 'linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)',
                  color: '#94a3b8',
                  boxShadow: 'none',
                  transform: 'none',
                },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              }}
            >
              📤 Bulk Upload & Process ({vendorFileGrid.filter(row => row.vendorId && row.file).length} files)
            </Button>
          </DialogActions>
        </Dialog>

        {/* Comparison Dialog */}
        <Dialog open={comparisonDialogOpen} onClose={() => setComparisonDialogOpen(false)} maxWidth="lg" fullWidth>
          <DialogTitle>
            📊 AI Vendor Comparison & Analysis
          </DialogTitle>
          <DialogContent>
            {analysisResults && analysisResults.detailed_results && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom>
                  🏆 AI Analysis Results
                </Typography>

                {/* Analysis Summary */}
                {analysisResults.analysis_summary && (
                  <Alert severity="info" sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      📊 Analysis Summary
                    </Typography>
                    <Typography variant="body2">
                      <strong>Total Quotations:</strong> {analysisResults.analysis_summary.total_quotations}<br/>
                      <strong>Recommended Vendor:</strong> {analysisResults.analysis_summary.recommended_vendor}<br/>
                      <strong>Confidence Level:</strong> {analysisResults.analysis_summary.confidence_level}<br/>
                      <strong>Top Score:</strong> {(analysisResults.analysis_summary.top_score * 100).toFixed(1)}%
                    </Typography>
                  </Alert>
                )}

                {/* Enhanced Comparison Table with Actual Values */}
                <TableContainer
                  component={Paper}
                  sx={{
                    mb: 3,
                    borderRadius: 3,
                    overflow: 'hidden',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
                  }}
                >
                  <Table sx={{ minWidth: 1200, width: '100%' }}>
                    <TableHead>
                      <TableRow sx={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        '& .MuiTableCell-head': {
                          color: 'white',
                          fontWeight: 700,
                          fontSize: '0.9rem',
                          borderBottom: 'none',
                        }
                      }}>
                        <TableCell align="center">Rank</TableCell>
                        <TableCell>Vendor Details</TableCell>
                        <TableCell align="center">Total Amount</TableCell>
                        <TableCell align="center">Delivery Time</TableCell>
                        <TableCell align="center">Quality Rating</TableCell>
                        <TableCell align="center">Final Score</TableCell>
                        <TableCell align="center">Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {analysisResults.detailed_results.map((result, index) => {
                        const quotation = quotations.find(q => q.id === result.quotation_id);
                        const isWinner = result.rank === 1;
                        const isSecond = result.rank === 2;

                        return (
                          <TableRow
                            key={result.quotation_id}
                            sx={{
                              backgroundColor: isWinner ? 'rgba(76, 175, 80, 0.08)' :
                                             isSecond ? 'rgba(255, 193, 7, 0.08)' :
                                             'transparent',
                              '&:hover': {
                                backgroundColor: isWinner ? 'rgba(76, 175, 80, 0.12)' :
                                               isSecond ? 'rgba(255, 193, 7, 0.12)' :
                                               'rgba(0, 0, 0, 0.04)',
                              },

                            }}
                          >
                            {/* Rank Column */}
                            <TableCell align="center">
                              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                                <Chip
                                  label={`#${result.rank}`}
                                  color={isWinner ? 'success' : isSecond ? 'warning' : 'default'}
                                  sx={{
                                    fontWeight: 700,
                                    fontSize: '0.9rem',
                                    minWidth: 45,
                                  }}
                                />
                                {isWinner && (
                                  <Typography variant="caption" color="success.main" fontWeight={600}>
                                    🏆 BEST
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>

                            {/* Vendor Details Column */}
                            <TableCell>
                              <Box>
                                <Typography variant="subtitle1" fontWeight={700} color="primary">
                                  {quotation?.vendor_name || 'Unknown Vendor'}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Quotation ID: {result.quotation_id.slice(0, 8)}...
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Uploaded: {quotation?.created_at ? new Date(quotation.created_at).toLocaleDateString() : 'N/A'}
                                </Typography>
                              </Box>
                            </TableCell>

                            {/* Total Amount Column */}
                            <TableCell align="center">
                              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                                <Typography variant="h6" fontWeight={700} color={isWinner ? 'success.main' : 'text.primary'}>
                                  ₹{quotation?.total_amount?.toLocaleString() || 'N/A'}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Cost Score:
                                  </Typography>
                                  <Chip
                                    label={`${(result.cost_score * 100).toFixed(1)}%`}
                                    size="small"
                                    color={result.cost_score >= 0.8 ? 'success' : result.cost_score >= 0.6 ? 'warning' : 'error'}
                                  />
                                </Box>
                                {quotation?.total_amount && (
                                  <Typography variant="caption" color="text.secondary">
                                    {quotation.total_amount === Math.min(...quotations.map(q => q.total_amount || Infinity)) ? '💰 Lowest Price' : ''}
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>

                            {/* Delivery Time Column */}
                            <TableCell align="center">
                              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                                <Typography variant="subtitle2" fontWeight={600}>
                                  {quotation?.delivery_time || 'Not specified'}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Delivery Score:
                                  </Typography>
                                  <Chip
                                    label={`${((result.delivery_score || 0) * 100).toFixed(1)}%`}
                                    size="small"
                                    color={(result.delivery_score || 0) >= 0.8 ? 'success' : (result.delivery_score || 0) >= 0.6 ? 'warning' : 'error'}
                                  />
                                </Box>
                                <Typography variant="caption" color="text.secondary">
                                  {(result.delivery_score || 0) >= 0.8 ? '⚡ Fast Delivery' :
                                   (result.delivery_score || 0) >= 0.6 ? '📅 Standard' : '⏳ Slow'}
                                </Typography>
                              </Box>
                            </TableCell>

                            {/* Quality Rating Column */}
                            <TableCell align="center">
                              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                  {[...Array(5)].map((_, i) => (
                                    <Typography
                                      key={i}
                                      sx={{
                                        color: i < ((result.quality_score || 0) * 5) ? '#ffd700' : '#e0e0e0',
                                        fontSize: '1.2rem'
                                      }}
                                    >
                                      ⭐
                                    </Typography>
                                  ))}
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Quality Score:
                                  </Typography>
                                  <Chip
                                    label={`${((result.quality_score || 0) * 100).toFixed(1)}%`}
                                    size="small"
                                    color={(result.quality_score || 0) >= 0.8 ? 'success' : (result.quality_score || 0) >= 0.6 ? 'warning' : 'error'}
                                  />
                                </Box>
                                <Typography variant="caption" color="text.secondary">
                                  {(result.quality_score || 0) >= 0.8 ? '🏅 Premium' :
                                   (result.quality_score || 0) >= 0.6 ? '✅ Good' : '⚠️ Basic'}
                                </Typography>
                              </Box>
                            </TableCell>

                            {/* Final Score Column */}
                            <TableCell align="center">
                              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography
                                    variant="h5"
                                    fontWeight={700}
                                    color={isWinner ? 'success.main' : isSecond ? 'warning.main' : 'text.primary'}
                                  >
                                    {((result.overall_score || 0) * 100).toFixed(1)}%
                                  </Typography>
                                </Box>
                                <LinearProgress
                                  variant="determinate"
                                  value={(result.overall_score || 0) * 100}
                                  sx={{
                                    width: 80,
                                    height: 8,
                                    borderRadius: 4,
                                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                    '& .MuiLinearProgress-bar': {
                                      borderRadius: 4,
                                      background: isWinner ? 'linear-gradient(90deg, #4caf50, #66bb6a)' :
                                                 isSecond ? 'linear-gradient(90deg, #ff9800, #ffb74d)' :
                                                 'linear-gradient(90deg, #9e9e9e, #bdbdbd)',
                                    }
                                  }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  Risk: {((result.risk_score || 0) * 100).toFixed(0)}%
                                </Typography>
                              </Box>
                            </TableCell>

                            {/* Action Column - Approve Button for All Vendors */}
                            <TableCell align="center">
                              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                                {releasedPOs.has(result.quotation_id) ? (
                                  // PO Already Released
                                  <Button
                                    variant="contained"
                                    color="success"
                                    size="small"
                                    disabled
                                    startIcon={<CheckCircleIcon />}
                                    sx={{
                                      minWidth: 120,
                                      fontWeight: 600,
                                      borderRadius: 2,
                                      textTransform: 'none',
                                      background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                                    }}
                                  >
                                    PO Released
                                  </Button>
                                ) : approvedQuotations.has(result.quotation_id) ? (
                                  // Approved - Show Release PO Button
                                  <Button
                                    variant="contained"
                                    color="warning"
                                    size="small"
                                    onClick={() => handleReleasePO(result.quotation_id)}
                                    startIcon={<CheckCircleIcon />}
                                    disabled={loading}
                                    sx={{
                                      minWidth: 120,
                                      fontWeight: 600,
                                      borderRadius: 2,
                                      textTransform: 'none',
                                      background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
                                      boxShadow: '0 4px 12px rgba(255, 152, 0, 0.3)',
                                      '&:hover': {
                                        background: 'linear-gradient(135deg, #f57c00 0%, #ff9800 100%)',
                                        boxShadow: '0 6px 16px rgba(255, 152, 0, 0.4)',
                                      }
                                    }}
                                  >
                                    Release PO
                                  </Button>
                                ) : (
                                  // Not Approved Yet - Show Approve Button
                                  <Button
                                    variant={isWinner ? "contained" : "outlined"}
                                    color={isWinner ? "success" : "primary"}
                                    size="small"
                                    onClick={() => handleApproveQuotation(result.quotation_id)}
                                    startIcon={<CheckCircleIcon />}
                                    disabled={loading}
                                    sx={{
                                      minWidth: 120,
                                      fontWeight: 600,
                                      borderRadius: 2,
                                      textTransform: 'none',
                                      ...(isWinner && {
                                        background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                                        boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                                        '&:hover': {
                                          background: 'linear-gradient(135deg, #43a047 0%, #5cb85c 100%)',
                                          boxShadow: '0 6px 16px rgba(76, 175, 80, 0.4)',
                                        }
                                      }),
                                      ...(!isWinner && {
                                        borderColor: 'primary.main',
                                        '&:hover': {
                                          background: 'rgba(25, 118, 210, 0.08)',
                                          borderColor: 'primary.dark',
                                        }
                                      })
                                    }}
                                  >
                                    {isWinner ? 'Approve Best' : 'Approve'}
                                  </Button>
                                )}




                              </Box>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Quick Comparison Summary */}
                <Paper sx={{ p: 3, borderRadius: 3, background: 'linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%)' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      📊 Quick Comparison Summary
                    </Typography>
                    <Chip
                      label="💡 You can approve any vendor - AI recommendation is guidance"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                        color: '#1976d2',
                        fontWeight: 600,
                        border: '1px solid #2196f3',
                      }}
                    />
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(76, 175, 80, 0.1)' }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight={600}>
                          💰 Best Price
                        </Typography>
                        <Typography variant="h6" fontWeight={700}>
                          ₹{Math.min(...quotations.map(q => q.total_amount || Infinity)).toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {quotations.find(q => q.total_amount === Math.min(...quotations.map(q => q.total_amount || Infinity)))?.vendor_name}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(33, 150, 243, 0.1)' }}>
                        <Typography variant="subtitle2" color="primary.main" fontWeight={600}>
                          🏆 AI Recommended
                        </Typography>
                        <Typography variant="h6" fontWeight={700}>
                          {analysisResults.analysis_summary.recommended_vendor}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {(analysisResults.analysis_summary.top_score * 100).toFixed(1)}% Overall Score
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, background: 'rgba(156, 39, 176, 0.1)' }}>
                        <Typography variant="subtitle2" color="secondary.main" fontWeight={600}>
                          📈 Price Range
                        </Typography>
                        <Typography variant="h6" fontWeight={700}>
                          ₹{(Math.max(...quotations.map(q => q.total_amount || 0)) - Math.min(...quotations.map(q => q.total_amount || Infinity))).toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Difference between highest and lowest
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setComparisonDialogOpen(false)}
              variant="outlined"
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600,
                px: 3
              }}
            >
              Close Comparison
            </Button>
            <Box sx={{ flexGrow: 1 }} />
            <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
              💡 Choose any vendor above - AI recommendation is just guidance
            </Typography>
          </DialogActions>
        </Dialog>

        {/* Auto Email Processor Dialog */}
        <AutoEmailProcessor
          open={autoEmailDialogOpen}
          onClose={() => setAutoEmailDialogOpen(false)}
          requestId={currentRequestId}
        />
      </Container>
    </Box>
  );
};

export default QuotationManager;
