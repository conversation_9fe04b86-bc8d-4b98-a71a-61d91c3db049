import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Fade,
  Zoom
} from '@mui/material';
import {
  Construction as ConstructionIcon,
  Settings as SettingsIcon,
  Assessment as AssessmentIcon,
  Add as AddIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  AccountBalance as AccountBalanceIcon,
  Engineering as EngineeringIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import GradeConfiguration from '../components/ActivityLog/GradeConfiguration';
import RateManagement from '../components/ActivityLog/RateManagement';
import DailyActivityLog from '../components/ActivityLog/DailyActivityLog';
import ActivityDashboard from '../components/ActivityLog/ActivityDashboard';

const ActivityLog = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Tab configuration with premium styling
  const tabs = [
    {
      label: 'Dashboard',
      icon: <AssessmentIcon />,
      component: <ActivityDashboard data={dashboardData} />,
      color: '#667eea',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    {
      label: 'Daily Activity',
      icon: <ConstructionIcon />,
      component: <DailyActivityLog onDataChange={fetchDashboardData} />,
      color: '#f093fb',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
    },
    {
      label: 'Grade Configuration',
      icon: <SettingsIcon />,
      component: <GradeConfiguration onDataChange={fetchDashboardData} />,
      color: '#4facfe',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
    },
    {
      label: 'Rate Management',
      icon: <AccountBalanceIcon />,
      component: <RateManagement onDataChange={fetchDashboardData} />,
      color: '#43e97b',
      gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
    }
  ];

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5017/api/activity-log/dashboard');
      const data = await response.json();
      if (data.success) {
        setDashboardData(data.dashboard);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ 
      minHeight: '100vh',
      background: `
        linear-gradient(135deg, 
          rgba(102, 126, 234, 0.05) 0%, 
          rgba(118, 75, 162, 0.05) 25%,
          rgba(240, 147, 251, 0.05) 50%,
          rgba(245, 87, 108, 0.05) 75%,
          rgba(79, 172, 254, 0.05) 100%
        )
      `,
      p: 3
    }}>
      {/* Premium Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Paper
          elevation={0}
          sx={{
            mb: 4,
            borderRadius: 4,
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 251, 235, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px) saturate(180%)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: `
              0 20px 40px -12px rgba(0, 0, 0, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.1)
            `,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '3px',
              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
            },
          }}
        >
          <Box sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: 4,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 12px 32px rgba(102, 126, 234, 0.4)',
                  }}
                >
                  <EngineeringIcon sx={{ fontSize: 32, color: 'white' }} />
                </Box>
                <Box>
                  <Typography
                    variant="h3"
                    fontWeight={800}
                    sx={{
                      color: '#1e293b',
                      fontSize: '2.5rem',
                      letterSpacing: '-0.02em',
                      fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    Activity Log
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      color: '#64748b',
                      fontWeight: 500,
                      mt: 0.5,
                      fontSize: '1.1rem',
                    }}
                  >
                    🏗️ Construction Material Calculation & Daily Work Progress Tracking
                  </Typography>
                </Box>
              </Box>
              
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Tooltip title="Quick Add Activity" arrow>
                  <IconButton
                    sx={{
                      background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                      color: 'white',
                      width: 48,
                      height: 48,
                      boxShadow: '0 8px 24px rgba(67, 233, 123, 0.3)',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 32px rgba(67, 233, 123, 0.4)',
                      },
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                    onClick={() => setActiveTab(1)}
                  >
                    <AddIcon />
                  </IconButton>
                </Tooltip>
                
                <Tooltip title="View Analytics" arrow>
                  <IconButton
                    sx={{
                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                      color: 'white',
                      width: 48,
                      height: 48,
                      boxShadow: '0 8px 24px rgba(240, 147, 251, 0.3)',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 32px rgba(240, 147, 251, 0.4)',
                      },
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                    onClick={() => setActiveTab(0)}
                  >
                    <TrendingUpIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          </Box>
        </Paper>
      </motion.div>

      {/* Premium Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Paper
          elevation={0}
          sx={{
            mb: 4,
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 100%
              )
            `,
            backdropFilter: 'blur(40px) saturate(200%)',
            border: '2px solid rgba(148, 163, 184, 0.15)',
            boxShadow: `
              0 32px 64px -12px rgba(15, 23, 42, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.8),
              inset 0 1px 0 rgba(255, 255, 255, 0.9)
            `,
            overflow: 'hidden',
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              '& .MuiTabs-flexContainer': {
                gap: 1,
                p: 2,
              },
              '& .MuiTab-root': {
                minHeight: 64,
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                color: '#64748b',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  background: 'rgba(148, 163, 184, 0.1)',
                  transform: 'translateY(-1px)',
                },
                '&.Mui-selected': {
                  color: 'white',
                  fontWeight: 700,
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                },
              },
              '& .MuiTabs-indicator': {
                display: 'none',
              },
            }}
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                icon={tab.icon}
                label={tab.label}
                iconPosition="start"
                sx={{
                  background: activeTab === index ? tab.gradient : 'transparent',
                  '&.Mui-selected': {
                    background: tab.gradient,
                  },
                }}
              />
            ))}
          </Tabs>
        </Paper>
      </motion.div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.4 }}
        >
          {tabs[activeTab].component}
        </motion.div>
      </AnimatePresence>
    </Box>
  );
};

export default ActivityLog;
