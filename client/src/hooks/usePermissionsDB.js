import { useState, useEffect, useMemo } from 'react';
import Cookies from 'js-cookie';

/**
 * Hook to get user permissions from database based on their role
 */
export const usePermissionsDB = () => {
  const [permissions, setPermissions] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const userRole = Cookies.get('userRole');
  const token = Cookies.get('token');

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!token || !userRole) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetch('http://localhost:5017/api/permissions/user-permissions', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch permissions');
        }

        const data = await response.json();
        setPermissions(data.permissions || {});
        setError(null);
      } catch (err) {
        console.error('Error fetching permissions:', err);
        setError(err.message);
        // Fallback to basic permissions if API fails
        setPermissions({});
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [token, userRole]);

  const hasPermission = (module, action = 'view') => {
    if (loading) return false;
    return permissions[module]?.[action] || false;
  };
  
  const canView = (module) => hasPermission(module, 'view');
  const canManage = (module) => hasPermission(module, 'manage');
  const canInitiate = (module) => hasPermission(module, 'initiate');
  const canReceive = (module) => hasPermission(module, 'receive');
  
  const isAdmin = () => userRole === 'admin' || userRole === 'superadmin';
  const isSuperAdmin = () => userRole === 'superadmin';
  const isStorekeeper = () => userRole === 'storekeeper';
  const isSiteEngineer = () => userRole === 'siteengineer';
  const isUser = () => userRole === 'user';

  return {
    userRole,
    permissions,
    loading,
    error,
    hasPermission,
    canView,
    canManage,
    canInitiate,
    canReceive,
    isAdmin,
    isSuperAdmin,
    isStorekeeper,
    isSiteEngineer,
    isUser
  };
};

/**
 * Component wrapper for permission-based rendering using database permissions
 */
export const PermissionGateDB = ({ 
  module, 
  action = 'view', 
  children, 
  fallback = null,
  requireAll = false
}) => {
  const { hasPermission, loading } = usePermissionsDB();
  
  if (loading) {
    return fallback; // Show fallback while loading
  }
  
  let hasAccess = false;
  
  if (Array.isArray(module)) {
    if (requireAll) {
      hasAccess = module.every(mod => 
        Array.isArray(action) 
          ? action.every(act => hasPermission(mod, act))
          : hasPermission(mod, action)
      );
    } else {
      hasAccess = module.some(mod => 
        Array.isArray(action)
          ? action.some(act => hasPermission(mod, act))
          : hasPermission(mod, action)
      );
    }
  } else {
    if (Array.isArray(action)) {
      hasAccess = requireAll 
        ? action.every(act => hasPermission(module, act))
        : action.some(act => hasPermission(module, act));
    } else {
      hasAccess = hasPermission(module, action);
    }
  }
  
  return hasAccess ? children : fallback;
};

/**
 * Navigation items generator using database permissions
 */
export const useNavigationItems = () => {
  const { canView, loading } = usePermissionsDB();

  const navigationItems = useMemo(() => {
    if (loading) return [];

    return [
      {
        key: 'dashboard',
        label: 'Dashboard',
        path: '/app/dashboard',
        visible: true // Dashboard is always visible
      },
      {
        key: 'inventory',
        label: 'Stock Management',
        path: '/app/inventory',
        visible: canView('inventory')
      },
      {
        key: 'products',
        label: 'Product Management',
        path: '/app/products',
        visible: canView('products')
      },
      {
        key: 'transfers',
        label: 'Transfer Management',
        path: '/app/transfers',
        visible: canView('transfers')
      },
      {
        key: 'stores',
        label: 'Store Registration',
        path: '/app/stores',
        visible: canView('storeRegistration')
      },
      {
        key: 'machinery',
        label: 'Plant & Machinery',
        path: '/app/machinery',
        visible: canView('machinery')
      },
      {
        key: 'activity-log',
        label: 'Activity Log',
        path: '/app/activity-log',
        visible: canView('activityLog')
      },
      {
        key: 'daily-progress-report',
        label: 'Daily Progress Report',
        path: '/app/daily-progress-report',
        visible: canView('dpr')
      },
      {
        key: 'users',
        label: 'User Management',
        path: '/app/users',
        visible: canView('users')
      },
      {
        key: 'roles',
        label: 'Role Management',
        path: '/app/roles',
        visible: canView('roles')
      },
      {
        key: 'reports',
        label: 'Reports',
        path: '/app/reports',
        visible: canView('reports')
      }
    ].filter(item => item.visible);
  }, [canView, loading]);

  return { navigationItems, loading };
};
