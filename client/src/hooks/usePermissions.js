import { useMemo } from 'react';
import Cookies from 'js-cookie';

/**
 * Permission system for role-based access control
 * Defines what each role can access and do
 */
const ROLE_PERMISSIONS = {
  superadmin: {
    // Superadmin has access to everything
    dashboard: { view: true, manage: true },
    inventory: { view: true, manage: true },
    products: { view: true, manage: true },
    transfers: { view: true, initiate: true, receive: true, manage: true },
    storeRegistration: { view: true, manage: true },
    storeLocations: { view: true, manage: true },
    users: { view: true, manage: true },
    roles: { view: true, manage: true },
    billing: { view: true, manage: true },
    machinery: { view: true, manage: true },
    reports: { view: true, manage: true },
    charts: { view: true, manage: true },
    analytics: { view: true, manage: true },
    recommendations: { view: true, manage: true },
    procurement: { view: true, manage: true },
    activityLog: { view: true, manage: true }
  },
  
  admin: {
    dashboard: { view: true, manage: true },
    inventory: { view: true, manage: true },
    products: { view: true, manage: true },
    transfers: { view: true, initiate: true, receive: true, manage: true },
    storeRegistration: { view: true, manage: true },
    storeLocations: { view: true, manage: true },
    users: { view: true, manage: true },
    roles: { view: false, manage: false }, // Only superadmin can manage roles
    billing: { view: true, manage: true },
    machinery: { view: true, manage: true },
    reports: { view: true, manage: true },
    charts: { view: true, manage: true },
    analytics: { view: true, manage: true },
    recommendations: { view: true, manage: true },
    procurement: { view: true, manage: true },
    activityLog: { view: true, manage: true }
  },
  
  storekeeper: {
    dashboard: { view: true, manage: false },
    inventory: { view: true, manage: true },
    products: { view: true, manage: true },
    transfers: { view: true, initiate: true, receive: true, manage: false },
    storeRegistration: { view: true, manage: false },
    storeLocations: { view: true, manage: false },
    users: { view: false, manage: false },
    roles: { view: false, manage: false },
    billing: { view: true, manage: false },
    machinery: { view: true, manage: false },
    reports: { view: true, manage: false },
    charts: { view: true, manage: false },
    analytics: { view: false, manage: false },
    recommendations: { view: true, manage: false },
    procurement: { view: true, manage: false },
    activityLog: { view: true, manage: true }
  },
  
  siteengineer: {
    dashboard: { view: true, manage: false },
    inventory: { view: true, manage: false },
    products: { view: true, manage: false },
    transfers: { view: true, initiate: true, receive: true, manage: false },
    storeRegistration: { view: true, manage: false },
    storeLocations: { view: true, manage: false },
    users: { view: false, manage: false },
    roles: { view: false, manage: false },
    billing: { view: true, manage: false },
    machinery: { view: true, manage: true }, // Site engineers can manage machinery
    reports: { view: true, manage: false },
    charts: { view: true, manage: false },
    analytics: { view: false, manage: false },
    recommendations: { view: true, manage: false },
    procurement: { view: true, manage: false },
    activityLog: { view: true, manage: true }
  },
  
  user: {
    dashboard: { view: true, manage: false },
    inventory: { view: true, manage: false },
    products: { view: true, manage: false },
    transfers: { view: true, initiate: false, receive: false, manage: false },
    storeRegistration: { view: false, manage: false },
    storeLocations: { view: true, manage: false },
    users: { view: false, manage: false },
    roles: { view: false, manage: false },
    billing: { view: true, manage: false },
    machinery: { view: true, manage: false },
    reports: { view: false, manage: false },
    charts: { view: true, manage: false },
    analytics: { view: false, manage: false },
    recommendations: { view: true, manage: false },
    procurement: { view: true, manage: false },
    activityLog: { view: true, manage: false }
  }
};

/**
 * Hook to get user permissions based on their role
 */
export const usePermissions = () => {
  const userRole = Cookies.get('userRole');
  
  const permissions = useMemo(() => {
    if (!userRole || !ROLE_PERMISSIONS[userRole]) {
      return {};
    }
    return ROLE_PERMISSIONS[userRole];
  }, [userRole]);
  
  const hasPermission = (module, action = 'view') => {
    return permissions[module]?.[action] || false;
  };
  
  const canView = (module) => hasPermission(module, 'view');
  const canManage = (module) => hasPermission(module, 'manage');
  const canInitiate = (module) => hasPermission(module, 'initiate');
  const canReceive = (module) => hasPermission(module, 'receive');
  
  const isAdmin = () => userRole === 'admin' || userRole === 'superadmin';
  const isSuperAdmin = () => userRole === 'superadmin';
  const isStorekeeper = () => userRole === 'storekeeper';
  const isSiteEngineer = () => userRole === 'siteengineer';
  const isUser = () => userRole === 'user';
  
  return {
    userRole,
    permissions,
    hasPermission,
    canView,
    canManage,
    canInitiate,
    canReceive,
    isAdmin,
    isSuperAdmin,
    isStorekeeper,
    isSiteEngineer,
    isUser
  };
};

/**
 * Component wrapper for permission-based rendering
 */
export const PermissionGate = ({ 
  module, 
  action = 'view', 
  children, 
  fallback = null,
  requireAll = false // If true, user must have ALL specified permissions
}) => {
  const { hasPermission } = usePermissions();
  
  let hasAccess = false;
  
  if (Array.isArray(module)) {
    // Multiple modules/actions
    if (requireAll) {
      hasAccess = module.every(mod => 
        Array.isArray(action) 
          ? action.every(act => hasPermission(mod, act))
          : hasPermission(mod, action)
      );
    } else {
      hasAccess = module.some(mod => 
        Array.isArray(action)
          ? action.some(act => hasPermission(mod, act))
          : hasPermission(mod, action)
      );
    }
  } else {
    // Single module
    if (Array.isArray(action)) {
      hasAccess = requireAll 
        ? action.every(act => hasPermission(module, act))
        : action.some(act => hasPermission(module, act));
    } else {
      hasAccess = hasPermission(module, action);
    }
  }
  
  return hasAccess ? children : fallback;
};

/**
 * Hook for navigation permissions
 */
export const useNavigationPermissions = () => {
  const { canView, isAdmin, isSuperAdmin } = usePermissions();
  
  const navigationItems = [
    {
      key: 'dashboard',
      label: 'Dashboard',
      path: '/app/dashboard',
      visible: canView('dashboard')
    },
    {
      key: 'inventory',
      label: 'Inventory',
      path: '/app/inventory',
      visible: canView('inventory'),
      children: [
        {
          key: 'products',
          label: 'Stock Management',
          path: '/app/inventory',
          visible: canView('products')
        },
        {
          key: 'initiate-transfer',
          label: 'Initiate Transfer',
          path: '/app/initiate-transfer',
          visible: canView('transfers')
        },
        {
          key: 'received-transfers',
          label: 'Received Transfers',
          path: '/app/received-transfers',
          visible: canView('transfers')
        },
        {
          key: 'store-registration',
          label: 'Store Registration',
          path: '/app/store-registration',
          visible: canView('storeRegistration')
        }
      ]
    },
    {
      key: 'security',
      label: 'Security',
      visible: isAdmin(),
      children: [
        {
          key: 'user-management',
          label: 'User Management',
          path: '/app/user-management',
          visible: canView('users')
        },
        {
          key: 'role-management',
          label: 'Role Management',
          path: '/app/role-management',
          visible: isSuperAdmin()
        }
      ]
    },
    {
      key: 'billing',
      label: 'Bill Details',
      path: '/app/bill-details',
      visible: canView('billing')
    },
    {
      key: 'machinery',
      label: 'Plant & Machinery',
      path: '/app/machinery-work',
      visible: canView('machinery')
    },
    {
      key: 'charts',
      label: 'Charts',
      path: '/app/charts',
      visible: canView('charts')
    },
    {
      key: 'activity-log',
      label: 'Activity Log',
      path: '/app/activity-log',
      visible: canView('activityLog')
    },
    {
      key: 'procurement',
      label: 'Procurement System',
      visible: canView('procurement'),
      children: [
        {
          key: 'vendor-registration',
          label: 'Vendor Registration',
          path: '/app/vendor-registration',
          visible: canView('procurement')
        },
        {
          key: 'procurement-requests',
          label: 'Procurement Requests',
          path: '/app/procurement-system',
          visible: canView('procurement')
        },
        {
          key: 'quotation-manager',
          label: 'Quotation Manager',
          path: '/app/quotation-manager',
          visible: canView('procurement')
        }
      ]
    }
  ];
  
  return navigationItems.filter(item => item.visible);
};

export default usePermissions;
