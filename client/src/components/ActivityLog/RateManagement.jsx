import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  TrendingUp as TrendingUpIcon,
  AccountBalance as AccountBalanceIcon,
  ExpandMore as ExpandMoreIcon,
  Update as UpdateIcon,
  History as HistoryIcon
} from '@mui/icons-material';

import { motion, AnimatePresence } from 'framer-motion';

const RateManagement = ({ onDataChange }) => {
  const [rateCards, setRateCards] = useState([]);
  const [currentRateCard, setCurrentRateCard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [formData, setFormData] = useState({
    effectiveDate: new Date(),
    materials: [
      { name: 'Cement', unit: 'ton', rate: 3780.00 },
      { name: 'Coarse sand', unit: 'CubicMeter', rate: 1765.00 },
      { name: '20 mm aggregate', unit: 'CubicMeter', rate: 882.75 },
      { name: '10 mm aggregate', unit: 'CubicMeter', rate: 882.75 },
      { name: 'Admixture', unit: 'Kg', rate: 140.00 }
    ],
    labour: [
      { name: 'Mason (1st Class)', unit: 'day', rate: 400.00 },
      { name: 'Mazdoor (Unskilled)', unit: 'day', rate: 350.00 }
    ],
    machinery: [
      { name: 'Concrete mixer 0.4/0.28 cum capacity', unit: 'hour', rate: 500.00 },
      { name: 'Generator 33 KVA', unit: 'hour', rate: 800.00 },
      { name: 'Vibrator', unit: 'hour', rate: 0.00 },
      { name: 'Concrete Pump', unit: 'hour', rate: 605.00 }
    ]
  });

  // Available units for different categories
  const units = {
    material: ['ton', 'CubicMeter', 'Kg', 'bag', 'cft'],
    labour: ['day', 'hour', 'shift'],
    machinery: ['hour', 'day', 'shift']
  };

  useEffect(() => {
    fetchRateCards();
    fetchCurrentRateCard();
  }, []);

  const fetchRateCards = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5017/api/activity-log/rates');
      const data = await response.json();
      if (data.success) {
        setRateCards(data.rateCards);
      }
    } catch (error) {
      console.error('Error fetching rate cards:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrentRateCard = async () => {
    try {
      const response = await fetch('http://localhost:5017/api/activity-log/rates/current');
      const data = await response.json();
      if (data.success && data.rateCard) {
        setCurrentRateCard(data.rateCard);
      }
    } catch (error) {
      console.error('Error fetching current rate card:', error);
    }
  };

  const handleOpenDialog = () => {
    if (currentRateCard) {
      setFormData({
        effectiveDate: new Date(),
        materials: [...currentRateCard.materials],
        labour: [...currentRateCard.labour],
        machinery: [...currentRateCard.machinery]
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const addItem = (category) => {
    const newItem = {
      name: '',
      unit: units[category === 'materials' ? 'material' : category === 'labour' ? 'labour' : 'machinery'][0],
      rate: 0
    };
    
    setFormData(prev => ({
      ...prev,
      [category]: [...prev[category], newItem]
    }));
  };

  const removeItem = (category, index) => {
    setFormData(prev => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index)
    }));
  };

  const updateItem = (category, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      [category]: prev[category].map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const handleSave = async () => {
    try {
      const payload = {
        ...formData,
        createdBy: 'current-user' // Replace with actual user ID
      };

      const response = await fetch('http://localhost:5017/api/activity-log/rates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      if (data.success) {
        fetchRateCards();
        fetchCurrentRateCard();
        handleCloseDialog();
        if (onDataChange) onDataChange();
      }
    } catch (error) {
      console.error('Error saving rate card:', error);
    }
  };

  const renderItemSection = (category, title, icon, categoryKey) => {
    const items = formData[category];

    return (
      <Accordion defaultExpanded sx={{ mb: 2, borderRadius: 2, '&:before': { display: 'none' } }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
            borderRadius: 2,
            mb: 1
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
            {icon}
            <Typography variant="h6" fontWeight={600}>{title}</Typography>
            <Chip 
              label={`${items.length} items`} 
              color="primary" 
              size="small"
              sx={{ ml: 'auto' }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2 }}>
            <Button
              startIcon={<AddIcon />}
              onClick={() => addItem(category)}
              variant="outlined"
              size="small"
              sx={{ borderRadius: 2 }}
            >
              Add {title.slice(0, -1)}
            </Button>
          </Box>
          
          {items.map((item, index) => (
            <Card key={index} sx={{ mb: 2, borderRadius: 2 }}>
              <CardContent sx={{ p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={5}>
                    <TextField
                      label="Name"
                      value={item.name}
                      onChange={(e) => updateItem(category, index, 'name', e.target.value)}
                      fullWidth
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <TextField
                      select
                      label="Unit"
                      value={item.unit}
                      onChange={(e) => updateItem(category, index, 'unit', e.target.value)}
                      fullWidth
                      size="small"
                      SelectProps={{ native: true }}
                    >
                      {units[categoryKey].map(unit => (
                        <option key={unit} value={unit}>{unit}</option>
                      ))}
                    </TextField>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <TextField
                      label="Rate (₹)"
                      type="number"
                      value={item.rate}
                      onChange={(e) => updateItem(category, index, 'rate', parseFloat(e.target.value) || 0)}
                      fullWidth
                      size="small"
                      inputProps={{ step: 0.01 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={1}>
                    <IconButton
                      onClick={() => removeItem(category, index)}
                      color="error"
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          ))}
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Box>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" fontWeight={700} sx={{ color: '#1e293b' }}>
            💰 Rate Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<UpdateIcon />}
            onClick={handleOpenDialog}
            sx={{
              borderRadius: 3,
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              boxShadow: '0 8px 24px rgba(67, 233, 123, 0.3)',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 32px rgba(67, 233, 123, 0.4)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            Update Rates
          </Button>
        </Box>

        {/* Current Rate Card */}
        {currentRateCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Paper
              sx={{
                mb: 4,
                borderRadius: 4,
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(248, 250, 252, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(67, 233, 123, 0.3)',
                boxShadow: '0 20px 40px -12px rgba(67, 233, 123, 0.25)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '3px',
                  background: 'linear-gradient(90deg, #43e97b 0%, #38f9d7 100%)',
                },
              }}
            >
              <Box sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <AccountBalanceIcon sx={{ color: 'success.main', fontSize: 32 }} />
                  <Box>
                    <Typography variant="h5" fontWeight={700} sx={{ color: '#1e293b' }}>
                      Current Rate Card
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#64748b' }}>
                      Effective from: {new Date(currentRateCard.effectiveDate).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Box>

                <Grid container spacing={4}>
                  {/* Materials */}
                  <Grid item xs={12} md={4}>
                    <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: 'primary.main' }}>
                      🏗️ Materials
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item</TableCell>
                            <TableCell align="center">Unit</TableCell>
                            <TableCell align="right">Rate</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {currentRateCard.materials.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell sx={{ fontSize: '0.875rem' }}>{item.name}</TableCell>
                              <TableCell align="center" sx={{ fontSize: '0.875rem' }}>{item.unit}</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                                ₹{item.rate.toLocaleString()}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>

                  {/* Labour */}
                  <Grid item xs={12} md={4}>
                    <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: 'secondary.main' }}>
                      👷 Labour
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item</TableCell>
                            <TableCell align="center">Unit</TableCell>
                            <TableCell align="right">Rate</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {currentRateCard.labour.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell sx={{ fontSize: '0.875rem' }}>{item.name}</TableCell>
                              <TableCell align="center" sx={{ fontSize: '0.875rem' }}>{item.unit}</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                                ₹{item.rate.toLocaleString()}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>

                  {/* Machinery */}
                  <Grid item xs={12} md={4}>
                    <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: 'success.main' }}>
                      🚜 Machinery
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item</TableCell>
                            <TableCell align="center">Unit</TableCell>
                            <TableCell align="right">Rate</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {currentRateCard.machinery.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell sx={{ fontSize: '0.875rem' }}>{item.name}</TableCell>
                              <TableCell align="center" sx={{ fontSize: '0.875rem' }}>{item.unit}</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                                ₹{item.rate.toLocaleString()}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </motion.div>
        )}

        {/* Rate History */}
        <Paper
          sx={{
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
          }}
        >
          <Box sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
              <HistoryIcon sx={{ color: 'primary.main', fontSize: 32 }} />
              <Typography variant="h5" fontWeight={700} sx={{ color: '#1e293b' }}>
                Rate History
              </Typography>
            </Box>

            {rateCards.length === 0 ? (
              <Alert severity="info" sx={{ borderRadius: 2 }}>
                No rate history available. Create your first rate card to get started.
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Effective Date</TableCell>
                      <TableCell align="center">Materials</TableCell>
                      <TableCell align="center">Labour</TableCell>
                      <TableCell align="center">Machinery</TableCell>
                      <TableCell align="center">Created</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {rateCards.map((rateCard, index) => (
                      <TableRow key={rateCard.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight={600}>
                            {new Date(rateCard.effectiveDate).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Chip label={`${rateCard.materials.length} items`} size="small" color="primary" />
                        </TableCell>
                        <TableCell align="center">
                          <Chip label={`${rateCard.labour.length} items`} size="small" color="secondary" />
                        </TableCell>
                        <TableCell align="center">
                          <Chip label={`${rateCard.machinery.length} items`} size="small" color="success" />
                        </TableCell>
                        <TableCell align="center">
                          <Typography variant="body2" color="text.secondary">
                            {new Date(rateCard.createdAt).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        </Paper>

        {/* Update Rates Dialog */}
        <Dialog 
          open={openDialog} 
          onClose={handleCloseDialog} 
          maxWidth="lg" 
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.98) 0%,
                  rgba(248, 250, 252, 0.95) 100%
                )
              `,
              backdropFilter: 'blur(40px)',
            }
          }}
        >
          <DialogTitle sx={{ pb: 1 }}>
            <Typography variant="h5" fontWeight={700}>
              Update Rate Card
            </Typography>
          </DialogTitle>
          <DialogContent sx={{ pt: 2 }}>
            <Box sx={{ mb: 3 }}>
              <TextField
                label="Effective Date"
                type="date"
                value={formData.effectiveDate ? formData.effectiveDate.toISOString().split('T')[0] : ''}
                onChange={(e) => setFormData(prev => ({ ...prev, effectiveDate: new Date(e.target.value) }))}
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>

            {/* Dynamic Sections */}
            {renderItemSection('materials', 'Materials', <AccountBalanceIcon color="primary" />, 'material')}
            {renderItemSection('labour', 'Labour', <AccountBalanceIcon color="secondary" />, 'labour')}
            {renderItemSection('machinery', 'Machinery', <AccountBalanceIcon color="success" />, 'machinery')}
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 1 }}>
            <Button 
              onClick={handleCloseDialog}
              startIcon={<CancelIcon />}
              sx={{ borderRadius: 2 }}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              variant="contained"
              startIcon={<SaveIcon />}
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #38f9d7 0%, #43e97b 100%)',
                },
              }}
            >
              Save Rate Card
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
};

export default RateManagement;
