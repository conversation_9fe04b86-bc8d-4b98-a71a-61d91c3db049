import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  LinearProgress
} from '@mui/material';
import PremiumDialog from '../common/PremiumDialog';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Construction as ConstructionIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  ExpandMore as ExpandMoreIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

import { motion, AnimatePresence } from 'framer-motion';

const DailyActivityLog = ({ onDataChange }) => {
  const [logs, setLogs] = useState([]);
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingLog, setEditingLog] = useState(null);
  const [selectedGrade, setSelectedGrade] = useState(null);
  const [formData, setFormData] = useState({
    date: new Date(),
    gradeId: '',
    workDescription: '',
    quantityCompleted: 1, // Default to 1 instead of 0
    projectId: '',
    projectName: '',
    location: '',
    contractor: '',
    status: 'draft'
  });

  // State for actual material quantities (editable by user)
  const [actualMaterialQuantities, setActualMaterialQuantities] = useState({});

  useEffect(() => {
    fetchLogs();
    fetchGrades();
  }, []);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5017/api/activity-log/logs');
      const data = await response.json();
      if (data.success) {
        setLogs(data.logs);
      }
    } catch (error) {
      console.error('Error fetching logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGrades = async () => {
    try {
      const response = await fetch('http://localhost:5017/api/activity-log/grades');
      const data = await response.json();
      if (data.success) {
        setGrades(data.grades);
      }
    } catch (error) {
      console.error('Error fetching grades:', error);
    }
  };

  const handleOpenDialog = (log = null) => {
    if (log) {
      setEditingLog(log);
      setFormData({
        date: new Date(log.date),
        gradeId: log.gradeId,
        workDescription: log.workDescription,
        quantityCompleted: log.quantityCompleted,
        projectId: log.projectId || '',
        projectName: log.projectName || '',
        location: log.location || '',
        contractor: log.contractor || '',
        status: log.status
      });
      const grade = grades.find(g => g.id === log.gradeId);
      setSelectedGrade(grade);
    } else {
      setEditingLog(null);
      setFormData({
        date: new Date(),
        gradeId: '',
        workDescription: '',
        quantityCompleted: 1, // Default to 1 instead of 0
        projectId: '',
        projectName: '',
        location: '',
        contractor: '',
        status: 'draft'
      });
      setSelectedGrade(null);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingLog(null);
    setSelectedGrade(null);
    setActualMaterialQuantities({});
  };

  const handleGradeChange = (gradeId) => {
    const grade = grades.find(g => g.id === gradeId);
    setSelectedGrade(grade);
    setFormData(prev => ({ ...prev, gradeId }));

    // Initialize actual quantities with expected quantities
    if (grade && formData.quantityCompleted) {
      const initialQuantities = {};
      [...grade.materials, ...grade.labour, ...grade.machinery].forEach(item => {
        const key = `${item.category}-${item.id}`;
        initialQuantities[key] = item.quantity * formData.quantityCompleted;
      });
      setActualMaterialQuantities(initialQuantities);
    }
  };

  const handleActualQuantityChange = (itemKey, newQuantity) => {
    setActualMaterialQuantities(prev => ({
      ...prev,
      [itemKey]: parseFloat(newQuantity) || 0
    }));
  };

  const calculateCosts = () => {
    if (!selectedGrade || !formData.quantityCompleted) return null;

    const quantity = parseFloat(formData.quantityCompleted);

    const materials = selectedGrade.materials.map(item => {
      const itemKey = `materials-${item.id}`;
      const expectedQuantity = item.quantity * quantity;
      const actualQuantity = actualMaterialQuantities[itemKey] !== undefined
        ? actualMaterialQuantities[itemKey]
        : expectedQuantity;

      return {
        ...item,
        expectedQuantity,
        actualQuantity,
        actualAmount: actualQuantity * item.rate,
        variance: actualQuantity - expectedQuantity,
        varianceAmount: (actualQuantity - expectedQuantity) * item.rate
      };
    });

    const labour = selectedGrade.labour.map(item => {
      const itemKey = `labour-${item.id}`;
      const expectedQuantity = item.quantity * quantity;
      const actualQuantity = actualMaterialQuantities[itemKey] !== undefined
        ? actualMaterialQuantities[itemKey]
        : expectedQuantity;

      return {
        ...item,
        expectedQuantity,
        actualQuantity,
        actualAmount: actualQuantity * item.rate,
        variance: actualQuantity - expectedQuantity,
        varianceAmount: (actualQuantity - expectedQuantity) * item.rate
      };
    });

    const machinery = selectedGrade.machinery.map(item => {
      const itemKey = `machinery-${item.id}`;
      const expectedQuantity = item.quantity * quantity;
      const actualQuantity = actualMaterialQuantities[itemKey] !== undefined
        ? actualMaterialQuantities[itemKey]
        : expectedQuantity;

      return {
        ...item,
        expectedQuantity,
        actualQuantity,
        actualAmount: actualQuantity * item.rate,
        variance: actualQuantity - expectedQuantity,
        varianceAmount: (actualQuantity - expectedQuantity) * item.rate
      };
    });

    const materialTotal = materials.reduce((sum, item) => sum + item.actualAmount, 0);
    const labourTotal = labour.reduce((sum, item) => sum + item.actualAmount, 0);
    const machineryTotal = machinery.reduce((sum, item) => sum + item.actualAmount, 0);
    const subtotal = materialTotal + labourTotal + machineryTotal;
    const overheadAmount = (subtotal * selectedGrade.overheadPercentage) / 100;
    const total = subtotal + overheadAmount;

    // Calculate expected totals for variance
    const expectedMaterialTotal = materials.reduce((sum, item) => sum + (item.expectedQuantity * item.rate), 0);
    const expectedLabourTotal = labour.reduce((sum, item) => sum + (item.expectedQuantity * item.rate), 0);
    const expectedMachineryTotal = machinery.reduce((sum, item) => sum + (item.expectedQuantity * item.rate), 0);
    const expectedSubtotal = expectedMaterialTotal + expectedLabourTotal + expectedMachineryTotal;
    const expectedOverheadAmount = (expectedSubtotal * selectedGrade.overheadPercentage) / 100;
    const expectedTotal = expectedSubtotal + expectedOverheadAmount;

    return {
      materials,
      labour,
      machinery,
      materialTotal,
      labourTotal,
      machineryTotal,
      overheadAmount,
      total,
      expectedMaterialTotal,
      expectedLabourTotal,
      expectedMachineryTotal,
      expectedOverheadAmount,
      expectedTotal,
      totalVariance: total - expectedTotal,
      totalVariancePercentage: expectedTotal > 0 ? ((total - expectedTotal) / expectedTotal) * 100 : 0
    };
  };

  const handleSave = async () => {
    try {
      const costs = calculateCosts();

      const payload = {
        ...formData,
        createdBy: 'current-user', // Replace with actual user ID
        // Include actual material breakdown for variance analysis
        materialBreakdown: costs ? [
          ...costs.materials.map(item => ({
            id: item.id,
            name: item.name,
            unit: item.unit,
            expectedQuantity: item.expectedQuantity,
            actualQuantity: item.actualQuantity,
            rate: item.rate,
            amount: item.actualAmount,
            variance: item.variance,
            varianceAmount: item.varianceAmount,
            category: 'material'
          })),
          ...costs.labour.map(item => ({
            id: item.id,
            name: item.name,
            unit: item.unit,
            expectedQuantity: item.expectedQuantity,
            actualQuantity: item.actualQuantity,
            rate: item.rate,
            amount: item.actualAmount,
            variance: item.variance,
            varianceAmount: item.varianceAmount,
            category: 'labour'
          })),
          ...costs.machinery.map(item => ({
            id: item.id,
            name: item.name,
            unit: item.unit,
            expectedQuantity: item.expectedQuantity,
            actualQuantity: item.actualQuantity,
            rate: item.rate,
            amount: item.actualAmount,
            variance: item.variance,
            varianceAmount: item.varianceAmount,
            category: 'machinery'
          }))
        ] : [],
        // Include variance summary
        varianceSummary: costs ? {
          expectedTotal: costs.expectedTotal,
          actualTotal: costs.total,
          totalVariance: costs.totalVariance,
          totalVariancePercentage: costs.totalVariancePercentage
        } : null
      };

      const url = editingLog
        ? `http://localhost:5017/api/activity-log/logs/${editingLog.id}`
        : 'http://localhost:5017/api/activity-log/logs';

      const method = editingLog ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      if (data.success) {
        fetchLogs();
        handleCloseDialog();
        if (onDataChange) onDataChange();
      }
    } catch (error) {
      console.error('Error saving log:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'verified': return 'primary';
      case 'draft': return 'warning';
      default: return 'default';
    }
  };

  const renderCostBreakdown = () => {
    const costs = calculateCosts();
    if (!costs) return null;

    return (
      <Paper
        sx={{
          p: 3,
          mt: 3,
          borderRadius: 3,
          background: 'linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%)',
          border: '1px solid rgba(67, 233, 123, 0.2)',
        }}
      >
        <Typography variant="h6" fontWeight={700} sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <AssessmentIcon color="success" />
          Cost Calculation Preview
        </Typography>

        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">Materials</Typography>
              <Typography variant="h6" fontWeight={600} color="primary.main">
                ₹{costs.materialTotal.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">Labour</Typography>
              <Typography variant="h6" fontWeight={600} color="secondary.main">
                ₹{costs.labourTotal.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">Machinery</Typography>
              <Typography variant="h6" fontWeight={600} color="success.main">
                ₹{costs.machineryTotal.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">Overhead</Typography>
              <Typography variant="h6" fontWeight={600} color="warning.main">
                ₹{costs.overheadAmount.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Total Cost for {formData.quantityCompleted} {selectedGrade?.unit}
          </Typography>
          <Typography 
            variant="h4" 
            fontWeight={700}
            sx={{ 
              color: 'success.main',
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            ₹{costs.total.toLocaleString()}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Rate per {selectedGrade?.unit}: ₹{(costs.total / formData.quantityCompleted).toLocaleString()}
          </Typography>
        </Box>

        {/* Detailed breakdown with variance analysis */}
        <Accordion sx={{ mt: 2 }} defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" fontWeight={600}>
              📋 Detailed Material Breakdown - Expected vs Actual
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Edit the actual quantities</strong> used in construction. The system will calculate variance automatically.
              </Typography>
            </Alert>

            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Item</TableCell>
                    <TableCell align="center">Unit</TableCell>
                    <TableCell align="center">Expected Qty</TableCell>
                    <TableCell align="center">Actual Qty</TableCell>
                    <TableCell align="center">Variance</TableCell>
                    <TableCell align="center">Rate</TableCell>
                    <TableCell align="center">Amount</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {costs.materials.map((item, index) => {
                    const itemKey = `materials-${item.id}`;
                    return (
                      <TableRow key={index} sx={{ backgroundColor: 'rgba(79, 172, 254, 0.05)' }}>
                        <TableCell>
                          <Typography variant="body2" fontWeight={600}>
                            {item.name}
                          </Typography>
                          <Typography variant="caption" color="primary">
                            Material
                          </Typography>
                        </TableCell>
                        <TableCell align="center">{item.unit}</TableCell>
                        <TableCell align="center">
                          <Typography variant="body2" color="text.secondary">
                            {item.expectedQuantity.toFixed(3)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <TextField
                            size="small"
                            type="number"
                            value={item.actualQuantity}
                            onChange={(e) => handleActualQuantityChange(itemKey, e.target.value)}
                            inputProps={{
                              step: 0.001,
                              min: 0,
                              style: { textAlign: 'center', fontSize: '0.875rem' }
                            }}
                            sx={{
                              width: 80,
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Typography
                            variant="body2"
                            fontWeight={600}
                            sx={{
                              color: item.variance >= 0 ? '#ef4444' : '#10b981'
                            }}
                          >
                            {item.variance >= 0 ? '+' : ''}{item.variance.toFixed(3)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">₹{item.rate.toLocaleString()}</TableCell>
                        <TableCell align="center" sx={{ fontWeight: 600 }}>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{item.actualAmount.toLocaleString()}
                          </Typography>
                          {item.varianceAmount !== 0 && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: item.varianceAmount >= 0 ? '#ef4444' : '#10b981',
                                display: 'block'
                              }}
                            >
                              {item.varianceAmount >= 0 ? '+' : ''}₹{Math.abs(item.varianceAmount).toLocaleString()}
                            </Typography>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  {costs.labour.map((item, index) => {
                    const itemKey = `labour-${item.id}`;
                    return (
                      <TableRow key={`labour-${index}`} sx={{ backgroundColor: 'rgba(245, 87, 108, 0.05)' }}>
                        <TableCell>
                          <Typography variant="body2" fontWeight={600}>
                            {item.name}
                          </Typography>
                          <Typography variant="caption" color="secondary">
                            Labour
                          </Typography>
                        </TableCell>
                        <TableCell align="center">{item.unit}</TableCell>
                        <TableCell align="center">
                          <Typography variant="body2" color="text.secondary">
                            {item.expectedQuantity.toFixed(3)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <TextField
                            size="small"
                            type="number"
                            value={item.actualQuantity}
                            onChange={(e) => handleActualQuantityChange(itemKey, e.target.value)}
                            inputProps={{
                              step: 0.001,
                              min: 0,
                              style: { textAlign: 'center', fontSize: '0.875rem' }
                            }}
                            sx={{
                              width: 80,
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Typography
                            variant="body2"
                            fontWeight={600}
                            sx={{
                              color: item.variance >= 0 ? '#ef4444' : '#10b981'
                            }}
                          >
                            {item.variance >= 0 ? '+' : ''}{item.variance.toFixed(3)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">₹{item.rate.toLocaleString()}</TableCell>
                        <TableCell align="center" sx={{ fontWeight: 600 }}>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{item.actualAmount.toLocaleString()}
                          </Typography>
                          {item.varianceAmount !== 0 && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: item.varianceAmount >= 0 ? '#ef4444' : '#10b981',
                                display: 'block'
                              }}
                            >
                              {item.varianceAmount >= 0 ? '+' : ''}₹{Math.abs(item.varianceAmount).toLocaleString()}
                            </Typography>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  {costs.machinery.map((item, index) => {
                    const itemKey = `machinery-${item.id}`;
                    return (
                      <TableRow key={`machinery-${index}`} sx={{ backgroundColor: 'rgba(67, 233, 123, 0.05)' }}>
                        <TableCell>
                          <Typography variant="body2" fontWeight={600}>
                            {item.name}
                          </Typography>
                          <Typography variant="caption" color="success.main">
                            Machinery
                          </Typography>
                        </TableCell>
                        <TableCell align="center">{item.unit}</TableCell>
                        <TableCell align="center">
                          <Typography variant="body2" color="text.secondary">
                            {item.expectedQuantity.toFixed(3)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <TextField
                            size="small"
                            type="number"
                            value={item.actualQuantity}
                            onChange={(e) => handleActualQuantityChange(itemKey, e.target.value)}
                            inputProps={{
                              step: 0.001,
                              min: 0,
                              style: { textAlign: 'center', fontSize: '0.875rem' }
                            }}
                            sx={{
                              width: 80,
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Typography
                            variant="body2"
                            fontWeight={600}
                            sx={{
                              color: item.variance >= 0 ? '#ef4444' : '#10b981'
                            }}
                          >
                            {item.variance >= 0 ? '+' : ''}{item.variance.toFixed(3)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">₹{item.rate.toLocaleString()}</TableCell>
                        <TableCell align="center" sx={{ fontWeight: 600 }}>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{item.actualAmount.toLocaleString()}
                          </Typography>
                          {item.varianceAmount !== 0 && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: item.varianceAmount >= 0 ? '#ef4444' : '#10b981',
                                display: 'block'
                              }}
                            >
                              {item.varianceAmount >= 0 ? '+' : ''}₹{Math.abs(item.varianceAmount).toLocaleString()}
                            </Typography>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Variance Summary */}
            {costs.totalVariance !== 0 && (
              <Box sx={{ mt: 3, p: 2, borderRadius: 2, backgroundColor: costs.totalVariance >= 0 ? 'rgba(239, 68, 68, 0.1)' : 'rgba(16, 185, 129, 0.1)' }}>
                <Typography variant="h6" fontWeight={700} sx={{ color: costs.totalVariance >= 0 ? '#dc2626' : '#059669', mb: 1 }}>
                  {costs.totalVariance >= 0 ? '⚠️ Cost Overrun' : '✅ Cost Savings'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  Expected Total: ₹{costs.expectedTotal.toLocaleString()} |
                  Actual Total: ₹{costs.total.toLocaleString()} |
                  Variance: {costs.totalVariance >= 0 ? '+' : ''}₹{Math.abs(costs.totalVariance).toLocaleString()}
                  ({costs.totalVariance >= 0 ? '+' : ''}{costs.totalVariancePercentage.toFixed(1)}%)
                </Typography>
              </Box>
            )}
          </AccordionDetails>
        </Accordion>
      </Paper>
    );
  };

  return (
    <Box>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" fontWeight={700} sx={{ color: '#1e293b' }}>
            📅 Daily Activity Log
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{
              borderRadius: 3,
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              boxShadow: '0 8px 24px rgba(240, 147, 251, 0.3)',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 32px rgba(240, 147, 251, 0.4)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            Log Today's Work
          </Button>
        </Box>

        {/* Activity Logs */}
        <Grid container spacing={3}>
          {logs.map((log, index) => (
            <Grid item xs={12} lg={6} key={log.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    borderRadius: 4,
                    background: `
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.95) 0%,
                        rgba(248, 250, 252, 0.9) 100%
                      )
                    `,
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(148, 163, 184, 0.2)',
                    boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 32px 64px -12px rgba(15, 23, 42, 0.25)',
                    },
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mb: 1 }}>
                          {log.gradeName}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                          <Chip 
                            icon={<CalendarIcon />}
                            label={new Date(log.date).toLocaleDateString()} 
                            size="small" 
                            color="primary" 
                            variant="outlined"
                          />
                          <Chip 
                            label={log.status} 
                            size="small" 
                            color={getStatusColor(log.status)}
                          />
                        </Box>
                      </Box>
                      <IconButton
                        onClick={() => handleOpenDialog(log)}
                        sx={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white',
                          width: 36,
                          height: 36,
                          '&:hover': {
                            transform: 'scale(1.1)',
                          },
                        }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Box>

                    <Typography variant="body2" sx={{ mb: 2, color: '#64748b' }}>
                      {log.workDescription}
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Quantity Completed</Typography>
                        <Typography variant="h6" fontWeight={600}>
                          {log.quantityCompleted} {log.unit}
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="body2" color="text.secondary">Total Cost</Typography>
                        <Typography 
                          variant="h6" 
                          fontWeight={600}
                          sx={{ color: 'success.main' }}
                        >
                          ₹{log.totalCost?.toLocaleString() || '0'}
                        </Typography>
                      </Box>
                    </Box>

                    {log.projectName && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Project</Typography>
                        <Typography variant="body2" fontWeight={500}>
                          {log.projectName} {log.location && `• ${log.location}`}
                        </Typography>
                      </Box>
                    )}

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="caption" color="text.secondary">Materials</Typography>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{log.totalMaterialCost?.toLocaleString() || '0'}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="caption" color="text.secondary">Labour</Typography>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{log.totalLabourCost?.toLocaleString() || '0'}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="caption" color="text.secondary">Machinery</Typography>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{log.totalMachineryCost?.toLocaleString() || '0'}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Add/Edit Dialog */}
        <PremiumDialog
          open={openDialog}
          onClose={handleCloseDialog}
          title={`${editingLog ? 'Edit' : 'Add New'} Activity Log`}
          titleIcon="📅"
          maxWidth="lg"
          actions={
            <>
              <Button
                onClick={handleCloseDialog}
                startIcon={<CancelIcon />}
                sx={{
                  borderRadius: 2,
                  color: '#64748b',
                  borderColor: '#e2e8f0',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    backgroundColor: '#f8fafc',
                  }
                }}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                variant="contained"
                startIcon={<SaveIcon />}
                disabled={!formData.gradeId || !formData.workDescription || !formData.quantityCompleted}
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #f5576c 0%, #f093fb 100%)',
                  },
                }}
              >
                {editingLog ? 'Update' : 'Save'} Activity
              </Button>
            </>
          }
        >
          <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Date"
                  type="date"
                  value={formData.date ? formData.date.toISOString().split('T')[0] : ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: new Date(e.target.value) }))}
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  select
                  label="Concrete Grade"
                  value={formData.gradeId}
                  onChange={(e) => handleGradeChange(e.target.value)}
                  fullWidth
                  required
                  sx={{ mb: 1 }}
                >
                  {grades.map((grade) => (
                    <MenuItem key={grade.id} value={grade.id}>
                      {grade.name} (₹{grade.ratePerUnit?.toLocaleString()}/{grade.unit})
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Work Description"
                  value={formData.workDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, workDescription: e.target.value }))}
                  fullWidth
                  multiline
                  rows={2}
                  required
                  placeholder="Describe the work completed today..."
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={`Quantity Completed ${selectedGrade ? `(${selectedGrade.unit})` : '(Cum)'}`}
                  type="number"
                  value={formData.quantityCompleted}
                  onChange={(e) => {
                    const newQuantity = parseFloat(e.target.value) || 1; // Default to 1 if empty
                    setFormData(prev => ({ ...prev, quantityCompleted: newQuantity }));

                    // Update actual quantities when quantity completed changes
                    if (selectedGrade && newQuantity > 0) {
                      const updatedQuantities = {};
                      [...selectedGrade.materials, ...selectedGrade.labour, ...selectedGrade.machinery].forEach(item => {
                        const key = `${item.category}-${item.id}`;
                        updatedQuantities[key] = item.quantity * newQuantity;
                      });
                      setActualMaterialQuantities(updatedQuantities);
                    }
                  }}
                  fullWidth
                  required
                  inputProps={{ step: 0.01, min: 1 }} // Minimum value of 1
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  select
                  label="Status"
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="verified">Verified</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Project Name"
                  value={formData.projectName}
                  onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
                  fullWidth
                  placeholder="Optional project name"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  fullWidth
                  placeholder="Work location"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Contractor"
                  value={formData.contractor}
                  onChange={(e) => setFormData(prev => ({ ...prev, contractor: e.target.value }))}
                  fullWidth
                  placeholder="Contractor name"
                  sx={{ mb: 1 }}
                />
              </Grid>
            </Grid>

            {/* Cost Preview */}
            {renderCostBreakdown()}
        </PremiumDialog>
      </Box>
    );
};

export default DailyActivityLog;
