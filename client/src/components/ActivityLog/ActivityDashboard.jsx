import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Construction as ConstructionIcon,
  AccountBalance as AccountBalanceIcon,
  Engineering as EngineeringIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  CalendarToday as CalendarIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

const ActivityDashboard = ({ data }) => {
  const [loading, setLoading] = useState(false);

  // Sample data for charts (replace with actual data)
  const costBreakdownData = [
    { name: 'Materials', value: data?.summary?.totalMaterialCost || 0, color: '#4facfe' },
    { name: 'Labour', value: data?.summary?.totalLabourCost || 0, color: '#f093fb' },
    { name: 'Machinery', value: data?.summary?.totalMachineryCost || 0, color: '#43e97b' }
  ];

  const monthlyTrendData = [
    { month: 'Jan', cost: 45000, quantity: 12 },
    { month: 'Feb', cost: 52000, quantity: 15 },
    { month: 'Mar', cost: 48000, quantity: 13 },
    { month: 'Apr', cost: 61000, quantity: 18 },
    { month: 'May', cost: 55000, quantity: 16 },
    { month: 'Jun', cost: 67000, quantity: 20 }
  ];

  const gradeWiseData = data?.gradeWiseSummary || [];

  const StatCard = ({ title, value, subtitle, icon, color, gradient }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card
        sx={{
          borderRadius: 4,
          background: `
            linear-gradient(135deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 100%
            )
          `,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${color}20`,
          boxShadow: `0 20px 40px -12px ${color}25`,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: gradient,
          },
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: `0 32px 64px -12px ${color}35`,
          },
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" sx={{ color: '#64748b', mb: 1, fontWeight: 500 }}>
                {title}
              </Typography>
              <Typography 
                variant="h4" 
                fontWeight={700}
                sx={{ 
                  color: '#1e293b',
                  mb: 0.5,
                  background: gradient,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                {value}
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b' }}>
                {subtitle}
              </Typography>
            </Box>
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: 3,
                background: gradient,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: `0 12px 32px ${color}40`,
              }}
            >
              {icon}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h5" fontWeight={700} sx={{ color: '#1e293b' }}>
          📊 Activity Dashboard
        </Typography>
        <Tooltip title="Refresh Data">
          <IconButton
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              width: 48,
              height: 48,
              '&:hover': {
                transform: 'rotate(180deg)',
              },
              transition: 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Activities"
            value={data?.summary?.totalLogs || 0}
            subtitle="Logged this period"
            icon={<ConstructionIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="#667eea"
            gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Cost"
            value={`₹${(data?.summary?.totalCost || 0).toLocaleString()}`}
            subtitle="All activities combined"
            icon={<AccountBalanceIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="#f093fb"
            gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Quantity"
            value={`${(data?.summary?.totalQuantity || 0).toFixed(1)} Cum`}
            subtitle="Work completed"
            icon={<EngineeringIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="#4facfe"
            gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Avg Rate"
            value={`₹${data?.summary?.totalQuantity ? Math.round(data.summary.totalCost / data.summary.totalQuantity).toLocaleString() : 0}`}
            subtitle="Per Cum"
            icon={<TrendingUpIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="#43e97b"
            gradient="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"
          />
        </Grid>
      </Grid>

      <Grid container spacing={4}>
        {/* Cost Breakdown Chart */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Paper
              sx={{
                p: 3,
                borderRadius: 4,
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(248, 250, 252, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(148, 163, 184, 0.2)',
                boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
              }}
            >
              <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b' }}>
                💰 Cost Breakdown
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={costBreakdownData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {costBreakdownData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip 
                      formatter={(value) => [`₹${value.toLocaleString()}`, 'Cost']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
                {costBreakdownData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                      }}
                    />
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      {item.name}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Paper>
          </motion.div>
        </Grid>

        {/* Monthly Trend */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Paper
              sx={{
                p: 3,
                borderRadius: 4,
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(248, 250, 252, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(148, 163, 184, 0.2)',
                boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
              }}
            >
              <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b' }}>
                📈 Monthly Trend
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={monthlyTrendData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                    <XAxis dataKey="month" stroke="#64748b" />
                    <YAxis stroke="#64748b" />
                    <RechartsTooltip 
                      formatter={(value, name) => [
                        name === 'cost' ? `₹${value.toLocaleString()}` : `${value} Cum`,
                        name === 'cost' ? 'Cost' : 'Quantity'
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="cost"
                      stroke="#4facfe"
                      fill="url(#colorCost)"
                      strokeWidth={3}
                    />
                    <defs>
                      <linearGradient id="colorCost" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#4facfe" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#4facfe" stopOpacity={0.05}/>
                      </linearGradient>
                    </defs>
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </motion.div>
        </Grid>

        {/* Grade-wise Summary */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Paper
              sx={{
                p: 3,
                borderRadius: 4,
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(248, 250, 252, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(148, 163, 184, 0.2)',
                boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
              }}
            >
              <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b' }}>
                🏗️ Grade-wise Performance
              </Typography>
              {gradeWiseData.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No activity data available yet
                  </Typography>
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Grade</TableCell>
                        <TableCell align="center">Activities</TableCell>
                        <TableCell align="center">Quantity</TableCell>
                        <TableCell align="center">Total Cost</TableCell>
                        <TableCell align="center">Avg Rate</TableCell>
                        <TableCell align="center">Progress</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {gradeWiseData.map((grade, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="body2" fontWeight={600}>
                              {grade._id}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Chip 
                              label={grade.logCount} 
                              size="small" 
                              color="primary" 
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Typography variant="body2">
                              {grade.totalQuantity.toFixed(1)} Cum
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Typography variant="body2" fontWeight={600} color="success.main">
                              ₹{grade.totalCost.toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Typography variant="body2">
                              ₹{Math.round(grade.totalCost / grade.totalQuantity).toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell align="center" sx={{ width: 120 }}>
                            <LinearProgress
                              variant="determinate"
                              value={Math.min((grade.totalCost / Math.max(...gradeWiseData.map(g => g.totalCost))) * 100, 100)}
                              sx={{
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: 'rgba(79, 172, 254, 0.1)',
                                '& .MuiLinearProgress-bar': {
                                  borderRadius: 4,
                                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                                },
                              }}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Paper>
          </motion.div>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Paper
              sx={{
                p: 3,
                borderRadius: 4,
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(248, 250, 252, 0.9) 100%
                  )
                `,
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(148, 163, 184, 0.2)',
                boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
              }}
            >
              <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b' }}>
                🕒 Recent Activities
              </Typography>
              {!data?.recentActivities || data.recentActivities.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    No recent activities
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                  {data.recentActivities.map((activity, index) => (
                    <Box key={activity.id} sx={{ mb: 2 }}>
                      <Card
                        sx={{
                          borderRadius: 2,
                          background: 'rgba(248, 250, 252, 0.5)',
                          border: '1px solid rgba(148, 163, 184, 0.1)',
                        }}
                      >
                        <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                            <Typography variant="body2" fontWeight={600} sx={{ color: '#1e293b' }}>
                              {activity.gradeName}
                            </Typography>
                            <Chip 
                              label={activity.status} 
                              size="small" 
                              color={activity.status === 'completed' ? 'success' : 'warning'}
                              sx={{ fontSize: '0.75rem' }}
                            />
                          </Box>
                          <Typography variant="body2" sx={{ color: '#64748b', mb: 1, fontSize: '0.875rem' }}>
                            {activity.workDescription}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                              {new Date(activity.date).toLocaleDateString()}
                            </Typography>
                            <Typography variant="body2" fontWeight={600} sx={{ color: 'success.main', fontSize: '0.875rem' }}>
                              ₹{activity.totalCost?.toLocaleString() || '0'}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Box>
                  ))}
                </Box>
              )}
            </Paper>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ActivityDashboard;
