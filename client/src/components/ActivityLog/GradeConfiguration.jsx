import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  Tooltip,
  Fab,
  Zoom
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,

  Construction as ConstructionIcon,
  Calculate as CalculateIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

const GradeConfiguration = ({ onDataChange }) => {
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingGrade, setEditingGrade] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    unit: 'Cum',
    description: '',
    materials: [
      { id: '1', name: 'Cement', unit: 'ton', quantity: 0.281, rate: 3780.00, category: 'material', isActive: true, order: 0 }
    ],
    labour: [
      { id: '1', name: 'Mason (1st Class)', unit: 'day', quantity: 0.25, rate: 400.00, category: 'labour', isActive: true, order: 0 }
    ],
    machinery: [
      { id: '1', name: 'Concrete mixer 0.4/0.28 cum capacity', unit: 'hour', quantity: 1.00, rate: 500.00, category: 'machinery', isActive: true, order: 0 }
    ],
    overheadPercentage: 10.0
  });

  // Available units for different categories
  const units = {
    materials: ['ton', 'CubicMeter', 'Kg', 'bag', 'cft'],
    labour: ['day', 'hour', 'shift'],
    machinery: ['hour', 'day', 'shift']
  };

  useEffect(() => {
    fetchGrades();
  }, []);

  const fetchGrades = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5017/api/activity-log/grades');
      const data = await response.json();
      if (data.success) {
        setGrades(data.grades);
      }
    } catch (error) {
      console.error('Error fetching grades:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (grade = null) => {
    if (grade) {
      setEditingGrade(grade);
      setFormData({
        name: grade.name,
        unit: grade.unit,
        description: grade.description || '',
        materials: grade.materials || [],
        labour: grade.labour || [],
        machinery: grade.machinery || [],
        overheadPercentage: grade.overheadPercentage || 10.0
      });
    } else {
      setEditingGrade(null);
      setFormData({
        name: '',
        unit: 'Cum',
        description: '',
        materials: [
          { id: Date.now().toString(), name: 'Cement', unit: 'ton', quantity: 0.281, rate: 3780.00, category: 'material', isActive: true, order: 0 }
        ],
        labour: [
          { id: Date.now().toString(), name: 'Mason (1st Class)', unit: 'day', quantity: 0.25, rate: 400.00, category: 'labour', isActive: true, order: 0 }
        ],
        machinery: [
          { id: Date.now().toString(), name: 'Concrete mixer', unit: 'hour', quantity: 1.00, rate: 500.00, category: 'machinery', isActive: true, order: 0 }
        ],
        overheadPercentage: 10.0
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingGrade(null);
  };

  const addItem = (category) => {
    const newItem = {
      id: Date.now().toString(),
      name: '',
      unit: units[category][0],
      quantity: 0,
      rate: 0,
      category,
      isActive: true,
      order: formData[category].length
    };
    
    setFormData(prev => ({
      ...prev,
      [category]: [...prev[category], newItem]
    }));
  };

  const removeItem = (category, id) => {
    setFormData(prev => ({
      ...prev,
      [category]: prev[category].filter(item => item.id !== id)
    }));
  };

  const updateItem = (category, id, field, value) => {
    setFormData(prev => ({
      ...prev,
      [category]: prev[category].map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    }));
  };

  const calculateAmount = (quantity, rate) => {
    return (parseFloat(quantity) || 0) * (parseFloat(rate) || 0);
  };

  const calculateTotals = () => {
    const materialTotal = formData.materials.reduce((sum, item) => 
      sum + calculateAmount(item.quantity, item.rate), 0
    );
    const labourTotal = formData.labour.reduce((sum, item) => 
      sum + calculateAmount(item.quantity, item.rate), 0
    );
    const machineryTotal = formData.machinery.reduce((sum, item) => 
      sum + calculateAmount(item.quantity, item.rate), 0
    );
    
    const subtotal = materialTotal + labourTotal + machineryTotal;
    const overheadAmount = (subtotal * formData.overheadPercentage) / 100;
    const total = subtotal + overheadAmount;

    return {
      materialTotal,
      labourTotal,
      machineryTotal,
      subtotal,
      overheadAmount,
      total
    };
  };

  const handleSave = async () => {
    try {
      const totals = calculateTotals();
      const payload = {
        ...formData,
        createdBy: 'current-user' // Replace with actual user ID
      };

      const url = editingGrade 
        ? `http://localhost:5017/api/activity-log/grades/${editingGrade.id}`
        : 'http://localhost:5017/api/activity-log/grades';
      
      const method = editingGrade ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      if (data.success) {
        fetchGrades();
        handleCloseDialog();
        if (onDataChange) onDataChange();
      }
    } catch (error) {
      console.error('Error saving grade:', error);
    }
  };

  const renderItemSection = (category, title, icon) => {
    const items = formData[category];
    const totals = calculateTotals();
    const categoryTotal = category === 'materials' ? totals.materialTotal : 
                         category === 'labour' ? totals.labourTotal : totals.machineryTotal;

    return (
      <Accordion defaultExpanded sx={{ mb: 2, borderRadius: 2, '&:before': { display: 'none' } }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            background: 'linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%)',
            borderRadius: 2,
            mb: 1
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
            {icon}
            <Typography variant="h6" fontWeight={600}>{title}</Typography>
            <Chip 
              label={`₹${categoryTotal.toLocaleString()}`} 
              color="primary" 
              size="small"
              sx={{ ml: 'auto' }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2 }}>
            <Button
              startIcon={<AddIcon />}
              onClick={() => addItem(category)}
              variant="outlined"
              size="small"
              sx={{ borderRadius: 2 }}
            >
              Add {title.slice(0, -1)}
            </Button>
          </Box>
          
          {items.map((item, index) => (
            <Card key={item.id} sx={{ mb: 2, borderRadius: 2 }}>
              <CardContent sx={{ p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={3}>
                    <TextField
                      label="Name"
                      value={item.name}
                      onChange={(e) => updateItem(category, item.id, 'name', e.target.value)}
                      fullWidth
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={6} sm={2}>
                    <TextField
                      select
                      label="Unit"
                      value={item.unit}
                      onChange={(e) => updateItem(category, item.id, 'unit', e.target.value)}
                      fullWidth
                      size="small"
                      SelectProps={{ native: true }}
                    >
                      {units[category].map(unit => (
                        <option key={unit} value={unit}>{unit}</option>
                      ))}
                    </TextField>
                  </Grid>
                  <Grid item xs={6} sm={2}>
                    <TextField
                      label="Quantity"
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateItem(category, item.id, 'quantity', parseFloat(e.target.value) || 0)}
                      fullWidth
                      size="small"
                      inputProps={{ step: 0.001 }}
                    />
                  </Grid>
                  <Grid item xs={6} sm={2}>
                    <TextField
                      label="Rate"
                      type="number"
                      value={item.rate}
                      onChange={(e) => updateItem(category, item.id, 'rate', parseFloat(e.target.value) || 0)}
                      fullWidth
                      size="small"
                      inputProps={{ step: 0.01 }}
                    />
                  </Grid>
                  <Grid item xs={6} sm={2}>
                    <TextField
                      label="Amount"
                      value={calculateAmount(item.quantity, item.rate).toFixed(2)}
                      fullWidth
                      size="small"
                      InputProps={{ readOnly: true }}
                      sx={{ '& .MuiInputBase-input': { fontWeight: 600, color: 'primary.main' } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={1}>
                    <IconButton
                      onClick={() => removeItem(category, item.id)}
                      color="error"
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          ))}
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Box>
      {/* Header with Add Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" fontWeight={700} sx={{ color: '#1e293b' }}>
          🏗️ Concrete Grade Configuration
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            borderRadius: 3,
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            boxShadow: '0 8px 24px rgba(79, 172, 254, 0.3)',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 12px 32px rgba(79, 172, 254, 0.4)',
            },
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          }}
        >
          Add New Grade
        </Button>
      </Box>

      {/* Grades List */}
      <Grid container spacing={3}>
        {grades.map((grade, index) => (
          <Grid item xs={12} md={6} lg={4} key={grade.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <Card
                sx={{
                  borderRadius: 4,
                  background: `
                    linear-gradient(135deg,
                      rgba(255, 255, 255, 0.95) 0%,
                      rgba(248, 250, 252, 0.9) 100%
                    )
                  `,
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(148, 163, 184, 0.2)',
                  boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 32px 64px -12px rgba(15, 23, 42, 0.25)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mb: 1 }}>
                        {grade.name}
                      </Typography>
                      <Chip 
                        label={`Unit: ${grade.unit}`} 
                        size="small" 
                        color="primary" 
                        variant="outlined"
                      />
                    </Box>
                    <IconButton
                      onClick={() => handleOpenDialog(grade)}
                      sx={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: 'white',
                        width: 36,
                        height: 36,
                        '&:hover': {
                          transform: 'scale(1.1)',
                        },
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {grade.description}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">Materials:</Typography>
                    <Typography variant="body2" fontWeight={600}>
                      ₹{grade.totalMaterialCost?.toLocaleString() || '0'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">Labour:</Typography>
                    <Typography variant="body2" fontWeight={600}>
                      ₹{grade.totalLabourCost?.toLocaleString() || '0'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">Machinery:</Typography>
                    <Typography variant="body2" fontWeight={600}>
                      ₹{grade.totalMachineryCost?.toLocaleString() || '0'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Overhead ({grade.overheadPercentage}%):
                    </Typography>
                    <Typography variant="body2" fontWeight={600}>
                      ₹{grade.overheadAmount?.toLocaleString() || '0'}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
                      Rate per {grade.unit}:
                    </Typography>
                    <Typography 
                      variant="h6" 
                      fontWeight={700} 
                      sx={{ 
                        color: 'primary.main',
                        background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                      }}
                    >
                      ₹{grade.ratePerUnit?.toLocaleString() || '0'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Add/Edit Dialog */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog} 
        maxWidth="lg" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 100%
              )
            `,
            backdropFilter: 'blur(40px)',
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h5" fontWeight={700}>
            {editingGrade ? 'Edit' : 'Add New'} Concrete Grade
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 2 }}>
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Grade Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                fullWidth
                placeholder="e.g., P.C.C M10 Grade"
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Unit"
                value={formData.unit}
                onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                fullWidth
                SelectProps={{ native: true }}
              >
                <option value="Cum">Cum</option>
                <option value="Sqm">Sqm</option>
                <option value="Rmt">Rmt</option>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                label="Overhead %"
                type="number"
                value={formData.overheadPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, overheadPercentage: parseFloat(e.target.value) || 0 }))}
                fullWidth
                inputProps={{ step: 0.1 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                fullWidth
                multiline
                rows={2}
                placeholder="Brief description of the concrete grade..."
              />
            </Grid>
          </Grid>

          {/* Dynamic Sections */}
          {renderItemSection('materials', 'Materials', <ConstructionIcon color="primary" />)}
          {renderItemSection('labour', 'Labour', <ConstructionIcon color="secondary" />)}
          {renderItemSection('machinery', 'Machinery', <ConstructionIcon color="success" />)}

          {/* Cost Summary */}
          <Paper
            sx={{
              p: 3,
              mt: 3,
              borderRadius: 3,
              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
              border: '1px solid rgba(102, 126, 234, 0.2)',
            }}
          >
            <Typography variant="h6" fontWeight={700} sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <CalculateIcon color="primary" />
              Cost Summary
            </Typography>
            <Grid container spacing={2}>
              {(() => {
                const totals = calculateTotals();
                return (
                  <>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">Materials</Typography>
                        <Typography variant="h6" fontWeight={600}>₹{totals.materialTotal.toLocaleString()}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">Labour</Typography>
                        <Typography variant="h6" fontWeight={600}>₹{totals.labourTotal.toLocaleString()}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">Machinery</Typography>
                        <Typography variant="h6" fontWeight={600}>₹{totals.machineryTotal.toLocaleString()}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">Overhead</Typography>
                        <Typography variant="h6" fontWeight={600}>₹{totals.overheadAmount.toLocaleString()}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">Total Rate per {formData.unit}</Typography>
                        <Typography 
                          variant="h4" 
                          fontWeight={700}
                          sx={{ 
                            color: 'primary.main',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            backgroundClip: 'text',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                          }}
                        >
                          ₹{totals.total.toLocaleString()}
                        </Typography>
                      </Box>
                    </Grid>
                  </>
                );
              })()}
            </Grid>
          </Paper>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button 
            onClick={handleCloseDialog}
            startIcon={<CancelIcon />}
            sx={{ borderRadius: 2 }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSave}
            variant="contained"
            startIcon={<SaveIcon />}
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #38f9d7 0%, #43e97b 100%)',
              },
            }}
          >
            {editingGrade ? 'Update' : 'Create'} Grade
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GradeConfiguration;
