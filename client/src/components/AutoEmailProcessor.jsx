import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Email as EmailIcon,
  AutoFixHigh as AutoIcon,
  CheckCircle as CheckIcon,
  <PERSON>rror as ErrorIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  AttachFile as AttachIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

const AutoEmailProcessor = ({ open, onClose, requestId = null }) => {
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [processingResults, setProcessingResults] = useState(null);
  const [emailQuotations, setEmailQuotations] = useState([]);
  const [pendingRequests, setPendingRequests] = useState([]);
  const [step, setStep] = useState('check'); // 'check', 'process', 'results', 'analysis'
  const [analysisResults, setAnalysisResults] = useState(null);

  useEffect(() => {
    if (open) {
      loadPendingRequests();
      if (!requestId) {
        checkEmailQuotations();
      }
    }
  }, [open, requestId]);

  const loadPendingRequests = async () => {
    try {
      const response = await fetch('http://localhost:5002/api/procurement/email/pending-requests');
      const data = await response.json();
      
      if (data.success) {
        setPendingRequests(data.pending_requests);
      }
    } catch (error) {
      console.error('Error loading pending requests:', error);
    }
  };

  const checkEmailQuotations = async () => {
    setChecking(true);
    try {
      const requestBody = { days_back: 7 };

      // If we have a specific requestId, include it for targeted email checking
      if (requestId) {
        requestBody.request_id = requestId;
      }

      const response = await fetch('http://localhost:5002/api/procurement/email/check-quotations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (data.success) {
        setEmailQuotations(data.quotations);
        setStep('check');
      } else {
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      alert(`❌ Error checking emails: ${error.message}`);
    }
    setChecking(false);
  };

  const processEmailQuotations = async () => {
    setLoading(true);
    setStep('process');

    try {
      // Use the new RFQ-specific endpoint if we have a requestId, otherwise use the general endpoint
      const endpoint = requestId
        ? 'http://localhost:5002/api/procurement/email/process-rfq-emails'
        : 'http://localhost:5002/api/procurement/email/auto-process';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ request_id: requestId }),
      });

      const data = await response.json();

      if (data.success) {
        setProcessingResults(data);
        setStep('results');

        // Show success message with targeted processing info
        const processingType = data.targeted_processing ? 'Targeted RFQ' : 'General';
        const message = `✅ ${processingType} Processing: ${data.message}\n📊 Processed: ${data.processed_count} quotations\n🤖 AI Analysis: ${data.analysis_available ? 'Completed' : 'Pending'}`;
        alert(message);
      } else {
        alert(`❌ Error: ${data.error}`);
        setStep('check');
      }
    } catch (error) {
      alert(`❌ Error processing emails: ${error.message}`);
      setStep('check');
    }
    setLoading(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const handleViewAnalysisResults = async () => {
    if (!requestId) {
      // If no specific request ID, navigate to quotation manager
      window.location.href = '/app/quotation-manager';
      return;
    }

    setLoading(true);
    try {
      // Run AI analysis for the specific request
      const response = await fetch(`http://localhost:5002/api/procurement/analyze/${requestId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      const data = await response.json();

      // Show success message
      alert(`🤖 AI Analysis completed! Recommended: ${data.analysis_summary.recommended_vendor}`);

      // Close the dialog and navigate directly to QuotationManager with the request ID
      handleClose();
      window.location.href = `/app/quotation-manager?request=${requestId}`;

    } catch (error) {
      alert(`❌ Error running analysis: ${error.message}`);
    }
    setLoading(false);
  };

  const handleClose = () => {
    setStep('check');
    setProcessingResults(null);
    setAnalysisResults(null);
    setEmailQuotations([]);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: 'linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%)',
        }
      }}
    >
      <DialogTitle sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <AutoIcon />
        <Box>
          <Typography variant="h6" fontWeight={700}>
            📧 {requestId ? 'Targeted RFQ' : 'Automated'} Email Quotation Processing
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            {requestId
              ? `Read vendor email replies specifically for this RFQ and analyze quotations`
              : `Read vendor email replies and analyze quotations automatically`
            }
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* Step 1: Check Emails */}
        {step === 'check' && (
          <Box>
            {/* Pending Requests Summary */}
            {pendingRequests.length > 0 && (
              <Card sx={{ mb: 3, borderRadius: 2, border: '1px solid #e3f2fd' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <ScheduleIcon color="primary" />
                    Pending Quotation Requests ({pendingRequests.length})
                  </Typography>
                  <Grid container spacing={2}>
                    {pendingRequests.map((request, index) => (
                      <Grid item xs={12} md={6} key={index}>
                        <Box sx={{ 
                          p: 2, 
                          borderRadius: 2, 
                          background: 'rgba(25, 118, 210, 0.05)',
                          border: '1px solid rgba(25, 118, 210, 0.2)'
                        }}>
                          <Typography variant="subtitle2" fontWeight={600}>
                            {request.category}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {request.request_id}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                            <Chip 
                              label={`${request.quotations_received} quotations`}
                              size="small"
                              color={request.quotations_received > 0 ? 'success' : 'default'}
                            />
                            <Typography variant="caption">
                              {new Date(request.created_at).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            )}

            {/* Email Quotations Found */}
            <Card sx={{ borderRadius: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EmailIcon color="primary" />
                    Email Quotations Found ({emailQuotations.length})
                    {requestId && (
                      <Chip
                        label="Filtered by RFQ"
                        size="small"
                        color="primary"
                        sx={{ ml: 1, fontSize: '0.7rem' }}
                      />
                    )}
                  </Typography>
                  <Tooltip title="Refresh email check">
                    <IconButton onClick={checkEmailQuotations} disabled={checking}>
                      {checking ? <CircularProgress size={20} /> : <RefreshIcon />}
                    </IconButton>
                  </Tooltip>
                </Box>

                {emailQuotations.length === 0 ? (
                  <Alert severity="info" sx={{ borderRadius: 2 }}>
                    <Typography variant="body2">
                      {requestId
                        ? `No quotation emails found for this specific RFQ in the last 7 days.`
                        : `No quotation emails found in the last 7 days.`
                      } Make sure:
                    </Typography>
                    <Box component="ul" sx={{ mt: 1, mb: 0 }}>
                      <li>Vendors have replied to RFQ emails</li>
                      <li>Email attachments contain quotation documents</li>
                      <li>Vendor email addresses are registered in the system</li>
                      {requestId && <li>Email subjects contain the correct RFQ ID</li>}
                    </Box>
                  </Alert>
                ) : (
                  <List>
                    {emailQuotations.map((quotation, index) => (
                      <React.Fragment key={index}>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <AttachIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="subtitle2" fontWeight={600}>
                                  {quotation.vendor_name}
                                </Typography>
                                {quotation.has_request_id ? (
                                  <Chip label={`REQ: ${quotation.request_id}`} size="small" color="success" />
                                ) : (
                                  <Chip label="No Request ID" size="small" color="warning" />
                                )}
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  📎 {quotation.attachment_name}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  📧 {quotation.subject}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                  🕒 {formatDate(quotation.received_date)}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < emailQuotations.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Box>
        )}

        {/* Step 2: Processing */}
        {step === 'process' && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CircularProgress size={60} sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>
              🤖 Processing Email Quotations...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Reading emails, extracting data, and running AI analysis
            </Typography>
            <LinearProgress sx={{ mt: 3, borderRadius: 2 }} />
          </Box>
        )}

        {/* Step 3: Results */}
        {step === 'results' && processingResults && (
          <Box>
            <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                ✅ Processing Complete!
              </Typography>
              <Typography variant="body2">
                {processingResults.message}
              </Typography>
            </Alert>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ borderRadius: 2, border: '1px solid #4caf50' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <CheckIcon color="success" />
                      Processed Quotations
                    </Typography>
                    <Typography variant="h4" color="success.main" fontWeight={700}>
                      {processingResults.processed_count}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Quotations successfully processed
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card sx={{ borderRadius: 2, border: '1px solid #2196f3' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <AnalyticsIcon color="primary" />
                      AI Analysis
                    </Typography>
                    <Typography variant="h4" color="primary.main" fontWeight={700}>
                      {processingResults.analysis_available ? 'Ready' : 'Pending'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {processingResults.analysis_available ? 'Analysis completed' : 'Need more quotations'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {processingResults.quotations_by_request && (
              <Card sx={{ mt: 3, borderRadius: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    📊 Quotations by Request
                  </Typography>
                  {Object.entries(processingResults.quotations_by_request).map(([requestId, count]) => (
                    <Box key={requestId} sx={{ display: 'flex', justifyContent: 'space-between', py: 1 }}>
                      <Typography variant="body2">Request {requestId}</Typography>
                      <Chip label={`${count} quotations`} size="small" color="primary" />
                    </Box>
                  ))}
                </CardContent>
              </Card>
            )}
          </Box>
        )}


      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 2 }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          sx={{ borderRadius: 2, textTransform: 'none', fontWeight: 600 }}
        >
          Close
        </Button>
        
        {step === 'check' && emailQuotations.length > 0 && (
          <Button
            onClick={processEmailQuotations}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <AutoIcon />}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            {loading ? 'Processing...' : 'Process & Analyze'}
          </Button>
        )}

        {step === 'results' && (
          <Button
            onClick={handleViewAnalysisResults}
            variant="contained"
            color="success"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
            sx={{ borderRadius: 2, textTransform: 'none', fontWeight: 600 }}
          >
            {loading ? 'Running Analysis...' : 'View Analysis Results'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AutoEmailProcessor;
