import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  IconButton,
  Fade,
  Backdrop
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Premium styled dialog with enhanced visuals
const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: 24,
    background: `
      linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 250, 252, 0.95) 100%
      )
    `,
    backdropFilter: 'blur(40px)',
    border: '1px solid rgba(148, 163, 184, 0.2)',
    boxShadow: `
      0 32px 64px -12px rgba(15, 23, 42, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `,
    overflow: 'visible',
    position: 'relative',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '1px',
      background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)',
    }
  },
  '& .MuiBackdrop-root': {
    backgroundColor: 'rgba(15, 23, 42, 0.4)',
    backdropFilter: 'blur(8px)',
  }
}));

// Premium styled dialog title
const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  padding: '32px 32px 16px 32px',
  background: `
    linear-gradient(135deg,
      rgba(79, 172, 254, 0.08) 0%,
      rgba(0, 242, 254, 0.05) 100%
    )
  `,
  borderBottom: '1px solid rgba(148, 163, 184, 0.1)',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: 'translateX(-50%)',
    width: '60px',
    height: '2px',
    background: 'linear-gradient(90deg, #4facfe, #00f2fe)',
    borderRadius: '1px',
  }
}));

// Premium styled dialog content with proper spacing
const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  padding: '32px !important', // Override MUI's default padding behavior
  paddingTop: '32px !important', // Force top padding even after DialogTitle
  minHeight: '200px',
  '&.MuiDialogContent-root': {
    paddingTop: '32px !important', // Extra specificity to override MUI defaults
  },
  // Override MUI's default behavior that removes top padding after DialogTitle
  '&.MuiDialogContent-root.MuiDialogContent-root': {
    paddingTop: '32px !important',
  }
}));

// Premium styled dialog actions
const StyledDialogActions = styled(DialogActions)(({ theme }) => ({
  padding: '24px 32px 32px 32px',
  background: `
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.8) 0%,
      rgba(255, 255, 255, 0.6) 100%
    )
  `,
  borderTop: '1px solid rgba(148, 163, 184, 0.1)',
  gap: '12px',
  '& .MuiButton-root': {
    borderRadius: '12px',
    padding: '12px 24px',
    fontWeight: 600,
    textTransform: 'none',
    fontSize: '0.95rem',
    minWidth: '120px',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
    }
  }
}));

const PremiumDialog = ({
  open,
  onClose,
  title,
  children,
  actions,
  maxWidth = 'md',
  fullWidth = true,
  showCloseButton = true,
  titleIcon,
  ...props
}) => {
  return (
    <StyledDialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      TransitionComponent={Fade}
      TransitionProps={{
        timeout: 400,
      }}
      BackdropComponent={Backdrop}
      BackdropProps={{
        timeout: 500,
      }}
      {...props}
    >
      <StyledDialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {titleIcon && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 48,
                  height: 48,
                  borderRadius: '12px',
                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  color: 'white',
                  fontSize: '1.5rem',
                }}
              >
                {titleIcon}
              </Box>
            )}
            <Typography 
              variant="h4" 
              fontWeight={700}
              sx={{ 
                color: '#1e293b',
                fontSize: '1.75rem',
                lineHeight: 1.2,
              }}
            >
              {title}
            </Typography>
          </Box>
          {showCloseButton && (
            <IconButton
              onClick={onClose}
              sx={{
                color: '#64748b',
                backgroundColor: 'rgba(148, 163, 184, 0.1)',
                width: 40,
                height: 40,
                '&:hover': {
                  backgroundColor: 'rgba(239, 68, 68, 0.1)',
                  color: '#ef4444',
                  transform: 'scale(1.1)',
                },
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              }}
            >
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </StyledDialogTitle>

      <StyledDialogContent>
        {children}
      </StyledDialogContent>

      {actions && (
        <StyledDialogActions>
          {actions}
        </StyledDialogActions>
      )}
    </StyledDialog>
  );
};

export default PremiumDialog;
