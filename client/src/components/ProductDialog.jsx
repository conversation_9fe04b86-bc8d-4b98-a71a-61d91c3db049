import React from 'react';
/* eslint-disable react/prop-types */
import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  Fade,
  useTheme,
  Snackbar,
  Alert,
  Grid,
  Stack,
  Avatar,
  Chip,
  Divider,
  alpha,
  Zoom,
  Tooltip,
  Autocomplete,
  Card,
  CardContent,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  AccountBalance as MoneyIcon,
  DateRange as DateIcon,
  QrCode as BarcodeIcon,
  Search as SearchIcon,
  Store as StoreIcon
} from '@mui/icons-material';
import { getApiUrl } from '../config';
import { useTranslation } from 'react-i18next';

const ProductDialog = ({ open, onClose, product, onSave }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    sku: '',
    stock: '',
    lowStockThreshold: '',
    price: '',
    category: '',
    manufacturing_date: '',
    expiry_date: '',
    store_id: '',
    batch_number: '',
    supplier: '',
    // Payment & Tax Information
    basePrice: '',
    gstRate: '18',
    gstAmount: '',
    discount: '0',
    discountType: 'percentage', // 'percentage' or 'fixed'
    finalPrice: '',
    hsn_code: '',
    tax_category: 'taxable' // 'taxable', 'exempt', 'zero_rated'
  });
  const [stores, setStores] = useState([]); // Add state for stores
  const [selectedStoreId, setSelectedStoreId] = useState(''); // Add state for selected store
  const [products, setProducts] = useState([]); // Add state for products
  const [selectedProduct, setSelectedProduct] = useState(null); // Add state for selected product
  const [showSuccess, setShowSuccess] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        sku: product.sku || '',
        stock: product.stock || '',
        lowStockThreshold: product.lowStockThreshold || '',
        price: product.price || '',
        category: product.category || '',
        manufacturing_date: product.manufacturing_date || '',
        expiry_date: product.expiry_date || '',
        store_id: product.store_id || '',
      });
       // Set selected store if product has one
      if (product.storeId) {
        setSelectedStoreId(product.storeId);
      }
    }
  }, [product]);

  // Fetch stores and products when dialog opens
  useEffect(() => {
    if (open) {
      const fetchStores = async () => {
        try {
          const response = await fetch(getApiUrl('api/store'));
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const result = await response.json();
          if (result.success && Array.isArray(result.data)) {
            setStores(result.data);
          } else {
            console.error('Invalid stores data format:', result);
            setStores([]);
          }
        } catch (error) {
          console.error('Error fetching stores:', error);
          setStores([]);
        }
      };

      const fetchProducts = async () => {
        try {
          const response = await fetch(getApiUrl('api/product/'));
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const result = await response.json();
          if (result.success && Array.isArray(result.data)) {
            setProducts(result.data);
          } else {
            console.error('Invalid products data format:', result);
            setProducts([]);
          }
        } catch (error) {
          console.error('Error fetching products:', error);
          setProducts([]);
        }
      };

      fetchStores();
      fetchProducts();
    }
  }, [open]); // Fetch stores and products when dialog open state changes

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStoreChange = (e) => {
    setSelectedStoreId(e.target.value);
  };

  const handleProductChange = (event, newValue) => {
    setSelectedProduct(newValue);
    if (newValue) {
      // Auto-fill form data from selected product
      setFormData(prev => ({
        ...prev,
        name: newValue.name,
        sku: newValue.sku,
        category: newValue.category,
        supplier: newValue.defaultSupplier?.name || prev.supplier,
        // Keep existing stock/price/date fields as they are inventory-specific
      }));
    } else {
      // Clear product-related fields if no product selected
      setFormData(prev => ({
        ...prev,
        name: '',
        sku: '',
        category: '',
        supplier: '',
      }));
    }
  };

  // Calculate GST and final price
  const calculatePricing = () => {
    const basePrice = parseFloat(formData.basePrice) || 0;
    const gstRate = parseFloat(formData.gstRate) || 0;
    const discount = parseFloat(formData.discount) || 0;

    let discountAmount = 0;
    if (formData.discountType === 'percentage') {
      discountAmount = (basePrice * discount) / 100;
    } else {
      discountAmount = discount;
    }

    const discountedPrice = basePrice - discountAmount;
    const gstAmount = (discountedPrice * gstRate) / 100;
    const finalPrice = discountedPrice + gstAmount;

    setFormData(prev => ({
      ...prev,
      gstAmount: gstAmount.toFixed(2),
      finalPrice: finalPrice.toFixed(2),
      price: finalPrice.toFixed(2) // Update main price field
    }));
  };

  // Recalculate when pricing fields change
  useEffect(() => {
    if (formData.basePrice) {
      calculatePricing();
    }
  }, [formData.basePrice, formData.gstRate, formData.discount, formData.discountType]);

  const handleSubmit = async () => {
    if (!product) {
      // Adding new inventory item
      if (!selectedProduct) {
        alert('Please select a product first');
        return;
      }
      if (!selectedStoreId) {
        alert('Please select a store first');
        return;
      }

      try {
        const response = await fetch(getApiUrl('api/inventory/add'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            product_id: selectedProduct._id,
            store_id: selectedStoreId,
            stock: parseInt(formData.stock),
            lowStockThreshold: parseInt(formData.lowStockThreshold),
            price: parseFloat(formData.finalPrice || formData.basePrice),
            basePrice: parseFloat(formData.basePrice),
            gstRate: parseFloat(formData.gstRate),
            gstAmount: parseFloat(formData.gstAmount || 0),
            discount: parseFloat(formData.discount || 0),
            discountType: formData.discountType,
            finalPrice: parseFloat(formData.finalPrice || formData.basePrice),
            hsn_code: formData.hsn_code || '',
            tax_category: formData.tax_category,
            manufacturing_date: formData.manufacturing_date,
            expiry_date: formData.expiry_date || null,
            batch_number: formData.batch_number || '',
            supplier: formData.supplier || selectedProduct.defaultSupplier?.name || ''
          })
        });

        if (response.ok) {
          const data = await response.json();
          onSave(data.data);
          setShowSuccess(true);
          setTimeout(() => {
            onClose();
          }, 1500);
        } else {
          const errorData = await response.json();
          console.error('Failed to add inventory:', errorData);
          alert(errorData.message || 'Failed to add inventory');
        }
      } catch (error) {
        console.error('Error adding inventory:', error);
        alert('Error adding inventory');
      }
    } else {
      // Editing existing inventory item
      onSave(formData);
      onClose();
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        TransitionComponent={Zoom}
        transitionDuration={400}
        PaperProps={{
          sx: {
            borderRadius: 6,
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 30%, #f1f5f9 100%)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 32px 80px rgba(0, 0, 0, 0.12), 0 8px 32px rgba(25, 118, 210, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
              borderRadius: '6px 6px 0 0',
              animation: 'shimmer 3s ease-in-out infinite',
            },
            '@keyframes shimmer': {
              '0%, 100%': { opacity: 1 },
              '50%': { opacity: 0.8 },
            },
          }
        }}
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.03) 50%, rgba(255, 255, 255, 0.1) 100%)',
            borderRadius: '6px 6px 0 0',
            p: 0,
            m: 0,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)',
              animation: 'slideShine 4s ease-in-out infinite',
            },
            '@keyframes slideShine': {
              '0%': { transform: 'translateX(-100%)' },
              '50%': { transform: 'translateX(100%)' },
              '100%': { transform: 'translateX(100%)' },
            },
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ p: 4, position: 'relative', zIndex: 1 }}>
            <Stack direction="row" alignItems="center" spacing={3}>
              <Box sx={{ position: 'relative' }}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
                    width: 64,
                    height: 64,
                    boxShadow: '0 12px 32px rgba(102, 126, 234, 0.4), 0 4px 16px rgba(118, 75, 162, 0.3)',
                    border: '3px solid rgba(255, 255, 255, 0.8)',
                    position: 'relative',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: -3,
                      left: -3,
                      right: -3,
                      bottom: -3,
                      background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',
                      borderRadius: '50%',
                      zIndex: -1,
                      animation: 'rotate 4s linear infinite',
                    },
                    '@keyframes rotate': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                  }}
                >
                  {product ? <EditIcon sx={{ fontSize: 32, color: 'white' }} /> : <AddIcon sx={{ fontSize: 32, color: 'white' }} />}
                </Avatar>
              </Box>
              <Box>
                <Typography
                  variant="h4"
                  fontWeight={800}
                  sx={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 0.5,
                    letterSpacing: '-0.02em',
                  }}
                >
                  {product ? 'Edit Stock' : 'Add New Stock'}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: 'rgba(100, 116, 139, 0.8)',
                    fontWeight: 500,
                    letterSpacing: '0.01em',
                  }}
                >
                  {product ? 'Update stock information with precision' : 'Add new stock to your inventory with smart controls'}
                </Typography>
              </Box>
            </Stack>
            <Tooltip title="Close" arrow>
              <IconButton
                onClick={onClose}
                sx={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%)',
                  border: '1px solid rgba(203, 213, 225, 0.3)',
                  backdropFilter: 'blur(10px)',
                  color: 'rgba(100, 116, 139, 0.8)',
                  width: 44,
                  height: 44,
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)',
                    color: '#dc2626',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 8px 24px rgba(220, 38, 38, 0.2)',
                    border: '1px solid rgba(220, 38, 38, 0.2)',
                  },
                }}
              >
                <CloseIcon sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
          </Stack>
        </DialogTitle>

        <DialogContent sx={{ pt: 4, pb: 3, px: 4 }}>
          <Grid container spacing={4}>
            {/* Product Selection */}
            <Grid item xs={12}>
              <Box
                sx={{
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.03) 100%)',
                  borderRadius: 4,
                  p: 3,
                  border: '1px solid rgba(102, 126, 234, 0.1)',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '2px',
                    background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
                  },
                }}
              >
                <Stack spacing={2}>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Box
                      sx={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        borderRadius: 2,
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
                      }}
                    >
                      <SearchIcon sx={{ color: 'white', fontSize: 24 }} />
                    </Box>
                    <Box>
                      <Typography variant="h6" fontWeight={700} color="#1e293b">
                        Select Product
                      </Typography>
                      <Typography variant="body2" color="rgba(100, 116, 139, 0.8)">
                        Choose from your registered product catalog
                      </Typography>
                    </Box>
                  </Stack>
                  <Autocomplete
                    value={selectedProduct}
                    onChange={handleProductChange}
                    options={products}
                    getOptionLabel={(option) => `${option.name} (${option.sku})`}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Search and select product"
                        placeholder="Type to search products..."
                        variant="outlined"
                        required
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 4,
                            background: 'rgba(255, 255, 255, 0.8)',
                            backdropFilter: 'blur(10px)',
                            border: '2px solid rgba(102, 126, 234, 0.1)',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              border: '2px solid rgba(102, 126, 234, 0.3)',
                              background: 'rgba(255, 255, 255, 0.95)',
                              transform: 'translateY(-1px)',
                              boxShadow: '0 8px 24px rgba(102, 126, 234, 0.15)',
                            },
                            '&.Mui-focused': {
                              border: '2px solid #667eea',
                              background: 'rgba(255, 255, 255, 1)',
                              boxShadow: '0 8px 32px rgba(102, 126, 234, 0.2)',
                            },
                          },
                          '& .MuiInputLabel-root': {
                            fontWeight: 600,
                            color: 'rgba(100, 116, 139, 0.8)',
                            '&.Mui-focused': {
                              color: '#667eea',
                            },
                          },
                        }}
                      />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props} sx={{ p: 0 }}>
                        <Card
                          sx={{
                            width: '100%',
                            mb: 1.5,
                            border: '1px solid rgba(102, 126, 234, 0.1)',
                            borderRadius: 3,
                            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%)',
                            backdropFilter: 'blur(10px)',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            cursor: 'pointer',
                            '&:hover': {
                              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.05) 100%)',
                              borderColor: '#667eea',
                              transform: 'translateY(-2px) scale(1.02)',
                              boxShadow: '0 12px 32px rgba(102, 126, 234, 0.2)',
                            },
                          }}
                        >
                          <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
                            <Stack direction="row" alignItems="center" spacing={3}>
                              <Box sx={{ position: 'relative' }}>
                                <Avatar
                                  sx={{
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    width: 48,
                                    height: 48,
                                    boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)',
                                    border: '2px solid rgba(255, 255, 255, 0.8)',
                                  }}
                                >
                                  <InventoryIcon sx={{ color: 'white', fontSize: 24 }} />
                                </Avatar>
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: -2,
                                    right: -2,
                                    width: 12,
                                    height: 12,
                                    borderRadius: '50%',
                                    background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                                    border: '2px solid white',
                                  }}
                                />
                              </Box>
                              <Box sx={{ flex: 1 }}>
                                <Typography variant="h6" fontWeight={700} color="#1e293b" sx={{ mb: 1 }}>
                                  {option.name}
                                </Typography>
                                <Stack direction="row" spacing={1.5} alignItems="center" sx={{ mb: 1 }}>
                                  <Chip
                                    label={option.sku}
                                    size="small"
                                    sx={{
                                      background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.08) 100%)',
                                      color: '#667eea',
                                      fontWeight: 700,
                                      border: '1px solid rgba(102, 126, 234, 0.2)',
                                      fontSize: '0.75rem',
                                    }}
                                  />
                                  <Chip
                                    label={option.category}
                                    size="small"
                                    sx={{
                                      background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.08) 100%)',
                                      color: '#059669',
                                      fontWeight: 600,
                                      border: '1px solid rgba(16, 185, 129, 0.2)',
                                      fontSize: '0.75rem',
                                    }}
                                  />
                                </Stack>
                                {option.description && (
                                  <Typography variant="body2" color="rgba(100, 116, 139, 0.8)" sx={{ mt: 0.5, fontWeight: 500 }}>
                                    {option.description}
                                  </Typography>
                                )}
                              </Box>
                            </Stack>
                          </CardContent>
                      </Card>
                    </Box>
                  )}
                    noOptionsText="No products found. Please register products first in Product Management."
                    sx={{ width: '100%' }}
                  />
                </Stack>
              </Box>
            </Grid>

            {/* Selected Product Preview */}
            {selectedProduct && (
              <Grid item xs={12}>
                <Card
                  sx={{
                    background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 50%, rgba(102, 126, 234, 0.03) 100%)',
                    border: '2px solid rgba(16, 185, 129, 0.2)',
                    borderRadius: 4,
                    position: 'relative',
                    overflow: 'hidden',
                    boxShadow: '0 8px 32px rgba(16, 185, 129, 0.15)',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      background: 'linear-gradient(90deg, #10b981 0%, #059669 50%, #667eea 100%)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Stack direction="row" alignItems="center" spacing={3}>
                      <Box sx={{ position: 'relative' }}>
                        <Avatar
                          sx={{
                            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                            width: 64,
                            height: 64,
                            boxShadow: '0 12px 32px rgba(16, 185, 129, 0.4)',
                            border: '3px solid rgba(255, 255, 255, 0.9)',
                          }}
                        >
                          <InventoryIcon sx={{ fontSize: 32, color: 'white' }} />
                        </Avatar>
                        <Box
                          sx={{
                            position: 'absolute',
                            top: -4,
                            right: -4,
                            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                            borderRadius: '50%',
                            width: 24,
                            height: 24,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: '2px solid white',
                            boxShadow: '0 4px 12px rgba(245, 158, 11, 0.4)',
                          }}
                        >
                          <Typography variant="caption" fontWeight={800} color="white" sx={{ fontSize: '0.7rem' }}>
                            ✓
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h5" fontWeight={800} color="#1e293b" sx={{ mb: 1 }}>
                          {selectedProduct.name}
                        </Typography>
                        <Stack direction="row" spacing={1.5} alignItems="center" sx={{ mb: 2 }} flexWrap="wrap">
                          <Chip
                            label={`SKU: ${selectedProduct.sku}`}
                            size="medium"
                            sx={{
                              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.1) 100%)',
                              color: '#667eea',
                              fontWeight: 700,
                              border: '1px solid rgba(102, 126, 234, 0.3)',
                              fontSize: '0.8rem',
                            }}
                          />
                          <Chip
                            label={selectedProduct.category}
                            size="medium"
                            sx={{
                              background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.1) 100%)',
                              color: '#059669',
                              fontWeight: 700,
                              border: '1px solid rgba(16, 185, 129, 0.3)',
                              fontSize: '0.8rem',
                            }}
                          />
                          {selectedProduct.defaultSupplier?.name && (
                            <Chip
                              label={`Supplier: ${selectedProduct.defaultSupplier.name}`}
                              size="medium"
                              sx={{
                                background: 'linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.1) 100%)',
                                color: '#d97706',
                                fontWeight: 700,
                                border: '1px solid rgba(245, 158, 11, 0.3)',
                                fontSize: '0.8rem',
                              }}
                            />
                          )}
                        </Stack>
                        {selectedProduct.description && (
                          <Typography variant="body1" color="rgba(100, 116, 139, 0.8)" sx={{ fontWeight: 500, lineHeight: 1.6 }}>
                            {selectedProduct.description}
                          </Typography>
                        )}
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            )}
            {/* Store Selection */}
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <CategoryIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Store Assignment
                  </Typography>
                </Stack>
                <FormControl
                  fullWidth
                  required
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                >
                  <InputLabel>Store</InputLabel>
                  <Select
                    name="storeId"
                    value={selectedStoreId}
                    onChange={handleStoreChange}
                    label="Store"
                  >
                    <MenuItem value=""><em>Select a Store</em></MenuItem>
                    {Array.isArray(stores) && stores.map((store) => (
                      <MenuItem key={store._id} value={store._id}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <CategoryIcon fontSize="small" />
                          <span>{store.storeName} ({store.storeLocation})</span>
                        </Stack>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </Grid>

            {/* Stock Information */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Stock Information
                </Typography>
                <TextField
                  name="stock"
                  label="Current Stock"
                  type="number"
                  value={formData.stock}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  placeholder="0"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Low Stock Alert
                </Typography>
                <TextField
                  name="lowStockThreshold"
                  label="Low Stock Alert"
                  type="number"
                  value={formData.lowStockThreshold}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  placeholder="0"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Price */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MoneyIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Pricing
                  </Typography>
                </Stack>
                <TextField
                  name="price"
                  label="Price"
                  type="number"
                  value={Math.floor(formData.price)}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  placeholder="0.00"
                  InputProps={{
                    startAdornment: (
                      <Typography sx={{ mr: 1, color: '#1976d2', fontWeight: 600 }}>
                        ₹
                      </Typography>
                    )
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Batch Number */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Batch Information
                </Typography>
                <TextField
                  name="batch_number"
                  label="Batch Number (Optional)"
                  value={formData.batch_number || ''}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  placeholder="Enter batch number"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Category Display */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Product Category
                </Typography>
                <TextField
                  name="category"
                  label="Category"
                  value={formData.category}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  disabled={!!selectedProduct}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      backgroundColor: selectedProduct ? 'rgba(25, 118, 210, 0.05)' : 'transparent',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Supplier Override */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Supplier (Optional)
                </Typography>
                <TextField
                  name="supplier"
                  label="Supplier Name"
                  value={formData.supplier || (selectedProduct?.defaultSupplier?.name || '')}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  placeholder={selectedProduct?.defaultSupplier?.name || "Enter supplier name"}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Manufacturing Date */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <DateIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Manufacturing Date
                  </Typography>
                </Stack>
                <TextField
                  name="manufacturing_date"
                  label="Manufacturing Date"
                  type="date"
                  value={formData.manufacturing_date}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Expiry Date (Optional) */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <DateIcon sx={{ color: '#f44336', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#f44336">
                    Expiry Date (Optional)
                  </Typography>
                </Stack>
                <TextField
                  name="expiry_date"
                  label="Expiry Date (Optional)"
                  type="date"
                  value={formData.expiry_date}
                  onChange={handleChange}
                  variant="outlined"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#f44336',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#f44336',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#f44336',
                    },
                  }}
                />
              </Stack>
            </Grid>

            <Divider sx={{ my: 3, borderColor: 'rgba(25, 118, 210, 0.2)' }} />

            {/* Payment & Tax Information */}
            <Grid item xs={12}>
              <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                <MoneyIcon sx={{ color: '#4caf50', fontSize: 20 }} />
                <Typography variant="subtitle2" fontWeight={600} color="#4caf50">
                  Payment & Tax Information
                </Typography>
              </Stack>
            </Grid>

            {/* Base Price */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#4caf50">
                  Base Price (₹)
                </Typography>
                <TextField
                  name="basePrice"
                  label="Base Price"
                  type="number"
                  value={formData.basePrice}
                  onChange={handleChange}
                  fullWidth
                  required
                  variant="outlined"
                  placeholder="Enter base price"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#4caf50',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* GST Rate */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#4caf50">
                  GST Rate (%)
                </Typography>
                <FormControl
                  fullWidth
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#4caf50',
                      }
                    }
                  }}
                >
                  <InputLabel>GST Rate</InputLabel>
                  <Select
                    name="gstRate"
                    value={formData.gstRate}
                    onChange={handleChange}
                    label="GST Rate"
                  >
                    <MenuItem value="0">0% (Exempt)</MenuItem>
                    <MenuItem value="5">5%</MenuItem>
                    <MenuItem value="12">12%</MenuItem>
                    <MenuItem value="18">18%</MenuItem>
                    <MenuItem value="28">28%</MenuItem>
                  </Select>
                </FormControl>
              </Stack>
            </Grid>

            {/* Discount */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#4caf50">
                  Discount (Optional)
                </Typography>
                <Stack direction="row" spacing={1}>
                  <TextField
                    name="discount"
                    label="Discount"
                    type="number"
                    value={formData.discount}
                    onChange={handleChange}
                    variant="outlined"
                    placeholder="0"
                    sx={{
                      flex: 2,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#4caf50',
                        },
                      },
                    }}
                  />
                  <FormControl sx={{ flex: 1 }}>
                    <InputLabel>Type</InputLabel>
                    <Select
                      name="discountType"
                      value={formData.discountType}
                      onChange={handleChange}
                      label="Type"
                      sx={{
                        borderRadius: 3,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#4caf50',
                        }
                      }}
                    >
                      <MenuItem value="percentage">%</MenuItem>
                      <MenuItem value="fixed">₹</MenuItem>
                    </Select>
                  </FormControl>
                </Stack>
              </Stack>
            </Grid>

            {/* Final Price (Calculated) */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#4caf50">
                  Final Price (₹)
                </Typography>
                <TextField
                  name="finalPrice"
                  label="Final Price (Including GST)"
                  value={formData.finalPrice}
                  fullWidth
                  variant="outlined"
                  disabled
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      backgroundColor: 'rgba(76, 175, 80, 0.05)',
                      fontWeight: 600,
                    },
                  }}
                />
              </Stack>
            </Grid>

            <Divider sx={{ my: 3, borderColor: 'rgba(25, 118, 210, 0.2)' }} />


          </Grid>
        </DialogContent>

        <DialogActions
          sx={{
            p: 3,
            background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(21, 101, 192, 0.01) 100%)',
            borderTop: '1px solid rgba(25, 118, 210, 0.1)',
            gap: 2
          }}
        >
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              borderRadius: 3,
              textTransform: 'none',
              px: 4,
              py: 1.5,
              fontWeight: 600,
              borderColor: '#bdbdbd',
              color: '#757575',
              '&:hover': {
                borderColor: '#9e9e9e',
                backgroundColor: 'rgba(189, 189, 189, 0.1)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            type="button"
            disabled={!selectedProduct || !selectedStoreId || !formData.stock || !formData.basePrice}
            sx={{
              borderRadius: 3,
              textTransform: 'none',
              px: 4,
              py: 1.5,
              fontWeight: 700,
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                boxShadow: '0 6px 25px rgba(25, 118, 210, 0.4)',
                transform: 'translateY(-1px)',
              },
              '&:disabled': {
                background: 'rgba(0, 0, 0, 0.12)',
                color: 'rgba(0, 0, 0, 0.26)',
                boxShadow: 'none',
              },
              transition: 'all 0.3s ease',
            }}
          >
            {product ? 'Save Changes' : 'Add Stock'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={showSuccess}
        autoHideDuration={3000}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          severity="success"
          sx={{
            width: '100%',
            borderRadius: 3,
            backdropFilter: 'blur(20px)',
            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)',
            color: 'white',
            fontWeight: 600,
            boxShadow: '0 8px 32px rgba(76, 175, 80, 0.3)',
            '& .MuiAlert-icon': {
              color: 'white'
            }
          }}
        >
          Product added successfully!
        </Alert>
      </Snackbar>
    </>
  );
};

export default ProductDialog;
