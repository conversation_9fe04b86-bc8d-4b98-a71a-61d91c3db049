import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Divider,
  LinearProgress,
  Avatar,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Assessment as ReportIcon,
  Construction as ConstructionIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  LocationOn as LocationIcon,
  Engineering as EngineeringIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import PremiumDialog from '../common/PremiumDialog';
import { usePermissions } from '../../hooks/usePermissions';
import Cookies from 'js-cookie';

const DailyProgressReport = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filterLocation, setFilterLocation] = useState('all');
  const [filterContractor, setFilterContractor] = useState('all');

  // Get user role and permissions
  const userRole = Cookies.get('userRole');
  const { canView, canManage } = usePermissions();

  // For DPR, allow access to all roles except 'user'
  const canViewDPR = userRole !== 'user';
  const canManageDPR = userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' || userRole === 'siteengineer';

  // Debug logging
  console.log('🔍 DPR Debug Info:', {
    userRole,
    canViewDPR,
    canManageDPR
  });

  // Enhanced sample data structure based on both Excel formats
  const [sampleReports] = useState([
    {
      id: 1,
      date: '2025-01-06',
      location: 'Pandhurna Dam Balancing Reservoir',
      workDescription: 'MS & HDPE Pipe Network Line Excavation & Laying work in Progress.',
      agency: 'MANTENA INFRASOL PVT LTD',
      workDetails: {
        excavation: { today: 50, cumulative: 354, target: 400 },
        pipeLaying: { today: 45, cumulative: 354, target: 400 },
        backFilling: { today: 40, cumulative: 300, target: 400 }
      },
      manpower: {
        skilled: 5,
        unskilled: 8,
        total: 13
      },
      machinery: {
        excavator: 1,
        dumper: 2,
        rocMachine: 0
      },
      materials: {
        cement: { consumed: 0, unit: 'bags' },
        steel: { consumed: 0, unit: 'tons' },
        pipes: { consumed: 25, unit: 'meters' }
      },
      status: 'active',
      remarks: 'Work progressing as per schedule'
    },
    {
      id: 2,
      date: '2025-01-06',
      location: 'Pump House -8 Intake Well (Gorakhpur)',
      workDescription: 'ECB Column Shuttering & Pump house backfilling work. Today RCC Qty: 0 Cum @M35. Today Plump Concrete Qty: 0 Cum @ Uptodate Concrete Qty: 785.4 cum',
      agency: 'MANTENA INFRASOL PVT LTD',
      workDetails: {
        rccWork: { today: 0, cumulative: 785.4, target: 1000 },
        shuttering: { today: 15, cumulative: 450, target: 600 },
        backFilling: { today: 20, cumulative: 380, target: 500 }
      },
      manpower: {
        skilled: 8,
        unskilled: 12,
        total: 20
      },
      machinery: {
        excavator: 1,
        dumper: 1,
        rocMachine: 0
      },
      materials: {
        cement: { consumed: 50, unit: 'bags' },
        steel: { consumed: 2.5, unit: 'tons' },
        aggregate: { consumed: 15, unit: 'cum' }
      },
      status: 'active',
      remarks: 'Concrete work completed, shuttering in progress'
    },
    {
      id: 3,
      date: '2025-01-06',
      location: 'WRD Camp Office (Linga)',
      workDescription: 'Slab Parafit wall brick masonry work & Brick shifting from ground floor to slab work in progress. Today Concrete Qty: 0 Cum @ M25. Uptodate Concrete Qty: 181.936 Cum',
      agency: 'Danish Parvez',
      workDetails: {
        brickWork: { today: 25, cumulative: 181.936, target: 250 },
        concreteWork: { today: 0, cumulative: 181.936, target: 200 },
        shifting: { today: 100, cumulative: 500, target: 800 }
      },
      manpower: {
        skilled: 2,
        unskilled: 3,
        total: 5
      },
      machinery: {
        excavator: 0,
        dumper: 0,
        rocMachine: 0
      },
      materials: {
        bricks: { consumed: 1000, unit: 'nos' },
        cement: { consumed: 25, unit: 'bags' },
        sand: { consumed: 5, unit: 'cum' }
      },
      status: 'active',
      remarks: 'Masonry work in progress'
    },
    {
      id: 4,
      date: '2025-01-06',
      location: 'Pump House -6 (Saroth)',
      workDescription: 'No Work. Today Qty: 0 Cum. Soft/Black Soil: 0 Cum. Hard Soil (Murrum): 0 Cum. Hard Rock: 0 Cum. Up to Date Quantity: 11819 Cum. Drilling Holes: 0 Nos (12 Ft) Blasting Holes: 0 Nos',
      agency: 'GV Infra Projects',
      workDetails: {
        excavation: { today: 0, cumulative: 11819, target: 12000 },
        drilling: { today: 0, cumulative: 150, target: 200 },
        blasting: { today: 0, cumulative: 80, target: 100 }
      },
      manpower: {
        skilled: 0,
        unskilled: 0,
        total: 0
      },
      machinery: {
        excavator: 0,
        dumper: 0,
        rocMachine: 0
      },
      materials: {
        explosives: { consumed: 0, unit: 'kg' },
        diesel: { consumed: 0, unit: 'liters' }
      },
      status: 'idle',
      remarks: 'No work today'
    }
  ]);

  useEffect(() => {
    setReports(sampleReports);
  }, [sampleReports]);

  // Check if user has permission to view DPR
  if (!canViewDPR) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" color="error" sx={{ mb: 2 }}>
          Access Denied
        </Typography>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          You don't have permission to view Daily Progress Reports.
        </Typography>
      </Box>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'warning';
      case 'pending': return 'error';
      default: return 'default';
    }
  };

  const calculateProgress = (completed, target) => {
    return target > 0 ? Math.round((completed / target) * 100) : 0;
  };

  const getProgressColor = (progress) => {
    if (progress >= 90) return 'success';
    if (progress >= 70) return 'warning';
    return 'error';
  };

  const filteredReports = reports.filter(report => {
    const dateMatch = report.date === selectedDate;
    const locationMatch = filterLocation === 'all' || report.location.toLowerCase().includes(filterLocation.toLowerCase());
    const contractorMatch = filterContractor === 'all' || report.agency.toLowerCase().includes(filterContractor.toLowerCase());
    return dateMatch && locationMatch && contractorMatch;
  });

  const uniqueLocations = [...new Set(reports.map(r => r.location))];
  const uniqueContractors = [...new Set(reports.map(r => r.agency))];

  const totalStats = filteredReports.reduce((acc, report) => {
    // Calculate totals from different work types
    Object.keys(report.workDetails).forEach(workType => {
      const work = report.workDetails[workType];
      if (work.today !== undefined) acc.totalTodayWork += work.today;
      if (work.cumulative !== undefined) acc.totalCumulative += work.cumulative;
      if (work.target !== undefined) acc.totalTarget += work.target;
    });

    // Manpower statistics
    acc.totalManpower += report.manpower.total;
    acc.totalSkilled += report.manpower.skilled;
    acc.totalUnskilled += report.manpower.unskilled;

    // Active sites count
    if (report.status === 'active') acc.activeSites++;
    if (report.status === 'idle') acc.idleSites++;

    return acc;
  }, {
    totalTodayWork: 0,
    totalCumulative: 0,
    totalTarget: 0,
    totalManpower: 0,
    totalSkilled: 0,
    totalUnskilled: 0,
    activeSites: 0,
    idleSites: 0
  });

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                width: 56,
                height: 56,
              }}
            >
              <ReportIcon fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} sx={{ color: '#1e293b', mb: 0.5 }}>
                📊 Daily Progress Report
              </Typography>
              <Typography variant="body1" sx={{ color: '#64748b' }}>
                Track construction progress and monitor project milestones
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Print Report
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Export Excel
            </Button>
            {canManageDPR && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenDialog(true)}
                sx={{
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 32px rgba(102, 126, 234, 0.4)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                Add New Entry
              </Button>
            )}
          </Box>
        </Box>

        {/* Filters */}
        <Paper
          sx={{
            p: 3,
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 8px 24px rgba(15, 23, 42, 0.1)',
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                label="Report Date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Location"
                value={filterLocation}
                onChange={(e) => setFilterLocation(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <MenuItem value="all">All Locations</MenuItem>
                {uniqueLocations.map(location => (
                  <MenuItem key={location} value={location}>{location}</MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Contractor"
                value={filterContractor}
                onChange={(e) => setFilterContractor(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <MenuItem value="all">All Contractors</MenuItem>
                {uniqueContractors.map(contractor => (
                  <MenuItem key={contractor} value={contractor}>{contractor}</MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarIcon sx={{ color: '#64748b' }} />
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  {filteredReports.length} entries found
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(102, 126, 234, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <ConstructionIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Today's Work Progress
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalTodayWork.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total work completed today
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalCumulative, totalStats.totalTarget)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(240, 147, 251, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <EngineeringIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Active Work Sites
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.activeSites}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  of {filteredReports.length} total sites ({totalStats.idleSites} idle)
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.activeSites, filteredReports.length)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(79, 172, 254, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <TrendingUpIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Total Manpower
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalManpower}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {totalStats.totalSkilled} skilled, {totalStats.totalUnskilled} unskilled
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalSkilled, totalStats.totalManpower)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Main Data Table */}
      <Paper
        sx={{
          borderRadius: 4,
          background: `
            linear-gradient(135deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 100%
            )
          `,
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(148, 163, 184, 0.2)',
          boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ p: 3, borderBottom: '1px solid rgba(148, 163, 184, 0.1)' }}>
          <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
            📋 Daily Progress Entries - {new Date(selectedDate).toLocaleDateString()}
          </Typography>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'rgba(148, 163, 184, 0.05)' }}>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>S.No</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Location</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Work Description</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Agency</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Today's Work</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Cumulative</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Manpower</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredReports.map((report, index) => (
                <motion.tr
                  key={report.id}
                  component={TableRow}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  sx={{
                    '&:hover': {
                      backgroundColor: 'rgba(102, 126, 234, 0.05)',
                    },
                  }}
                >
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationIcon sx={{ color: '#667eea', fontSize: 16 }} />
                      <Box>
                        <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 200 }}>
                          {report.location.length > 30
                            ? `${report.location.substring(0, 30)}...`
                            : report.location}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ maxWidth: 300 }}>
                    <Typography variant="body2" sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.4
                    }}>
                      {report.workDescription}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          fontSize: '0.75rem',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        }}
                      >
                        {report.agency.charAt(0)}
                      </Avatar>
                      <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 120 }}>
                        {report.agency.length > 15
                          ? `${report.agency.substring(0, 15)}...`
                          : report.agency}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 100 }}>
                      {Object.entries(report.workDetails).slice(0, 2).map(([workType, work]) => (
                        <Box key={workType} sx={{ mb: 1 }}>
                          <Typography variant="caption" sx={{ color: '#64748b', textTransform: 'capitalize' }}>
                            {workType}: {work.today || 0}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      {Object.entries(report.workDetails).slice(0, 2).map(([workType, work]) => (
                        <Box key={workType} sx={{ mb: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>
                              {work.cumulative}/{work.target}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>
                              {calculateProgress(work.cumulative, work.target)}%
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={calculateProgress(work.cumulative, work.target)}
                            color={getProgressColor(calculateProgress(work.cumulative, work.target))}
                            sx={{ height: 4, borderRadius: 2 }}
                          />
                        </Box>
                      ))}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={`${report.manpower.total}`}
                        size="small"
                        color="primary"
                        sx={{ minWidth: 40 }}
                      />
                      <Box>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          S: {report.manpower.skilled}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          U: {report.manpower.unskilled}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={report.status}
                      size="small"
                      color={getStatusColor(report.status)}
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          sx={{
                            color: '#667eea',
                            '&:hover': {
                              backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            },
                          }}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      {canManageDPR && (
                        <Tooltip title="Edit Entry">
                          <IconButton
                            size="small"
                            sx={{
                              color: '#f093fb',
                              '&:hover': {
                                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                              },
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </motion.tr>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {filteredReports.length === 0 && (
          <Box sx={{ p: 6, textAlign: 'center' }}>
            <ReportIcon sx={{ fontSize: 64, color: '#cbd5e1', mb: 2 }} />
            <Typography variant="h6" sx={{ color: '#64748b', mb: 1 }}>
              No progress entries found
            </Typography>
            <Typography variant="body2" sx={{ color: '#94a3b8' }}>
              Add new entries to track daily construction progress
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Add Entry Dialog */}
      <PremiumDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        title="Add New Progress Entry"
        titleIcon="📊"
        maxWidth="lg"
        actions={
          <>
            <Button
              onClick={() => setOpenDialog(false)}
              sx={{
                borderRadius: 2,
                color: '#64748b',
                borderColor: '#e2e8f0',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                }
              }}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                },
              }}
            >
              Save Entry
            </Button>
          </>
        }
      >
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          DPR Entry Form will be implemented here with all the required fields
          matching the Excel format structure.
        </Typography>
      </PremiumDialog>
    </Box>
  );
};

export default DailyProgressReport;
