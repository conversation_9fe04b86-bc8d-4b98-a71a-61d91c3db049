import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Divider,
  LinearProgress,
  Avatar,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  Alert,
  AlertTitle,
  Snackbar,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Autocomplete,
  Slider,
  Rating,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Switch,
  FormControlLabel,
  InputAdornment,
  Fab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Assessment as ReportIcon,
  Construction as ConstructionIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  Group as GroupIcon,
  LocationOn as LocationIcon,
  Engineering as EngineeringIcon,
  CalendarToday as CalendarIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  CloudUpload as UploadIcon,
  Share as ShareIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  PhotoCamera as PhotoIcon,
  AttachFile as AttachFileIcon,
  Map as MapIcon,
  QrCode as QrCodeIcon,
  Backup as BackupIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Delete as DeleteIcon,
  GetApp as ExportIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import PremiumDialog from '../common/PremiumDialog';
import { usePermissions } from '../../hooks/usePermissions';
import Cookies from 'js-cookie';

const DailyProgressReport = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);

  // Form state for new DPR entry
  const [formData, setFormData] = useState({
    // Basic Information
    date: new Date().toISOString().split('T')[0],
    sNo: '',
    contractorName: '',
    node: '',
    pipeDiameter: '',
    units: 'RMT',
    location: '',
    remarks: '',
    engineerSupervisor: '',

    // HDPE Network Line
    hdpeExcavation: 0,
    hdpePipeLaying: 0,
    hdpeBackFilling: 0,

    // MS Network Line
    msExcavation: 0,
    msPipeLaying: 0,
    msBackFilling: 0,

    // Work Description Details
    workDescription: '',
    agencyName: '',

    // Summary Section
    description: '',
    targetQty: 0,
    lastSeasonQty: 0,
    thisSeasonQty: 0,
    cumulativeQty: 0,
    balanceQty: 0,

    // Additional Details
    skilledLabour: 0,
    unskilledLabour: 0,
    totalLabour: 0,
    concreteQty: 0,
    machineryDetails: ''
  });
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filterLocation, setFilterLocation] = useState('all');
  const [filterContractor, setFilterContractor] = useState('all');

  // 🚀 PREMIUM ADVANCED FEATURES STATE
  const [currentTab, setCurrentTab] = useState(0);
  const [viewMode, setViewMode] = useState('table'); // 'table', 'cards', 'timeline', 'analytics', 'kanban'
  const [selectedReports, setSelectedReports] = useState([]);
  const [bulkActions, setBulkActions] = useState(false);
  const [notifications, setNotifications] = useState([
    { id: 1, type: 'success', message: 'DPR entry saved successfully', time: '2 min ago' },
    { id: 2, type: 'warning', message: 'Weather alert: Rain expected', time: '15 min ago' },
    { id: 3, type: 'info', message: 'New team member added to project', time: '1 hour ago' }
  ]);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [autoSave, setAutoSave] = useState(true);
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const [advancedFilters, setAdvancedFilters] = useState({
    dateRange: { start: null, end: null },
    contractor: '',
    location: '',
    status: 'all',
    priority: 'all',
    progress: [0, 100],
    workType: 'all',
    qualityRating: [0, 5]
  });
  const [exportOptions, setExportOptions] = useState({
    format: 'pdf',
    includeCharts: true,
    includePhotos: true,
    template: 'detailed',
    dateRange: 'today'
  });
  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalReports: 0,
    completedToday: 0,
    avgProgress: 0,
    criticalIssues: 0,
    activeContractors: 0,
    totalLabour: 0,
    weatherImpact: 'low',
    safetyScore: 95,
    qualityScore: 4.2,
    budgetUtilization: 78
  });
  const [weatherData, setWeatherData] = useState({
    temperature: 25,
    condition: 'Clear',
    humidity: 65,
    windSpeed: 12,
    forecast: 'Sunny',
    workSuitability: 'Excellent'
  });
  const [qualityRating, setQualityRating] = useState(4.5);
  const [safetyScore, setSafetyScore] = useState(95);
  const [attachments, setAttachments] = useState([]);
  const [gpsLocation, setGpsLocation] = useState(null);
  const [voiceNotes, setVoiceNotes] = useState([]);
  const [collaborators, setCollaborators] = useState([
    { id: 1, name: 'John Doe', role: 'Site Engineer', avatar: 'JD', online: true },
    { id: 2, name: 'Jane Smith', role: 'Project Manager', avatar: 'JS', online: false },
    { id: 3, name: 'Mike Johnson', role: 'Quality Inspector', avatar: 'MJ', online: true }
  ]);
  const [templates, setTemplates] = useState([
    { id: 1, name: 'Standard Construction', category: 'Construction' },
    { id: 2, name: 'Pipeline Installation', category: 'Infrastructure' },
    { id: 3, name: 'Quality Inspection', category: 'Quality' }
  ]);
  const [aiSuggestions, setAiSuggestions] = useState([
    'Consider increasing manpower for excavation work',
    'Weather conditions are optimal for concrete work',
    'Material delivery scheduled for tomorrow'
  ]);
  const [workflowStatus, setWorkflowStatus] = useState('draft'); // draft, review, approved, published
  const [timeTracking, setTimeTracking] = useState({
    startTime: null,
    endTime: null,
    breakTime: 0,
    overtimeHours: 0
  });
  const [costTracking, setCostTracking] = useState({
    budgetAllocated: 100000,
    actualSpent: 78000,
    projectedCost: 95000,
    variance: -5000
  });
  const [riskAssessment, setRiskAssessment] = useState({
    weatherRisk: 'low',
    safetyRisk: 'medium',
    qualityRisk: 'low',
    scheduleRisk: 'high'
  });
  const [complianceChecks, setComplianceChecks] = useState({
    safetyCompliance: true,
    environmentalCompliance: true,
    qualityStandards: true,
    regulatoryApproval: false
  });

  // Get user role and permissions
  const userRole = Cookies.get('userRole');
  const { canView, canManage } = usePermissions();

  // For DPR, allow access to all roles except 'user'
  const canViewDPR = userRole !== 'user';
  const canManageDPR = userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' || userRole === 'siteengineer';

  // Debug logging
  console.log('🔍 DPR Debug Info:', {
    userRole,
    canViewDPR,
    canManageDPR
  });

  // Form handlers
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-calculate balance quantity
    if (['targetQty', 'cumulativeQty'].includes(field)) {
      const target = field === 'targetQty' ? value : formData.targetQty;
      const cumulative = field === 'cumulativeQty' ? value : formData.cumulativeQty;
      setFormData(prev => ({
        ...prev,
        balanceQty: target - cumulative
      }));
    }

    // Auto-calculate total labour
    if (['skilledLabour', 'unskilledLabour'].includes(field)) {
      const skilled = field === 'skilledLabour' ? value : formData.skilledLabour;
      const unskilled = field === 'unskilledLabour' ? value : formData.unskilledLabour;
      setFormData(prev => ({
        ...prev,
        totalLabour: skilled + unskilled
      }));
    }
  };

  const handleSubmit = () => {
    // Validate required fields
    const requiredFields = ['contractorName', 'location', 'workDescription', 'agencyName'];
    const missingFields = requiredFields.filter(field => !formData[field]);

    if (missingFields.length > 0) {
      alert(`Please fill in the following required fields: ${missingFields.join(', ')}`);
      return;
    }

    // Create new report entry
    const newReport = {
      id: Date.now(),
      date: formData.date,
      sNo: formData.sNo || reports.length + 1,
      contractorName: formData.contractorName,
      node: formData.node,
      pipeDiameter: formData.pipeDiameter,
      units: formData.units,
      location: formData.location,
      remarks: formData.remarks,
      engineerSupervisor: formData.engineerSupervisor,

      // Network data
      hdpeNetwork: {
        excavation: formData.hdpeExcavation,
        pipeLaying: formData.hdpePipeLaying,
        backFilling: formData.hdpeBackFilling
      },
      msNetwork: {
        excavation: formData.msExcavation,
        pipeLaying: formData.msPipeLaying,
        backFilling: formData.msBackFilling
      },

      // Work details
      workDescription: formData.workDescription,
      agencyName: formData.agencyName,

      // Summary
      targetQty: formData.targetQty,
      lastSeasonQty: formData.lastSeasonQty,
      thisSeasonQty: formData.thisSeasonQty,
      cumulativeQty: formData.cumulativeQty,
      balanceQty: formData.balanceQty,

      // Labour details
      labour: {
        skilled: formData.skilledLabour,
        unskilled: formData.unskilledLabour,
        total: formData.totalLabour
      },

      concreteQty: formData.concreteQty,
      machineryDetails: formData.machineryDetails,

      status: 'active',
      createdAt: new Date().toISOString()
    };

    // Add to reports list
    setReports(prev => [...prev, newReport]);

    // Reset form and close dialog
    setFormData({
      date: new Date().toISOString().split('T')[0],
      sNo: '',
      contractorName: '',
      node: '',
      pipeDiameter: '',
      units: 'RMT',
      location: '',
      remarks: '',
      engineerSupervisor: '',
      hdpeExcavation: 0,
      hdpePipeLaying: 0,
      hdpeBackFilling: 0,
      msExcavation: 0,
      msPipeLaying: 0,
      msBackFilling: 0,
      workDescription: '',
      agencyName: '',
      description: '',
      targetQty: 0,
      lastSeasonQty: 0,
      thisSeasonQty: 0,
      cumulativeQty: 0,
      balanceQty: 0,
      skilledLabour: 0,
      unskilledLabour: 0,
      totalLabour: 0,
      concreteQty: 0,
      machineryDetails: ''
    });

    setOpenDialog(false);
    alert('DPR entry added successfully!');
  };

  // Enhanced sample data structure based on both Excel formats
  const [sampleReports] = useState([
    {
      id: 1,
      date: '2025-01-06',
      location: 'Pandhurna Dam Balancing Reservoir',
      workDescription: 'MS & HDPE Pipe Network Line Excavation & Laying work in Progress.',
      agency: 'MANTENA INFRASOL PVT LTD',
      workDetails: {
        excavation: { today: 50, cumulative: 354, target: 400 },
        pipeLaying: { today: 45, cumulative: 354, target: 400 },
        backFilling: { today: 40, cumulative: 300, target: 400 }
      },
      manpower: {
        skilled: 5,
        unskilled: 8,
        total: 13
      },
      machinery: {
        excavator: 1,
        dumper: 2,
        rocMachine: 0
      },
      materials: {
        cement: { consumed: 0, unit: 'bags' },
        steel: { consumed: 0, unit: 'tons' },
        pipes: { consumed: 25, unit: 'meters' }
      },
      status: 'active',
      remarks: 'Work progressing as per schedule'
    },
    {
      id: 2,
      date: '2025-01-06',
      location: 'Pump House -8 Intake Well (Gorakhpur)',
      workDescription: 'ECB Column Shuttering & Pump house backfilling work. Today RCC Qty: 0 Cum @M35. Today Plump Concrete Qty: 0 Cum @ Uptodate Concrete Qty: 785.4 cum',
      agency: 'MANTENA INFRASOL PVT LTD',
      workDetails: {
        rccWork: { today: 0, cumulative: 785.4, target: 1000 },
        shuttering: { today: 15, cumulative: 450, target: 600 },
        backFilling: { today: 20, cumulative: 380, target: 500 }
      },
      manpower: {
        skilled: 8,
        unskilled: 12,
        total: 20
      },
      machinery: {
        excavator: 1,
        dumper: 1,
        rocMachine: 0
      },
      materials: {
        cement: { consumed: 50, unit: 'bags' },
        steel: { consumed: 2.5, unit: 'tons' },
        aggregate: { consumed: 15, unit: 'cum' }
      },
      status: 'active',
      remarks: 'Concrete work completed, shuttering in progress'
    },
    {
      id: 3,
      date: '2025-01-06',
      location: 'WRD Camp Office (Linga)',
      workDescription: 'Slab Parafit wall brick masonry work & Brick shifting from ground floor to slab work in progress. Today Concrete Qty: 0 Cum @ M25. Uptodate Concrete Qty: 181.936 Cum',
      agency: 'Danish Parvez',
      workDetails: {
        brickWork: { today: 25, cumulative: 181.936, target: 250 },
        concreteWork: { today: 0, cumulative: 181.936, target: 200 },
        shifting: { today: 100, cumulative: 500, target: 800 }
      },
      manpower: {
        skilled: 2,
        unskilled: 3,
        total: 5
      },
      machinery: {
        excavator: 0,
        dumper: 0,
        rocMachine: 0
      },
      materials: {
        bricks: { consumed: 1000, unit: 'nos' },
        cement: { consumed: 25, unit: 'bags' },
        sand: { consumed: 5, unit: 'cum' }
      },
      status: 'active',
      remarks: 'Masonry work in progress'
    },
    {
      id: 4,
      date: '2025-01-06',
      location: 'Pump House -6 (Saroth)',
      workDescription: 'No Work. Today Qty: 0 Cum. Soft/Black Soil: 0 Cum. Hard Soil (Murrum): 0 Cum. Hard Rock: 0 Cum. Up to Date Quantity: 11819 Cum. Drilling Holes: 0 Nos (12 Ft) Blasting Holes: 0 Nos',
      agency: 'GV Infra Projects',
      workDetails: {
        excavation: { today: 0, cumulative: 11819, target: 12000 },
        drilling: { today: 0, cumulative: 150, target: 200 },
        blasting: { today: 0, cumulative: 80, target: 100 }
      },
      manpower: {
        skilled: 0,
        unskilled: 0,
        total: 0
      },
      machinery: {
        excavator: 0,
        dumper: 0,
        rocMachine: 0
      },
      materials: {
        explosives: { consumed: 0, unit: 'kg' },
        diesel: { consumed: 0, unit: 'liters' }
      },
      status: 'idle',
      remarks: 'No work today'
    }
  ]);

  useEffect(() => {
    setReports(sampleReports);
  }, [sampleReports]);

  // Check if user has permission to view DPR
  if (!canViewDPR) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" color="error" sx={{ mb: 2 }}>
          Access Denied
        </Typography>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          You don't have permission to view Daily Progress Reports.
        </Typography>
      </Box>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'warning';
      case 'pending': return 'error';
      default: return 'default';
    }
  };

  const calculateProgress = (completed, target) => {
    return target > 0 ? Math.round((completed / target) * 100) : 0;
  };

  const getProgressColor = (progress) => {
    if (progress >= 90) return 'success';
    if (progress >= 70) return 'warning';
    return 'error';
  };

  const filteredReports = reports.filter(report => {
    const dateMatch = report.date === selectedDate;
    const locationMatch = filterLocation === 'all' || report.location.toLowerCase().includes(filterLocation.toLowerCase());
    const contractorMatch = filterContractor === 'all' || report.agency.toLowerCase().includes(filterContractor.toLowerCase());
    return dateMatch && locationMatch && contractorMatch;
  });

  const uniqueLocations = [...new Set(reports.map(r => r.location))];
  const uniqueContractors = [...new Set(reports.map(r => r.agency))];

  const totalStats = filteredReports.reduce((acc, report) => {
    // Calculate totals from different work types
    Object.keys(report.workDetails).forEach(workType => {
      const work = report.workDetails[workType];
      if (work.today !== undefined) acc.totalTodayWork += work.today;
      if (work.cumulative !== undefined) acc.totalCumulative += work.cumulative;
      if (work.target !== undefined) acc.totalTarget += work.target;
    });

    // Manpower statistics
    acc.totalManpower += report.manpower.total;
    acc.totalSkilled += report.manpower.skilled;
    acc.totalUnskilled += report.manpower.unskilled;

    // Active sites count
    if (report.status === 'active') acc.activeSites++;
    if (report.status === 'idle') acc.idleSites++;

    return acc;
  }, {
    totalTodayWork: 0,
    totalCumulative: 0,
    totalTarget: 0,
    totalManpower: 0,
    totalSkilled: 0,
    totalUnskilled: 0,
    activeSites: 0,
    idleSites: 0
  });

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                width: 56,
                height: 56,
              }}
            >
              <ReportIcon fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} sx={{ color: '#1e293b', mb: 0.5 }}>
                📊 Daily Progress Report
              </Typography>
              <Typography variant="body1" sx={{ color: '#64748b' }}>
                Track construction progress and monitor project milestones
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Print Report
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Export Excel
            </Button>
            {canManageDPR && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenDialog(true)}
                sx={{
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 32px rgba(102, 126, 234, 0.4)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                Add New Entry
              </Button>
            )}
          </Box>
        </Box>

        {/* 🚀 PREMIUM DASHBOARD METRICS */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                '&:hover': { transform: 'translateY(-4px)', boxShadow: '0 12px 40px rgba(102, 126, 234, 0.4)' },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" fontWeight={600}>Today's Progress</Typography>
                    <TrendingUpIcon sx={{ fontSize: 32, opacity: 0.8 }} />
                  </Box>
                  <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                    {filteredReports.length}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Active Reports • +12% from yesterday
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={85}
                    sx={{
                      mt: 2,
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      '& .MuiLinearProgress-bar': { backgroundColor: 'rgba(255,255,255,0.9)' }
                    }}
                  />
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 32px rgba(240, 147, 251, 0.3)',
                '&:hover': { transform: 'translateY(-4px)', boxShadow: '0 12px 40px rgba(240, 147, 251, 0.4)' },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" fontWeight={600}>Total Workforce</Typography>
                    <GroupIcon sx={{ fontSize: 32, opacity: 0.8 }} />
                  </Box>
                  <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                    {totalStats.totalManpower}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    {totalStats.totalSkilled} Skilled • {totalStats.totalUnskilled} Unskilled
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2, gap: 1 }}>
                    <Rating value={4.5} size="small" readOnly sx={{ color: 'rgba(255,255,255,0.9)' }} />
                    <Typography variant="caption">Efficiency</Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 32px rgba(79, 172, 254, 0.3)',
                '&:hover': { transform: 'translateY(-4px)', boxShadow: '0 12px 40px rgba(79, 172, 254, 0.4)' },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" fontWeight={600}>Weather Impact</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h6">☀️</Typography>
                      <Typography variant="body2">{weatherData.temperature}°C</Typography>
                    </Box>
                  </Box>
                  <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                    {weatherData.workSuitability}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    {weatherData.condition} • Humidity {weatherData.humidity}%
                  </Typography>
                  <Chip
                    label="Optimal Conditions"
                    size="small"
                    sx={{
                      mt: 2,
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      fontWeight: 600
                    }}
                  />
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 32px rgba(250, 112, 154, 0.3)',
                '&:hover': { transform: 'translateY(-4px)', boxShadow: '0 12px 40px rgba(250, 112, 154, 0.4)' },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" fontWeight={600}>Safety Score</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ fontSize: 24 }} />
                    </Box>
                  </Box>
                  <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                    {safetyScore}%
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Quality Rating: {qualityRating}/5.0
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2, gap: 1 }}>
                    <CircularProgress
                      variant="determinate"
                      value={safetyScore}
                      size={24}
                      sx={{ color: 'rgba(255,255,255,0.9)' }}
                    />
                    <Typography variant="caption">Compliance</Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>

        {/* 🎯 PREMIUM VIEW MODE TABS */}
        <Paper sx={{
          mb: 3,
          borderRadius: 4,
          background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.9) 100%)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(148,163,184,0.2)',
          boxShadow: '0 8px 24px rgba(15,23,42,0.1)'
        }}>
          <Tabs
            value={currentTab}
            onChange={(e, newValue) => setCurrentTab(newValue)}
            sx={{
              p: 2,
              '& .MuiTab-root': {
                borderRadius: 3,
                mx: 1,
                minHeight: 48,
                fontWeight: 600,
                textTransform: 'none',
                '&.Mui-selected': {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  boxShadow: '0 4px 16px rgba(102,126,234,0.3)'
                }
              }
            }}
          >
            <Tab icon={<DashboardIcon />} label="Dashboard" />
            <Tab icon={<ReportIcon />} label="Reports" />
            <Tab icon={<AnalyticsIcon />} label="Analytics" />
            <Tab icon={<TimelineIcon />} label="Timeline" />
            <Tab icon={<SettingsIcon />} label="Settings" />
          </Tabs>
        </Paper>

        {/* 🎯 TAB CONTENT BASED ON CURRENT TAB */}
        {currentTab === 0 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(102,126,234,0.05) 0%, rgba(118,75,162,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              📊 Dashboard Overview
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ p: 2, textAlign: 'center', borderRadius: 3 }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#667eea' }}>
                    {filteredReports.length}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Total Reports</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ p: 2, textAlign: 'center', borderRadius: 3 }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#f093fb' }}>
                    {totalStats.activeSites}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Active Sites</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ p: 2, textAlign: 'center', borderRadius: 3 }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#4facfe' }}>
                    {totalStats.totalManpower}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Total Workers</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ p: 2, textAlign: 'center', borderRadius: 3 }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#fa709a' }}>
                    {safetyScore}%
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Safety Score</Typography>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        )}

        {currentTab === 1 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(240,147,251,0.05) 0%, rgba(245,87,108,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              📋 Reports Management
            </Typography>
            <Typography variant="body1" sx={{ color: '#64748b' }}>
              This section shows all DPR reports in table format below. You can filter, search, and manage reports here.
            </Typography>
          </Paper>
        )}

        {currentTab === 2 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(79,172,254,0.05) 0%, rgba(0,242,254,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              📈 Analytics & Insights
            </Typography>
            <Alert severity="info" sx={{ borderRadius: 3 }}>
              <AlertTitle>Coming Soon!</AlertTitle>
              Advanced analytics with charts, progress trends, and performance insights will be available here.
            </Alert>
          </Paper>
        )}

        {currentTab === 3 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(250,112,154,0.05) 0%, rgba(254,225,64,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              ⏰ Timeline View
            </Typography>
            <Alert severity="info" sx={{ borderRadius: 3 }}>
              <AlertTitle>Coming Soon!</AlertTitle>
              Timeline view showing chronological progress of all activities will be available here.
            </Alert>
          </Paper>
        )}

        {currentTab === 4 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(168,237,234,0.05) 0%, rgba(254,214,227,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              ⚙️ Settings & Configuration
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={realTimeUpdates}
                      onChange={(e) => setRealTimeUpdates(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Real-time Updates"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoSave}
                      onChange={(e) => setAutoSave(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto Save"
                />
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Filters */}
        <Paper
          sx={{
            p: 3,
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 8px 24px rgba(15, 23, 42, 0.1)',
          }}
        >
          {/* 🔍 ADVANCED SEARCH & FILTERS */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6" fontWeight={600} sx={{ color: '#1e293b' }}>
              🔍 Advanced Filters & Search
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={realTimeUpdates}
                    onChange={(e) => setRealTimeUpdates(e.target.checked)}
                    color="primary"
                  />
                }
                label="Real-time Updates"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={autoSave}
                    onChange={(e) => setAutoSave(e.target.checked)}
                    color="primary"
                  />
                }
                label="Auto Save"
              />
            </Box>
          </Box>

          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="🗓️ Report Date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                select
                label="📍 Location Filter"
                value={filterLocation}
                onChange={(e) => setFilterLocation(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              >
                <MenuItem value="all">All Locations</MenuItem>
                {uniqueLocations.map((location) => (
                  <MenuItem key={location} value={location}>
                    {location.length > 30 ? `${location.substring(0, 30)}...` : location}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                select
                label="🏗️ Contractor"
                value={filterContractor}
                onChange={(e) => setFilterContractor(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              >
                <MenuItem value="all">All Contractors</MenuItem>
                {uniqueContractors.map((contractor) => (
                  <MenuItem key={contractor} value={contractor}>
                    {contractor}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                select
                label="📊 View Mode"
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              >
                <MenuItem value="table">📋 Table View</MenuItem>
                <MenuItem value="cards">🎴 Card View</MenuItem>
                <MenuItem value="timeline">⏰ Timeline View</MenuItem>
                <MenuItem value="analytics">📈 Analytics View</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="🔍 Quick Search"
                placeholder="Search reports..."
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: '#64748b' }} />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Advanced Filters">
                  <IconButton
                    sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white',
                      borderRadius: 3,
                      width: 48,
                      height: 48,
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: '0 8px 24px rgba(102,126,234,0.4)'
                      }
                    }}
                  >
                    <FilterIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export Data">
                  <IconButton
                    sx={{
                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                      color: 'white',
                      borderRadius: 3,
                      width: 48,
                      height: 48,
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: '0 8px 24px rgba(240,147,251,0.4)'
                      }
                    }}
                  >
                    <ExportIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>

          {/* 📊 PROGRESS OVERVIEW BAR */}
          <Box sx={{ mt: 3, p: 3, background: 'rgba(102,126,234,0.05)', borderRadius: 3 }}>
            <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              📊 Today's Progress Overview
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#667eea' }}>
                    {totalStats.activeSites}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Active Sites</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#f093fb' }}>
                    {totalStats.totalManpower}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Total Workers</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#4facfe' }}>
                    {Math.round((totalStats.totalCumulative / totalStats.totalTarget) * 100) || 0}%
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Overall Progress</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight={700} sx={{ color: '#fa709a' }}>
                    {safetyScore}%
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>Safety Score</Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Paper>

        {/* 🚨 NOTIFICATIONS & ALERTS */}
        {notifications.length > 0 && (
          <Paper sx={{
            mb: 3,
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.9) 100%)',
            border: '1px solid rgba(148,163,184,0.2)',
            overflow: 'hidden'
          }}>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
                🚨 Recent Notifications
              </Typography>
              <Grid container spacing={2}>
                {notifications.slice(0, 3).map((notification) => (
                  <Grid item xs={12} md={4} key={notification.id}>
                    <Alert
                      severity={notification.type}
                      sx={{
                        borderRadius: 3,
                        '& .MuiAlert-icon': { fontSize: 20 }
                      }}
                    >
                      <AlertTitle sx={{ fontSize: '0.9rem', fontWeight: 600 }}>
                        {notification.type === 'success' ? '✅ Success' :
                         notification.type === 'warning' ? '⚠️ Warning' :
                         notification.type === 'error' ? '❌ Error' : '💡 Info'}
                      </AlertTitle>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" sx={{ opacity: 0.7 }}>
                        {notification.time}
                      </Typography>
                    </Alert>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Paper>
        )}

        {/* Filters */}
        <Paper
          sx={{
            p: 3,
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 8px 24px rgba(15, 23, 42, 0.1)',
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                label="Report Date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Location"
                value={filterLocation}
                onChange={(e) => setFilterLocation(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <MenuItem value="all">All Locations</MenuItem>
                {uniqueLocations.map(location => (
                  <MenuItem key={location} value={location}>{location}</MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Contractor"
                value={filterContractor}
                onChange={(e) => setFilterContractor(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <MenuItem value="all">All Contractors</MenuItem>
                {uniqueContractors.map(contractor => (
                  <MenuItem key={contractor} value={contractor}>{contractor}</MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarIcon sx={{ color: '#64748b' }} />
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  {filteredReports.length} entries found
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(102, 126, 234, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <ConstructionIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Today's Work Progress
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalTodayWork.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total work completed today
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalCumulative, totalStats.totalTarget)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(240, 147, 251, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <EngineeringIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Active Work Sites
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.activeSites}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  of {filteredReports.length} total sites ({totalStats.idleSites} idle)
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.activeSites, filteredReports.length)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(79, 172, 254, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <TrendingUpIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Total Manpower
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalManpower}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {totalStats.totalSkilled} skilled, {totalStats.totalUnskilled} unskilled
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalSkilled, totalStats.totalManpower)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Main Data Table */}
      <Paper
        sx={{
          borderRadius: 4,
          background: `
            linear-gradient(135deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 100%
            )
          `,
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(148, 163, 184, 0.2)',
          boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ p: 3, borderBottom: '1px solid rgba(148, 163, 184, 0.1)' }}>
          <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
            📋 Daily Progress Entries - {new Date(selectedDate).toLocaleDateString()}
          </Typography>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'rgba(148, 163, 184, 0.05)' }}>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>S.No</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Contractor</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Location</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Work Description</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Agency</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>HDPE Network</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>MS Network</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Labour</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Progress</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredReports.map((report, index) => (
                <motion.tr
                  key={report.id}
                  component={TableRow}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  sx={{
                    '&:hover': {
                      backgroundColor: 'rgba(102, 126, 234, 0.05)',
                    },
                  }}
                >
                  <TableCell>{report.sNo || index + 1}</TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 150 }}>
                      {report.contractorName?.length > 20
                        ? `${report.contractorName.substring(0, 20)}...`
                        : report.contractorName || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationIcon sx={{ color: '#667eea', fontSize: 16 }} />
                      <Box>
                        <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 150 }}>
                          {report.location?.length > 25
                            ? `${report.location.substring(0, 25)}...`
                            : report.location || 'N/A'}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ maxWidth: 250 }}>
                    <Typography variant="body2" sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.4
                    }}>
                      {report.workDescription || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          fontSize: '0.75rem',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        }}
                      >
                        {(report.agencyName || report.agency || 'N')?.charAt(0)}
                      </Avatar>
                      <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 100 }}>
                        {(report.agencyName || report.agency || 'N/A')?.length > 12
                          ? `${(report.agencyName || report.agency).substring(0, 12)}...`
                          : (report.agencyName || report.agency || 'N/A')}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Exc: {report.hdpeNetwork?.excavation || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Pipe: {report.hdpeNetwork?.pipeLaying || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Fill: {report.hdpeNetwork?.backFilling || 0}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Exc: {report.msNetwork?.excavation || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Pipe: {report.msNetwork?.pipeLaying || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Fill: {report.msNetwork?.backFilling || 0}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={`${report.labour?.total || report.manpower?.total || 0}`}
                        size="small"
                        color="primary"
                        sx={{ minWidth: 40 }}
                      />
                      <Box>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          S: {report.labour?.skilled || report.manpower?.skilled || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          U: {report.labour?.unskilled || report.manpower?.unskilled || 0}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 100 }}>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Target: {report.targetQty || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Cumulative: {report.cumulativeQty || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                        Balance: {report.balanceQty || 0}
                      </Typography>
                      {report.targetQty > 0 && (
                        <LinearProgress
                          variant="determinate"
                          value={calculateProgress(report.cumulativeQty || 0, report.targetQty)}
                          color={getProgressColor(calculateProgress(report.cumulativeQty || 0, report.targetQty))}
                          sx={{ height: 4, borderRadius: 2, mt: 0.5 }}
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          sx={{
                            color: '#667eea',
                            '&:hover': {
                              backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            },
                          }}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      {canManageDPR && (
                        <Tooltip title="Edit Entry">
                          <IconButton
                            size="small"
                            sx={{
                              color: '#f093fb',
                              '&:hover': {
                                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                              },
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </motion.tr>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {filteredReports.length === 0 && (
          <Box sx={{ p: 6, textAlign: 'center' }}>
            <ReportIcon sx={{ fontSize: 64, color: '#cbd5e1', mb: 2 }} />
            <Typography variant="h6" sx={{ color: '#64748b', mb: 1 }}>
              No progress entries found
            </Typography>
            <Typography variant="body2" sx={{ color: '#94a3b8' }}>
              Add new entries to track daily construction progress
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Add Entry Dialog */}
      <PremiumDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        title="Add New Progress Entry"
        titleIcon="📊"
        maxWidth="lg"
        actions={
          <>
            <Button
              onClick={() => setOpenDialog(false)}
              sx={{
                borderRadius: 2,
                color: '#64748b',
                borderColor: '#e2e8f0',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                }
              }}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                },
              }}
            >
              Save Entry
            </Button>
          </>
        }
      >
        <Box sx={{ p: 2 }}>
          {/* 🎯 PREMIUM FORM PROGRESS STEPPER */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(102,126,234,0.05) 0%, rgba(118,75,162,0.05) 100%)', border: '1px solid rgba(102,126,234,0.1)' }}>
            <Stepper activeStep={0} alternativeLabel sx={{ mb: 2 }}>
              <Step>
                <StepLabel>📋 Basic Info</StepLabel>
              </Step>
              <Step>
                <StepLabel>🔵 Network Details</StepLabel>
              </Step>
              <Step>
                <StepLabel>📊 Progress & Labour</StepLabel>
              </Step>
              <Step>
                <StepLabel>✅ Review & Submit</StepLabel>
              </Step>
            </Stepper>

            {/* 🤖 AI SUGGESTIONS */}
            {aiSuggestions.length > 0 && (
              <Alert
                severity="info"
                sx={{
                  mb: 2,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(79,172,254,0.1) 0%, rgba(0,242,254,0.1) 100%)',
                  border: '1px solid rgba(79,172,254,0.2)'
                }}
              >
                <AlertTitle sx={{ fontWeight: 600 }}>🤖 AI Suggestions</AlertTitle>
                <Box component="ul" sx={{ m: 0, pl: 2 }}>
                  {aiSuggestions.slice(0, 2).map((suggestion, index) => (
                    <Typography component="li" variant="body2" key={index} sx={{ mb: 0.5 }}>
                      {suggestion}
                    </Typography>
                  ))}
                </Box>
              </Alert>
            )}

            {/* 🌤️ WEATHER CONDITIONS */}
            <Box sx={{
              p: 2,
              borderRadius: 3,
              background: 'linear-gradient(135deg, rgba(79,172,254,0.1) 0%, rgba(0,242,254,0.1) 100%)',
              border: '1px solid rgba(79,172,254,0.2)',
              mb: 2
            }}>
              <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: '#1e293b' }}>
                🌤️ Current Weather Conditions
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4">☀️</Typography>
                    <Typography variant="body2" fontWeight={600}>{weatherData.temperature}°C</Typography>
                  </Box>
                </Grid>
                <Grid item xs={9}>
                  <Typography variant="body2" sx={{ mb: 0.5 }}>
                    <strong>Condition:</strong> {weatherData.condition} • <strong>Humidity:</strong> {weatherData.humidity}%
                  </Typography>
                  <Typography variant="body2">
                    <strong>Work Suitability:</strong> {weatherData.workSuitability} • <strong>Wind:</strong> {weatherData.windSpeed} km/h
                  </Typography>
                </Grid>
              </Grid>
            </Box>

            {/* 👥 COLLABORATORS */}
            <Box sx={{
              p: 2,
              borderRadius: 3,
              background: 'linear-gradient(135deg, rgba(240,147,251,0.1) 0%, rgba(245,87,108,0.1) 100%)',
              border: '1px solid rgba(240,147,251,0.2)',
              mb: 2
            }}>
              <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: '#1e293b' }}>
                👥 Active Collaborators
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {collaborators.map((collaborator) => (
                  <Chip
                    key={collaborator.id}
                    avatar={
                      <Avatar sx={{
                        width: 24,
                        height: 24,
                        fontSize: '0.75rem',
                        background: collaborator.online ? 'linear-gradient(135deg, #4ade80 0%, #22c55e 100%)' : '#94a3b8'
                      }}>
                        {collaborator.avatar}
                      </Avatar>
                    }
                    label={`${collaborator.name} (${collaborator.role})`}
                    size="small"
                    sx={{
                      borderRadius: 2,
                      background: 'rgba(255,255,255,0.8)',
                      '& .MuiChip-label': { fontSize: '0.75rem' }
                    }}
                  />
                ))}
              </Box>
            </Box>
          </Paper>

          {/* Basic Information Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              📋 Basic Information
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="S.No"
                  value={formData.sNo}
                  onChange={(e) => handleInputChange('sNo', e.target.value)}
                  fullWidth
                  placeholder="Auto-generated if empty"
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Contractor Name *"
                  value={formData.contractorName}
                  onChange={(e) => handleInputChange('contractorName', e.target.value)}
                  fullWidth
                  required
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Node"
                  value={formData.node}
                  onChange={(e) => handleInputChange('node', e.target.value)}
                  fullWidth
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Pipe Diameter (MM)"
                  value={formData.pipeDiameter}
                  onChange={(e) => handleInputChange('pipeDiameter', e.target.value)}
                  fullWidth
                  type="number"
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  label="Units"
                  value={formData.units}
                  onChange={(e) => handleInputChange('units', e.target.value)}
                  fullWidth
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                >
                  <MenuItem value="RMT">RMT</MenuItem>
                  <MenuItem value="Kms">Kms</MenuItem>
                  <MenuItem value="Nos">Nos</MenuItem>
                  <MenuItem value="Cum">Cum</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Location *"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  fullWidth
                  required
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Engineer/Supervisor"
                  value={formData.engineerSupervisor}
                  onChange={(e) => handleInputChange('engineerSupervisor', e.target.value)}
                  fullWidth
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
            </Grid>
          </Paper>

          {/* HDPE Network Line Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              🔵 HDPE Network Line
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Excavation"
                  value={formData.hdpeExcavation}
                  onChange={(e) => handleInputChange('hdpeExcavation', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Pipe Laying"
                  value={formData.hdpePipeLaying}
                  onChange={(e) => handleInputChange('hdpePipeLaying', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Back Filling"
                  value={formData.hdpeBackFilling}
                  onChange={(e) => handleInputChange('hdpeBackFilling', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
            </Grid>
          </Paper>

          {/* MS Network Line Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              🔶 MS Network Line
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Excavation"
                  value={formData.msExcavation}
                  onChange={(e) => handleInputChange('msExcavation', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Pipe Laying"
                  value={formData.msPipeLaying}
                  onChange={(e) => handleInputChange('msPipeLaying', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Back Filling"
                  value={formData.msBackFilling}
                  onChange={(e) => handleInputChange('msBackFilling', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Work Description Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              📝 Work Description & Agency
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <TextField
                  label="Work Description *"
                  value={formData.workDescription}
                  onChange={(e) => handleInputChange('workDescription', e.target.value)}
                  fullWidth
                  required
                  multiline
                  rows={4}
                  placeholder="e.g., MS & HDPE Pipe Network Line Excavation & Laying work in Progress..."
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  label="Agency Name *"
                  value={formData.agencyName}
                  onChange={(e) => handleInputChange('agencyName', e.target.value)}
                  fullWidth
                  required
                  placeholder="e.g., MANTENA INFRASOL PVT LTD"
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Remarks"
                  value={formData.remarks}
                  onChange={(e) => handleInputChange('remarks', e.target.value)}
                  fullWidth
                  multiline
                  rows={2}
                  placeholder="Additional remarks or notes..."
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Summary Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              📊 Progress Summary
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label="Target Quantity"
                  value={formData.targetQty}
                  onChange={(e) => handleInputChange('targetQty', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label="Last Season Quantity"
                  value={formData.lastSeasonQty}
                  onChange={(e) => handleInputChange('lastSeasonQty', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label="This Season Quantity"
                  value={formData.thisSeasonQty}
                  onChange={(e) => handleInputChange('thisSeasonQty', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label="Cumulative Quantity"
                  value={formData.cumulativeQty}
                  onChange={(e) => handleInputChange('cumulativeQty', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label="Balance Quantity"
                  value={formData.balanceQty}
                  fullWidth
                  type="number"
                  InputProps={{ readOnly: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: '#f8fafc'
                    }
                  }}
                  helperText="Auto-calculated: Target - Cumulative"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Labour Details Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              👷 Labour Details
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Skilled Labour"
                  value={formData.skilledLabour}
                  onChange={(e) => handleInputChange('skilledLabour', parseInt(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Unskilled Labour"
                  value={formData.unskilledLabour}
                  onChange={(e) => handleInputChange('unskilledLabour', parseInt(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Total Labour"
                  value={formData.totalLabour}
                  fullWidth
                  type="number"
                  InputProps={{ readOnly: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: '#f8fafc'
                    }
                  }}
                  helperText="Auto-calculated: Skilled + Unskilled"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Additional Details Section */}
          <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              🔧 Additional Details
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Concrete Quantity (Cum)"
                  value={formData.concreteQty}
                  onChange={(e) => handleInputChange('concreteQty', parseFloat(e.target.value) || 0)}
                  fullWidth
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Machinery Details"
                  value={formData.machineryDetails}
                  onChange={(e) => handleInputChange('machineryDetails', e.target.value)}
                  fullWidth
                  placeholder="e.g., Excavator, Dumper, ROC Machine"
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
            </Grid>
          </Paper>

          {/* 🌟 PREMIUM QUALITY & SAFETY ASSESSMENT */}
          <Paper sx={{
            p: 3,
            mb: 3,
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            background: 'linear-gradient(135deg, rgba(250,112,154,0.05) 0%, rgba(254,225,64,0.05) 100%)'
          }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              🌟 Quality & Safety Assessment
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                  ⭐ Work Quality Rating
                </Typography>
                <Rating
                  value={qualityRating}
                  onChange={(event, newValue) => setQualityRating(newValue)}
                  precision={0.5}
                  size="large"
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  Rate the overall quality of work completed today
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                  🛡️ Safety Score (%)
                </Typography>
                <Slider
                  value={safetyScore}
                  onChange={(event, newValue) => setSafetyScore(newValue)}
                  valueLabelDisplay="on"
                  min={0}
                  max={100}
                  marks={[
                    { value: 0, label: '0%' },
                    { value: 50, label: '50%' },
                    { value: 100, label: '100%' }
                  ]}
                  sx={{
                    color: safetyScore >= 90 ? '#22c55e' : safetyScore >= 70 ? '#f59e0b' : '#ef4444',
                    '& .MuiSlider-thumb': {
                      background: safetyScore >= 90 ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)' :
                                 safetyScore >= 70 ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' :
                                 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
                    }
                  }}
                />
                <Typography variant="body2" sx={{ color: '#64748b', mt: 1 }}>
                  Assess safety compliance and practices
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                  ✅ Compliance Checklist
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={complianceChecks.safetyCompliance}
                          onChange={(e) => setComplianceChecks({
                            ...complianceChecks,
                            safetyCompliance: e.target.checked
                          })}
                          color="success"
                        />
                      }
                      label="Safety Compliance"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={complianceChecks.environmentalCompliance}
                          onChange={(e) => setComplianceChecks({
                            ...complianceChecks,
                            environmentalCompliance: e.target.checked
                          })}
                          color="success"
                        />
                      }
                      label="Environmental"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={complianceChecks.qualityStandards}
                          onChange={(e) => setComplianceChecks({
                            ...complianceChecks,
                            qualityStandards: e.target.checked
                          })}
                          color="success"
                        />
                      }
                      label="Quality Standards"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={complianceChecks.regulatoryApproval}
                          onChange={(e) => setComplianceChecks({
                            ...complianceChecks,
                            regulatoryApproval: e.target.checked
                          })}
                          color="success"
                        />
                      }
                      label="Regulatory"
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Paper>

          {/* 📎 PREMIUM ATTACHMENTS & MEDIA */}
          <Paper sx={{
            p: 3,
            mb: 3,
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            background: 'linear-gradient(135deg, rgba(79,172,254,0.05) 0%, rgba(0,242,254,0.05) 100%)'
          }}>
            <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
              📎 Attachments & Media
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <Button
                  variant="outlined"
                  startIcon={<PhotoIcon />}
                  fullWidth
                  sx={{
                    borderRadius: 3,
                    borderColor: '#4facfe',
                    color: '#4facfe',
                    borderStyle: 'dashed',
                    height: 80,
                    '&:hover': {
                      borderColor: '#00f2fe',
                      backgroundColor: 'rgba(79,172,254,0.05)'
                    }
                  }}
                >
                  Add Photos
                </Button>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Button
                  variant="outlined"
                  startIcon={<AttachFileIcon />}
                  fullWidth
                  sx={{
                    borderRadius: 3,
                    borderColor: '#f093fb',
                    color: '#f093fb',
                    borderStyle: 'dashed',
                    height: 80,
                    '&:hover': {
                      borderColor: '#f5576c',
                      backgroundColor: 'rgba(240,147,251,0.05)'
                    }
                  }}
                >
                  Attach Files
                </Button>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Button
                  variant="outlined"
                  startIcon={<MapIcon />}
                  fullWidth
                  sx={{
                    borderRadius: 3,
                    borderColor: '#fa709a',
                    color: '#fa709a',
                    borderStyle: 'dashed',
                    height: 80,
                    '&:hover': {
                      borderColor: '#fee140',
                      backgroundColor: 'rgba(250,112,154,0.05)'
                    }
                  }}
                >
                  GPS Location
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Form Summary */}
          <Paper sx={{ p: 3, borderRadius: 3, border: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 2 }}>
              📌 <strong>Required Fields:</strong> Contractor Name, Location, Work Description, Agency Name
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b' }}>
              💡 <strong>Note:</strong> Balance Quantity and Total Labour are auto-calculated based on your inputs.
            </Typography>
          </Paper>
        </Box>
      </PremiumDialog>

      {/* 🚀 PREMIUM FLOATING ACTION MENU */}
      <SpeedDial
        ariaLabel="Quick Actions"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          '& .MuiFab-primary': {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            boxShadow: '0 8px 32px rgba(102,126,234,0.4)',
            '&:hover': {
              background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
              transform: 'scale(1.1)',
              boxShadow: '0 12px 40px rgba(102,126,234,0.5)'
            }
          }
        }}
        icon={<SpeedDialIcon />}
        direction="up"
      >
        <SpeedDialAction
          icon={<AddIcon />}
          tooltipTitle="Add New DPR"
          onClick={() => setOpenDialog(true)}
          sx={{
            '& .MuiFab-primary': {
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              '&:hover': { transform: 'scale(1.1)' }
            }
          }}
        />
        <SpeedDialAction
          icon={<AnalyticsIcon />}
          tooltipTitle="View Analytics"
          onClick={() => setCurrentTab(2)}
          sx={{
            '& .MuiFab-primary': {
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              '&:hover': { transform: 'scale(1.1)' }
            }
          }}
        />
        <SpeedDialAction
          icon={<ExportIcon />}
          tooltipTitle="Export Reports"
          onClick={() => {
            setSnackbar({
              open: true,
              message: 'Export feature coming soon!',
              severity: 'info'
            });
          }}
          sx={{
            '& .MuiFab-primary': {
              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
              '&:hover': { transform: 'scale(1.1)' }
            }
          }}
        />
        <SpeedDialAction
          icon={<NotificationsIcon />}
          tooltipTitle="Notifications"
          onClick={() => {
            setSnackbar({
              open: true,
              message: `You have ${notifications.length} new notifications`,
              severity: 'info'
            });
          }}
          sx={{
            '& .MuiFab-primary': {
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
              '&:hover': { transform: 'scale(1.1)' }
            }
          }}
        />
      </SpeedDial>

      {/* 🎉 PREMIUM SNACKBAR NOTIFICATIONS */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            '& .MuiAlert-icon': { fontSize: 24 }
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* 🎯 PREMIUM LOADING OVERLAY */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(15, 23, 42, 0.8)',
            backdropFilter: 'blur(8px)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999
          }}
        >
          <Box sx={{ textAlign: 'center', color: 'white' }}>
            <CircularProgress
              size={60}
              sx={{
                color: '#667eea',
                mb: 2
              }}
            />
            <Typography variant="h6" fontWeight={600}>
              Processing your request...
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
              Please wait while we update your data
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default DailyProgressReport;
