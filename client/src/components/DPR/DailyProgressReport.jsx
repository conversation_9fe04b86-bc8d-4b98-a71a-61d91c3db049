import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Divider,
  LinearProgress,
  Avatar,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Assessment as ReportIcon,
  Construction as ConstructionIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  LocationOn as LocationIcon,
  Engineering as EngineeringIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import PremiumDialog from '../common/PremiumDialog';

const DailyProgressReport = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filterLocation, setFilterLocation] = useState('all');
  const [filterContractor, setFilterContractor] = useState('all');

  // Sample data structure based on the Excel format
  const [sampleReports] = useState([
    {
      id: 1,
      date: '2025-01-06',
      contractor: 'Nikhil Construction & Computers',
      node: '5481-5486',
      pipeDiameter: 160,
      units: 'RMT',
      excavation: { target: 354, completed: 354, cumulative: 354 },
      pipeLaying: { target: 354, completed: 354, cumulative: 354 },
      backFilling: { target: 354, completed: 354, cumulative: 354 },
      location: 'Pandhurna',
      remarks: 'Work in Progress',
      engineer: 'Mohan Yadav',
      status: 'active'
    },
    {
      id: 2,
      date: '2025-01-06',
      contractor: 'GR Construction',
      node: '5125-5126',
      pipeDiameter: 110,
      units: 'RMT',
      excavation: { target: 200, completed: 180, cumulative: 180 },
      pipeLaying: { target: 200, completed: 160, cumulative: 160 },
      backFilling: { target: 200, completed: 140, cumulative: 140 },
      location: 'Pandhurna',
      remarks: 'Work in Progress',
      engineer: 'Mohan Yadav',
      status: 'active'
    },
    {
      id: 3,
      date: '2025-01-06',
      contractor: 'Shukla Construction',
      node: '2127-2128',
      pipeDiameter: 200,
      units: 'RMT',
      excavation: { target: 200, completed: 200, cumulative: 200 },
      pipeLaying: { target: 200, completed: 200, cumulative: 200 },
      backFilling: { target: 200, completed: 200, cumulative: 200 },
      location: 'Pandhurna',
      remarks: 'Work in Progress',
      engineer: 'Mohan Yadav',
      status: 'completed'
    }
  ]);

  useEffect(() => {
    setReports(sampleReports);
  }, [sampleReports]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'warning';
      case 'pending': return 'error';
      default: return 'default';
    }
  };

  const calculateProgress = (completed, target) => {
    return target > 0 ? Math.round((completed / target) * 100) : 0;
  };

  const getProgressColor = (progress) => {
    if (progress >= 90) return 'success';
    if (progress >= 70) return 'warning';
    return 'error';
  };

  const filteredReports = reports.filter(report => {
    const dateMatch = report.date === selectedDate;
    const locationMatch = filterLocation === 'all' || report.location === filterLocation;
    const contractorMatch = filterContractor === 'all' || report.contractor === filterContractor;
    return dateMatch && locationMatch && contractorMatch;
  });

  const uniqueLocations = [...new Set(reports.map(r => r.location))];
  const uniqueContractors = [...new Set(reports.map(r => r.contractor))];

  const totalStats = filteredReports.reduce((acc, report) => {
    acc.totalExcavation += report.excavation.completed;
    acc.totalPipeLaying += report.pipeLaying.completed;
    acc.totalBackFilling += report.backFilling.completed;
    acc.targetExcavation += report.excavation.target;
    acc.targetPipeLaying += report.pipeLaying.target;
    acc.targetBackFilling += report.backFilling.target;
    return acc;
  }, {
    totalExcavation: 0,
    totalPipeLaying: 0,
    totalBackFilling: 0,
    targetExcavation: 0,
    targetPipeLaying: 0,
    targetBackFilling: 0
  });

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                width: 56,
                height: 56,
              }}
            >
              <ReportIcon fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} sx={{ color: '#1e293b', mb: 0.5 }}>
                📊 Daily Progress Report
              </Typography>
              <Typography variant="body1" sx={{ color: '#64748b' }}>
                Track construction progress and monitor project milestones
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Print Report
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Export Excel
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenDialog(true)}
              sx={{
                borderRadius: 3,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 12px 32px rgba(102, 126, 234, 0.4)',
                },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              }}
            >
              Add New Entry
            </Button>
          </Box>
        </Box>

        {/* Filters */}
        <Paper
          sx={{
            p: 3,
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 8px 24px rgba(15, 23, 42, 0.1)',
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                label="Report Date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Location"
                value={filterLocation}
                onChange={(e) => setFilterLocation(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <MenuItem value="all">All Locations</MenuItem>
                {uniqueLocations.map(location => (
                  <MenuItem key={location} value={location}>{location}</MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Contractor"
                value={filterContractor}
                onChange={(e) => setFilterContractor(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <MenuItem value="all">All Contractors</MenuItem>
                {uniqueContractors.map(contractor => (
                  <MenuItem key={contractor} value={contractor}>{contractor}</MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarIcon sx={{ color: '#64748b' }} />
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  {filteredReports.length} entries found
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(102, 126, 234, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <ConstructionIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Excavation Progress
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalExcavation.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  of {totalStats.targetExcavation.toLocaleString()} RMT target
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalExcavation, totalStats.targetExcavation)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(240, 147, 251, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <EngineeringIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Pipe Laying Progress
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalPipeLaying.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  of {totalStats.targetPipeLaying.toLocaleString()} RMT target
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalPipeLaying, totalStats.targetPipeLaying)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <Card
              sx={{
                borderRadius: 4,
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                boxShadow: '0 20px 40px -12px rgba(79, 172, 254, 0.3)',
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <TrendingUpIcon fontSize="large" />
                  <Typography variant="h6" fontWeight={600}>
                    Back Filling Progress
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                  {totalStats.totalBackFilling.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  of {totalStats.targetBackFilling.toLocaleString()} RMT target
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress(totalStats.totalBackFilling, totalStats.targetBackFilling)}
                  sx={{
                    mt: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Main Data Table */}
      <Paper
        sx={{
          borderRadius: 4,
          background: `
            linear-gradient(135deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 100%
            )
          `,
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(148, 163, 184, 0.2)',
          boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ p: 3, borderBottom: '1px solid rgba(148, 163, 184, 0.1)' }}>
          <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
            📋 Daily Progress Entries - {new Date(selectedDate).toLocaleDateString()}
          </Typography>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'rgba(148, 163, 184, 0.05)' }}>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>S.No</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Contractor</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Node</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Pipe Dia (MM)</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Units</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Excavation</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Pipe Laying</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Back Filling</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Location</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredReports.map((report, index) => (
                <motion.tr
                  key={report.id}
                  component={TableRow}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  sx={{
                    '&:hover': {
                      backgroundColor: 'rgba(102, 126, 234, 0.05)',
                    },
                  }}
                >
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          fontSize: '0.875rem',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        }}
                      >
                        {report.contractor.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight={600}>
                          {report.contractor.length > 20
                            ? `${report.contractor.substring(0, 20)}...`
                            : report.contractor}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {report.engineer}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={report.node}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        color: '#667eea',
                        fontWeight: 600,
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight={600}>
                      {report.pipeDiameter}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={report.units}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {report.excavation.completed}/{report.excavation.target}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {calculateProgress(report.excavation.completed, report.excavation.target)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={calculateProgress(report.excavation.completed, report.excavation.target)}
                        color={getProgressColor(calculateProgress(report.excavation.completed, report.excavation.target))}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {report.pipeLaying.completed}/{report.pipeLaying.target}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {calculateProgress(report.pipeLaying.completed, report.pipeLaying.target)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={calculateProgress(report.pipeLaying.completed, report.pipeLaying.target)}
                        color={getProgressColor(calculateProgress(report.pipeLaying.completed, report.pipeLaying.target))}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {report.backFilling.completed}/{report.backFilling.target}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {calculateProgress(report.backFilling.completed, report.backFilling.target)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={calculateProgress(report.backFilling.completed, report.backFilling.target)}
                        color={getProgressColor(calculateProgress(report.backFilling.completed, report.backFilling.target))}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationIcon sx={{ color: '#64748b', fontSize: 16 }} />
                      <Typography variant="body2">{report.location}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={report.status}
                      size="small"
                      color={getStatusColor(report.status)}
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          sx={{
                            color: '#667eea',
                            '&:hover': {
                              backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            },
                          }}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Entry">
                        <IconButton
                          size="small"
                          sx={{
                            color: '#f093fb',
                            '&:hover': {
                              backgroundColor: 'rgba(240, 147, 251, 0.1)',
                            },
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </motion.tr>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {filteredReports.length === 0 && (
          <Box sx={{ p: 6, textAlign: 'center' }}>
            <ReportIcon sx={{ fontSize: 64, color: '#cbd5e1', mb: 2 }} />
            <Typography variant="h6" sx={{ color: '#64748b', mb: 1 }}>
              No progress entries found
            </Typography>
            <Typography variant="body2" sx={{ color: '#94a3b8' }}>
              Add new entries to track daily construction progress
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Add Entry Dialog */}
      <PremiumDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        title="Add New Progress Entry"
        titleIcon="📊"
        maxWidth="lg"
        actions={
          <>
            <Button
              onClick={() => setOpenDialog(false)}
              sx={{
                borderRadius: 2,
                color: '#64748b',
                borderColor: '#e2e8f0',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                }
              }}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                },
              }}
            >
              Save Entry
            </Button>
          </>
        }
      >
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          DPR Entry Form will be implemented here with all the required fields
          matching the Excel format structure.
        </Typography>
      </PremiumDialog>
    </Box>
  );
};

export default DailyProgressReport;
