{"common": {"welcome": "Welcome", "login": "<PERSON><PERSON>", "logout": "Logout", "dashboard": "Dashboard", "settings": "Settings", "profile": "Profile", "language": "Language", "totalProducts": "Total Products", "acrossCategories": "Across {{count}} categories", "lowStockItems": "Low Stock Items", "requireImmediateAttention": "Require immediate attention", "totalStockValue": "Total Stock Value", "averageStockLevel": "Average Stock Level", "unitsPerProduct": "Units per product", "searchProducts": "Search products...", "category": "Category", "allCategories": "All Categories", "createTransferRequest": "Create Transfer Request", "productName": "Product Name", "sku": "SKU", "stockLevel": "Stock Level", "price": "Price", "status": "Status", "actions": "Actions", "inStock": "In Stock", "lowStock": "Low Stock", "updateStock": "Update Stock", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "add": "Add", "update": "Update", "create": "Create", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "selectAll": "Select All", "none": "None", "all": "All", "name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "role": "Role", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "date": "Date", "time": "Time", "amount": "Amount", "quantity": "Quantity", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "description": "Description", "notes": "Notes", "address": "Address", "phone": "Phone", "mobile": "Mobile", "website": "Website", "company": "Company", "department": "Department", "position": "Position", "location": "Location", "city": "City", "state": "State", "country": "Country", "pincode": "Pincode", "required": "Required", "optional": "Optional", "pleaseSelect": "Please select", "pleaseEnter": "Please enter", "invalidEmail": "Invalid email address", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "fieldRequired": "This field is required", "invalidInput": "Invalid input", "operationSuccessful": "Operation completed successfully", "operationFailed": "Operation failed", "noDataFound": "No data found", "noResultsFound": "No results found", "dataLoadingError": "Error loading data", "networkError": "Network error occurred", "serverError": "Server error occurred", "unauthorized": "Unauthorized access", "accessDenied": "Access denied", "sessionExpired": "Session expired", "pleaseLoginAgain": "Please login again"}, "app": {"title": "MANTENA"}, "menu": {"dashboard": "Dashboard", "inventory": "Inventory", "productManagement": "Stock Management", "productRegistration": "Product Management", "initiateTransfer": "Initiate Transfer", "receivedTransfers": "Received Transfers", "storeRegistration": "Store Registration", "security": "Security", "userManagement": "User Management", "roleManagement": "Role Management", "billDetails": "<PERSON>", "machineryWork": "Plant & Machinery", "charts": "Charts", "reports": "Reports", "advancedReports": "Advanced Reports", "customerAnalysis": "Customer Analysis", "supplierAnalysis": "Supplier Analysis", "recommendation": "Recommendations", "activityLog": "Activity Log", "logout": "Logout"}, "dashboard": {"totalProducts": "Total Products", "lowStockItems": "Low Stock Items", "totalStockValue": "Total Stock Value", "averageStockLevel": "Average Stock Level", "expiryStatus": "Expiry Status", "lowStock": "Low Stock", "createTransferRequest": "Create Transfer Request"}, "table": {"productName": "Product Name", "store": "Store", "sku": "SKU", "category": "Category", "stockLevel": "Stock Level", "price": "Price", "mfgDate": "Mfg Date", "expiryDate": "Expiry Date", "status": "Status", "actions": "Actions", "pageInfo": "Page {{page}} of {{total}}"}, "status": {"inStock": "In Stock", "lowStock": "Low Stock"}, "actions": {"updateStock": "Update Stock", "exportList": "Export List", "addProduct": "Add Stock", "previous": "Previous", "next": "Next"}, "inventory": {"title": "Inventory Management"}, "search": {"placeholder": "Search products..."}, "filter": {"category": "Category", "allCategories": "All Categories"}, "categories": {"laying": "Laying", "electricalMaterial": "Electrical Material", "structureMaterial": "Structure Material", "plumbingMaterials": "Plumbing Materials", "toolsAndEquipment": "Tools and Equipment"}, "login": {"title": "Sign In", "subtitle": "Sign in to your account", "welcomeTo": "WELCOME TO", "mantena": "MANTENA", "infrastructureExcellence": "Infrastructure Excellence", "companyDescription": "Leading construction and infrastructure development with innovative solutions and premium quality standards", "emailAddress": "Email Address", "enterEmail": "Enter your email address", "enterPassword": "Enter your password", "signIn": "Sign In", "register": "Register", "createAccount": "Create Account", "createNewAccount": "Create a new account", "fullName": "Full Name", "enterFullName": "Enter your full name", "enterNewPassword": "Enter new password", "confirmNewPassword": "Confirm new password", "passwordMinLength": "Password must be at least 8 characters long", "registrationNote": "By registering, you'll be assigned a basic user role.", "contactAdmin": "For elevated permissions, contact your administrator.", "loginError": "<PERSON><PERSON> failed. Please check your credentials.", "registrationSuccess": "Registration successful! Please login with your credentials.", "registrationError": "Registration failed. Please try again.", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInHere": "Sign in here", "registerHere": "Register here"}, "userManagement": {"title": "User Management", "subtitle": "Manage user accounts, roles, and permissions", "addNewUser": "Add New User", "editUser": "Edit User", "userInformation": "User Information", "securitySettings": "Security Settings", "changePassword": "Change Password (Optional)", "leaveBlankToKeep": "Leave blank to keep current password", "userRole": "User Role", "accountStatus": "Account Status", "accountActive": "Account Active", "accountInactive": "Account Inactive", "updateUser": "Update User", "addUser": "Add User", "deleteUser": "Delete User", "activateUser": "Activate User", "deactivateUser": "Deactivate User", "totalUsers": "Total Users", "activeUsers": "Active Users", "inactiveUsers": "Inactive Users", "adminUsers": "Admin Users", "noUsersFound": "No users found", "addFirstUser": "Add your first user to get started", "userAddedSuccessfully": "User added successfully", "userUpdatedSuccessfully": "User updated successfully", "userDeletedSuccessfully": "User deleted successfully", "userStatusUpdated": "User status updated successfully", "confirmDeleteUser": "Are you sure you want to delete this user?", "cannotDeleteYourself": "You cannot delete your own account", "loadingUsers": "Loading users...", "refreshUsers": "Refresh Users", "userCreationFailed": "Failed to create user", "userUpdateFailed": "Failed to update user", "userDeletionFailed": "Failed to delete user", "invalidUserData": "Invalid user data provided", "emailAlreadyExists": "Email address already exists", "weakPassword": "Password is too weak", "roles": {"superadmin": "Super Administrator", "admin": "Administrator", "siteengineer": "Site Engineer", "storekeeper": "Store Keeper", "user": "Regular User"}}, "billDetails": {"title": "<PERSON>", "subtitle": "Manage bills and invoices", "billNumber": "<PERSON>", "invoiceNumber": "Invoice Number", "vendorName": "Vendor Name", "vendorDetails": "<PERSON><PERSON><PERSON>", "billDate": "<PERSON>", "dueDate": "Due Date", "paymentMethod": "Payment Method", "paymentType": "Payment Type", "paymentStatus": "Payment Status", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "dueAmount": "Due Amount", "unitPrice": "Unit Price", "productDetails": "Product Details", "generateBill": "Generate Bill", "printBill": "Print Bill", "downloadPDF": "Download PDF", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "markAsUnpaid": "<PERSON> as Unpaid", "paymentReceived": "Payment Received", "paymentPending": "Payment Pending", "overdue": "Overdue", "paid": "Paid", "unpaid": "Unpaid", "partial": "Partial", "cash": "Cash", "card": "Card", "upi": "UPI", "netBanking": "Net Banking", "cheque": "Cheque", "other": "Other", "billGenerated": "Bill generated successfully", "billUpdated": "Bill updated successfully", "paymentUpdated": "Payment status updated", "noBillsFound": "No bills found", "loadingBills": "Loading bills...", "dueSummary": "Due Summary", "overdueAmount": "Overdue Amount", "daysOverdue": "Days Overdue", "billCount": "<PERSON>", "vendorSummary": "<PERSON><PERSON><PERSON>"}, "productManagement": {"title": "Product Management", "subtitle": "Manage products and inventory", "addNewProduct": "Add New Product", "editProduct": "Edit Product", "productInformation": "Product Information", "pricing": "Pricing", "inventory": "Inventory", "dates": "Dates", "enterProductName": "Enter product name", "enterSKU": "Enter SKU", "selectStore": "Select a store", "selectCategory": "Select category", "currentStock": "Current Stock", "lowStockAlert": "Low Stock Alert", "manufacturingDate": "Manufacturing Date", "expiryDate": "Expiry Date", "saveChanges": "Save Changes", "productAddedSuccessfully": "Product added successfully!", "productUpdatedSuccessfully": "Product updated successfully!", "productDeletedSuccessfully": "Product deleted successfully!", "confirmDeleteProduct": "Are you sure you want to delete this product?", "noProductsFound": "No products found", "loadingProducts": "Loading products...", "outOfStock": "Out of Stock", "lowStockWarning": "Low stock warning", "stockUpdated": "Stock updated successfully", "invalidProductData": "Invalid product data", "duplicateSKU": "SKU already exists", "productCreationFailed": "Failed to create product", "productUpdateFailed": "Failed to update product"}, "storeRegistration": {"title": "Store Registration", "subtitle": "Register and manage store locations", "addNewStore": "Add New Store", "editStore": "Edit Store", "storeInformation": "Store Information", "contactInformation": "Contact Information", "storeName": "Store Name", "storeCode": "Store Code", "storeType": "Store Type", "managerName": "Manager Name", "contactNumber": "Contact Number", "emailAddress": "Email Address", "fullAddress": "Full Address", "landmark": "Landmark", "operatingHours": "Operating Hours", "storeCapacity": "Store Capacity", "registrationDate": "Registration Date", "storeStatus": "Store Status", "operational": "Operational", "underMaintenance": "Under Maintenance", "closed": "Closed", "storeRegistered": "Store registered successfully", "storeUpdated": "Store updated successfully", "storeDeleted": "Store deleted successfully", "noStoresFound": "No stores found", "loadingStores": "Loading stores...", "invalidStoreData": "Invalid store data", "storeCodeExists": "Store code already exists", "storeRegistrationFailed": "Store registration failed"}, "transfers": {"title": "Transfer Management", "initiateTransfer": "Initiate Transfer", "receivedTransfers": "Received Transfers", "transferRequest": "Transfer Request", "transferDetails": "Transfer Details", "sourceStore": "Source Store", "destinationStore": "Destination Store", "transferDate": "Transfer Date", "requestedBy": "Requested By", "approvedBy": "Approved By", "transferStatus": "Transfer Status", "transferQuantity": "Transfer Quantity", "availableStock": "Available Stock", "requestedQuantity": "Requested Quantity", "approvedQuantity": "Approved Quantity", "transferReason": "Transfer Reason", "transferNotes": "Transfer Notes", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "inTransit": "In Transit", "completed": "Completed", "cancelled": "Cancelled", "approveTransfer": "Approve Transfer", "rejectTransfer": "Reject Transfer", "completeTransfer": "Complete Transfer", "cancelTransfer": "Cancel Transfer", "transferInitiated": "Transfer request initiated successfully", "transferApproved": "Transfer approved successfully", "transferRejected": "Transfer rejected successfully", "transferCompleted": "Transfer completed successfully", "transferCancelled": "Transfer cancelled successfully", "noTransfersFound": "No transfers found", "loadingTransfers": "Loading transfers...", "insufficientStock": "Insufficient stock for transfer", "invalidTransferData": "Invalid transfer data", "transferFailed": "Transfer operation failed", "selectSourceStore": "Select source store", "selectDestinationStore": "Select destination store", "enterQuantity": "Enter quantity", "enterReason": "Enter transfer reason"}, "machineryWork": {"title": "Plant & Machinery", "subtitle": "Manage machinery operations and maintenance", "addNewLog": "Add New Log", "editLog": "Edit Log", "machineryDetails": "Machinery Details", "operationDetails": "Operation Details", "machineryName": "Machinery Name", "machineryType": "Machinery Type", "operatorName": "Operator Name", "workDescription": "Work Description", "startTime": "Start Time", "endTime": "End Time", "totalHours": "Total Hours", "fuelUsed": "Fuel Used", "maintenanceNotes": "Maintenance Notes", "workStatus": "Work Status", "operational": "Operational", "maintenance": "Under Maintenance", "breakdown": "Breakdown", "idle": "Idle", "logAdded": "Machinery log added successfully", "logUpdated": "Machinery log updated successfully", "logDeleted": "Machinery log deleted successfully", "noLogsFound": "No machinery logs found", "loadingLogs": "Loading machinery logs...", "totalMachinery": "Total Machinery", "activeMachinery": "Active Machinery", "totalFuelUsed": "Total Fuel Used", "totalWorkHours": "Total Work Hours", "maintenanceDue": "Maintenance Due", "efficiency": "Efficiency", "utilizationRate": "Utilization Rate", "fuelEfficiency": "Fuel Efficiency", "liters": "Liters", "hours": "Hours", "kilometers": "Kilometers"}, "roleManagement": {"title": "Role Management", "subtitle": "Manage user roles and permissions", "createNewRole": "Create New Role", "editRole": "Edit Role", "roleDetails": "Role Details", "permissions": "Permissions", "roleName": "Role Name", "displayName": "Display Name", "roleDescription": "Role Description", "systemRole": "System Role", "customRole": "Custom Role", "permissionCategories": "Permission Categories", "dashboard": "Dashboard", "inventory": "Inventory", "users": "Users", "bills": "Bills", "reports": "Reports", "settings": "Settings", "view": "View", "create": "Create", "edit": "Edit", "delete": "Delete", "approve": "Approve", "export": "Export", "roleCreated": "Role created successfully", "roleUpdated": "Role updated successfully", "roleDeleted": "Role deleted successfully", "cannotDeleteSystemRole": "Cannot delete system role", "cannotEditSystemRole": "Cannot edit system role", "roleInUse": "Role is currently in use", "noRolesFound": "No roles found", "loadingRoles": "Loading roles...", "permissionStrength": "Permission Strength", "highAccess": "High Access", "mediumAccess": "Medium Access", "lowAccess": "Low Access", "noAccess": "No Access"}}