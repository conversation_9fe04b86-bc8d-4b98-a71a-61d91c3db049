# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.1](https://github.com/inspect-js/which-builtin-type/compare/v1.2.0...v1.2.1) - 2024-12-12

### Commits

- [meta] sort package.json [`8305bf9`](https://github.com/inspect-js/which-builtin-type/commit/8305bf9a47674564c84dde5856829444ac59e379)
- [actions] re-add finishers [`67140db`](https://github.com/inspect-js/which-builtin-type/commit/67140dba99e8f204c6f1abce315711fae4239032)
- [<PERSON>] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/function.prototype.name`, `@types/tape`, `has-symbols` [`5adff02`](https://github.com/inspect-js/which-builtin-type/commit/5adff02870f00eafe144250b0ea2181f59b0d337)
- [Deps] update `call-bind`, `is-date-object`, `is-regex`, `which-boxed-primitive` [`87922d3`](https://github.com/inspect-js/which-builtin-type/commit/87922d3a0a21ee2b0991d9f3832123f5ff149eed)
- [Refactor] use `call-bound` directly [`8f633bc`](https://github.com/inspect-js/which-builtin-type/commit/8f633bc7587f481877f3965fdf0c74885753d824)
- [Deps] update `is-regex`, `which-typed-array` [`a912742`](https://github.com/inspect-js/which-builtin-type/commit/a9127421b702943a57927bf3e4d867cdc5862419)

## [v1.2.0](https://github.com/inspect-js/which-builtin-type/compare/v1.1.4...v1.2.0) - 2024-11-23

### Commits

- [New] add types [`d4aa2db`](https://github.com/inspect-js/which-builtin-type/commit/d4aa2db289a8d97c6917c0edb1edcb32001488a8)
- [actions] split out node 10-20, and 20+ [`7b3d28b`](https://github.com/inspect-js/which-builtin-type/commit/7b3d28bcb1258cdfd5a7df5606622351596fb442)
- [Refactor] use `callBound` to cache Promise#then [`a000377`](https://github.com/inspect-js/which-builtin-type/commit/a0003772dabdd5fd4b84c2ed8e139df008a437e8)
- [Dev Deps] update `auto-changelog`, `object-inspect`, `tape` [`37062d2`](https://github.com/inspect-js/which-builtin-type/commit/37062d280a8c36c1bd96b6cfbe053f10cf8d71b6)
- [Tests] replace `aud` with `npm audit` [`337aac1`](https://github.com/inspect-js/which-builtin-type/commit/337aac1d88fc63d935bd15a97926e9b0b462a735)
- [Deps] update `is-finalizationregistry` [`4ef8763`](https://github.com/inspect-js/which-builtin-type/commit/4ef8763a19709df7ad50d790bd2b724f531a65d9)
- [Dev Deps] add missing peer dep [`1cb2842`](https://github.com/inspect-js/which-builtin-type/commit/1cb28421438eb4ee8f0f284b535f1cfc6b9c9757)

## [v1.1.4](https://github.com/inspect-js/which-builtin-type/compare/v1.1.3...v1.1.4) - 2024-07-29

### Commits

- [readme] fix URLs [`f26fc22`](https://github.com/inspect-js/which-builtin-type/commit/f26fc2243220277874e36ace85f48f87ec8ab502)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `available-typed-arrays`, `npmignore`, `object-inspect`, `object.assign`, `tape` [`f724135`](https://github.com/inspect-js/which-builtin-type/commit/f724135d44efd74ee067102c8c499566d73b849c)
- [Deps] update `function.prototype.name`, `has-tostringtag`, `which-collection`, `which-typed-array` [`831119b`](https://github.com/inspect-js/which-builtin-type/commit/831119b76ce79004c368deab141e8a9bd680950c)

## [v1.1.3](https://github.com/inspect-js/which-builtin-type/compare/v1.1.2...v1.1.3) - 2022-11-02

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`0ccf168`](https://github.com/inspect-js/which-builtin-type/commit/0ccf168604ddaab1d40de8fde5140f5dca942c5b)
- [Dev Deps] update `aud`, `has-bigints`, `has-symbols`, `in-publish`, `object-inspect`, `object.assign`, `tape` [`2c87b2e`](https://github.com/inspect-js/which-builtin-type/commit/2c87b2ed58c397a26953dbe2cb2aae452e32ee21)
- [actions] update rebase action to use reusable workflow [`ab27caf`](https://github.com/inspect-js/which-builtin-type/commit/ab27caf0d6cd1fccc5738fda4cebee7bf47eca14)
- [meta] simplify `exports` [`680d056`](https://github.com/inspect-js/which-builtin-type/commit/680d05674f979c1a02ae7c6b9f885114e962324e)
- [Deps] update `which-typed-array` [`3ca0216`](https://github.com/inspect-js/which-builtin-type/commit/3ca02166585c6e7350fe0bc7518fccce269fc5ac)
- [meta] add `sideEffects` flag [`caa2221`](https://github.com/inspect-js/which-builtin-type/commit/caa22214c751674f1959944a0ece81b032141e3b)

## [v1.1.2](https://github.com/inspect-js/which-builtin-type/compare/v1.1.1...v1.1.2) - 2022-04-12

### Commits

- [actions] reuse common workflows [`b1b60aa`](https://github.com/inspect-js/which-builtin-type/commit/b1b60aa01b20f613a97b456c3062412f0aba67dd)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`8115a20`](https://github.com/inspect-js/which-builtin-type/commit/8115a20941ca702c1a927a0aea08ffa0b37821c0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`86b3c3b`](https://github.com/inspect-js/which-builtin-type/commit/86b3c3ba155a2a292a69d507be85ede12896163d)
- [actions] update codecov uploader [`de30f4b`](https://github.com/inspect-js/which-builtin-type/commit/de30f4bdd613b36a11270085974013c1e0025b74)
- [Deps] update `function.prototype.name`, `is-finalizationregistry`, `which-typed-array` [`0ba20f5`](https://github.com/inspect-js/which-builtin-type/commit/0ba20f53824455b2fcdd90027a64bc34f09bec6b)
- [Refactor] use `is-async-function` [`6f36d89`](https://github.com/inspect-js/which-builtin-type/commit/6f36d8942a158cc41f486ba9e29d08f75a6d1406)
- [Deps] update `is-weakref` [`d775476`](https://github.com/inspect-js/which-builtin-type/commit/d775476f5f0dccab56b13c839ce6ada377b67ca0)

## [v1.1.1](https://github.com/inspect-js/which-builtin-type/compare/v1.1.0...v1.1.1) - 2021-08-06

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `available-typed-arrays`, `object-inspect`, `tape` [`8adae7d`](https://github.com/inspect-js/which-builtin-type/commit/8adae7d7ba4a49309f87e990d9787589f8c978a1)
- [Deps] update `is-date-object`, `is-generator-function`, `is-regex`, `which-typed-array` [`805b158`](https://github.com/inspect-js/which-builtin-type/commit/805b158f86d258983868b45ceb4a3a9417fed08e)
- [Refactor] use `has-tostringtag` to behave correctly in the presence of symbol shams [`9ec250a`](https://github.com/inspect-js/which-builtin-type/commit/9ec250a652c6607053d63c3e662547cf36c8cb9b)
- [readme] add github actions/codecov badges [`75b51b4`](https://github.com/inspect-js/which-builtin-type/commit/75b51b4c96280a05014c97efba6b8291b18af5cb)

## [v1.1.0](https://github.com/inspect-js/which-builtin-type/compare/v1.0.1...v1.1.0) - 2021-04-18

### Commits

- [Fix] prevent constructor or Symbol.toStringTag from lying about builtins [`7638412`](https://github.com/inspect-js/which-builtin-type/commit/7638412d9b6cca9af3999b4bf45d3a630f84409e)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`6a06770`](https://github.com/inspect-js/which-builtin-type/commit/6a06770548aec6948ab0aaa28babc0423f0fe745)
- [New] recognize Promise [`0d79e3a`](https://github.com/inspect-js/which-builtin-type/commit/0d79e3a0916438e1c808f83d8928af29914de08a)
- [New] recognize WeakRef and FinalizationRegistry [`020de6a`](https://github.com/inspect-js/which-builtin-type/commit/020de6a84242e12a5cd956b74f4457a8d3cba6fb)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `has-symbols`, `object-inspect`, `tape` [`71e47f5`](https://github.com/inspect-js/which-builtin-type/commit/71e47f50c534e2e37ba654bdc2851f9fb8b9f435)
- [Deps] update `function.prototype.name`, `is-regex` [`f2d56b9`](https://github.com/inspect-js/which-builtin-type/commit/f2d56b9ba12962f6432d869e14a73c43b0bfaeb0)
- [meta] use `prepublishOnly` script for npm 7+ [`daae0a0`](https://github.com/inspect-js/which-builtin-type/commit/daae0a018594cef507a0d5bc3304ec950db15925)
- [Tests] increase coverage [`bd406f2`](https://github.com/inspect-js/which-builtin-type/commit/bd406f212ac79af48c0ca11fb36c770fe106bf5f)

## [v1.0.1](https://github.com/inspect-js/which-builtin-type/compare/v1.0.0...v1.0.1) - 2020-12-14

### Commits

- [Tests] migrate tests to Github Actions [`165a1b5`](https://github.com/inspect-js/which-builtin-type/commit/165a1b5924c242db86749e9691bb310fce3e2b05)
- [meta] do not publish github action workflow files [`851f508`](https://github.com/inspect-js/which-builtin-type/commit/851f508ad644248621ca2d465ba440913e865d4e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `has-bigints`, `object-inspect`, `object.assign`, `tape` [`5b0906c`](https://github.com/inspect-js/which-builtin-type/commit/5b0906cf115aa7b77073262a3fb34134c214c444)
- [Tests] run `nyc` on all tests; use `tape` runner [`85924cd`](https://github.com/inspect-js/which-builtin-type/commit/85924cd70d665f79fd8f1a439706412d7e8e6455)
- [readme] fix repo URLs, remove defunct badges [`8fb4bb5`](https://github.com/inspect-js/which-builtin-type/commit/8fb4bb51219e956916583ba9a34a9a2d0228716e)
- [Dev Deps] update `@ljharb/eslint-config`, `available-typed-arrays`, `make-arrow-function`, `make-generator-function` [`9d65291`](https://github.com/inspect-js/which-builtin-type/commit/9d652910fc84589669f9bfe7accc0272df911a0f)
- [Deps] update `function.prototype.name`, `is-generator-function`, `is-regex`, `which-boxed-primitive`, `which-collection`, `which-typed-array` [`7900c10`](https://github.com/inspect-js/which-builtin-type/commit/7900c10e8113fb9b087103f313bda71f4204a935)
- [actions] add "Allow Edits" workflow [`f9f04f5`](https://github.com/inspect-js/which-builtin-type/commit/f9f04f5999f564b921b9678792a3db8ad4268709)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`4452dee`](https://github.com/inspect-js/which-builtin-type/commit/4452dee4fe2bc3cdb29470f25a184031b1c267ee)

## v1.0.0 - 2020-01-24

### Commits

- Tests [`d65924f`](https://github.com/inspect-js/which-builtin-type/commit/d65924fea79cd13609d3155b018f9db12f995b71)
- Initial commit [`a6208ff`](https://github.com/inspect-js/which-builtin-type/commit/a6208ff57dfbf9b6368adc5a22ca5d2db2c123e5)
- Implementation [`0d1c341`](https://github.com/inspect-js/which-builtin-type/commit/0d1c341b71de6b475db6f8cb430cace4be54c93b)
- readme [`23ecfb6`](https://github.com/inspect-js/which-builtin-type/commit/23ecfb6b5c5e30cbae73148f3d01adbb6229d064)
- npm init [`cce1b17`](https://github.com/inspect-js/which-builtin-type/commit/cce1b17caa4d56bae6980d898e8ad223da073fcc)
- [meta] add `auto-changelog` [`f903f62`](https://github.com/inspect-js/which-builtin-type/commit/f903f629cf70d725a2d0c1fd0e76112c39b88b70)
- [actions] add automatic rebasing / merge commit blocking [`71f9295`](https://github.com/inspect-js/which-builtin-type/commit/71f92950bdbcd1eb79d8ce8c3b7584bd4db45733)
- [Tests] use shared travis-ci configs [`2d7a1f4`](https://github.com/inspect-js/which-builtin-type/commit/2d7a1f4105de53def9b1652e53e900debb2a99a5)
- [Tests] add `npm run lint` [`a6372e2`](https://github.com/inspect-js/which-builtin-type/commit/a6372e203dbdb4ad42da15af7927cd21e0f94618)
- Only apps should have lockfiles [`d6bd083`](https://github.com/inspect-js/which-builtin-type/commit/d6bd083310cd713e6e1e3f16fcd35277194663e7)
- [meta] add `funding` field [`377f67a`](https://github.com/inspect-js/which-builtin-type/commit/377f67a24bef750dca957c8ccde62c3e8af6932b)
- [meta] add `safe-publish-latest` [`99295e9`](https://github.com/inspect-js/which-builtin-type/commit/99295e921f5cb898a62081b6719c30cf537f58f9)
