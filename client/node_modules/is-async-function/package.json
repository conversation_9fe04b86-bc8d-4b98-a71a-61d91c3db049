{"name": "is-async-function", "version": "2.1.1", "description": "Determine if a function is a native async function.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc npm run test:all", "test:all": "npm run test:index && npm run test:uglified", "test:index": "node test", "test:uglified": "node test/uglified", "posttest": "npx npm@\">= 10.2\" audit --production", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-async-function.git"}, "keywords": ["async", "async function", "es6", "es2015", "yield", "function", "function*"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-async-function/issues"}, "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/for-each": "^0.3.3", "@types/make-async-function": "^1.0.2", "@types/make-generator-function": "^2.0.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "make-async-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next", "uglify-register": "^1.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}