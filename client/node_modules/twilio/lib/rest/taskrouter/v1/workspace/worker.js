"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkerPage = exports.WorkerListInstance = exports.WorkerInstance = exports.WorkerContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const reservation_1 = require("./worker/reservation");
const workerChannel_1 = require("./worker/workerChannel");
const workerStatistics_1 = require("./worker/workerStatistics");
const workersCumulativeStatistics_1 = require("./worker/workersCumulativeStatistics");
const workersRealTimeStatistics_1 = require("./worker/workersRealTimeStatistics");
const workersStatistics_1 = require("./worker/workersStatistics");
class WorkerContextImpl {
    constructor(_version, workspaceSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(workspaceSid)) {
            throw new Error("Parameter 'workspaceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { workspaceSid, sid };
        this._uri = `/Workspaces/${workspaceSid}/Workers/${sid}`;
    }
    get reservations() {
        this._reservations =
            this._reservations ||
                (0, reservation_1.ReservationListInstance)(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._reservations;
    }
    get workerChannels() {
        this._workerChannels =
            this._workerChannels ||
                (0, workerChannel_1.WorkerChannelListInstance)(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._workerChannels;
    }
    get statistics() {
        this._statistics =
            this._statistics ||
                (0, workerStatistics_1.WorkerStatisticsListInstance)(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._statistics;
    }
    remove(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        const headers = {};
        if (params["ifMatch"] !== undefined)
            headers["If-Match"] = params["ifMatch"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            params: data,
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkerInstance(operationVersion, payload, instance._solution.workspaceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["activitySid"] !== undefined)
            data["ActivitySid"] = params["activitySid"];
        if (params["attributes"] !== undefined)
            data["Attributes"] = params["attributes"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["rejectPendingReservations"] !== undefined)
            data["RejectPendingReservations"] = serialize.bool(params["rejectPendingReservations"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        if (params["ifMatch"] !== undefined)
            headers["If-Match"] = params["ifMatch"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkerInstance(operationVersion, payload, instance._solution.workspaceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkerContextImpl = WorkerContextImpl;
class WorkerInstance {
    constructor(_version, payload, workspaceSid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.activityName = payload.activity_name;
        this.activitySid = payload.activity_sid;
        this.attributes = payload.attributes;
        this.available = payload.available;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateStatusChanged = deserialize.iso8601DateTime(payload.date_status_changed);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.friendlyName = payload.friendly_name;
        this.sid = payload.sid;
        this.workspaceSid = payload.workspace_sid;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { workspaceSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new WorkerContextImpl(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._context;
    }
    remove(params, callback) {
        return this._proxy.remove(params, callback);
    }
    /**
     * Fetch a WorkerInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed WorkerInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the reservations.
     */
    reservations() {
        return this._proxy.reservations;
    }
    /**
     * Access the workerChannels.
     */
    workerChannels() {
        return this._proxy.workerChannels;
    }
    /**
     * Access the statistics.
     */
    statistics() {
        return this._proxy.statistics;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            activityName: this.activityName,
            activitySid: this.activitySid,
            attributes: this.attributes,
            available: this.available,
            dateCreated: this.dateCreated,
            dateStatusChanged: this.dateStatusChanged,
            dateUpdated: this.dateUpdated,
            friendlyName: this.friendlyName,
            sid: this.sid,
            workspaceSid: this.workspaceSid,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkerInstance = WorkerInstance;
function WorkerListInstance(version, workspaceSid) {
    if (!(0, utility_1.isValidPathParam)(workspaceSid)) {
        throw new Error("Parameter 'workspaceSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new WorkerContextImpl(version, workspaceSid, sid);
    };
    instance._version = version;
    instance._solution = { workspaceSid };
    instance._uri = `/Workspaces/${workspaceSid}/Workers`;
    Object.defineProperty(instance, "cumulativeStatistics", {
        get: function cumulativeStatistics() {
            if (!instance._cumulativeStatistics) {
                instance._cumulativeStatistics =
                    (0, workersCumulativeStatistics_1.WorkersCumulativeStatisticsListInstance)(instance._version, instance._solution.workspaceSid);
            }
            return instance._cumulativeStatistics;
        },
    });
    Object.defineProperty(instance, "realTimeStatistics", {
        get: function realTimeStatistics() {
            if (!instance._realTimeStatistics) {
                instance._realTimeStatistics = (0, workersRealTimeStatistics_1.WorkersRealTimeStatisticsListInstance)(instance._version, instance._solution.workspaceSid);
            }
            return instance._realTimeStatistics;
        },
    });
    Object.defineProperty(instance, "statistics", {
        get: function statistics() {
            if (!instance._statistics) {
                instance._statistics = (0, workersStatistics_1.WorkersStatisticsListInstance)(instance._version, instance._solution.workspaceSid);
            }
            return instance._statistics;
        },
    });
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["friendlyName"] === null ||
            params["friendlyName"] === undefined) {
            throw new Error("Required parameter \"params['friendlyName']\" missing.");
        }
        let data = {};
        data["FriendlyName"] = params["friendlyName"];
        if (params["activitySid"] !== undefined)
            data["ActivitySid"] = params["activitySid"];
        if (params["attributes"] !== undefined)
            data["Attributes"] = params["attributes"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkerInstance(operationVersion, payload, instance._solution.workspaceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["activityName"] !== undefined)
            data["ActivityName"] = params["activityName"];
        if (params["activitySid"] !== undefined)
            data["ActivitySid"] = params["activitySid"];
        if (params["available"] !== undefined)
            data["Available"] = params["available"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["targetWorkersExpression"] !== undefined)
            data["TargetWorkersExpression"] = params["targetWorkersExpression"];
        if (params["taskQueueName"] !== undefined)
            data["TaskQueueName"] = params["taskQueueName"];
        if (params["taskQueueSid"] !== undefined)
            data["TaskQueueSid"] = params["taskQueueSid"];
        if (params["ordering"] !== undefined)
            data["Ordering"] = params["ordering"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkerPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new WorkerPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.WorkerListInstance = WorkerListInstance;
class WorkerPage extends Page_1.default {
    /**
     * Initialize the WorkerPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of WorkerInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new WorkerInstance(this._version, payload, this._solution.workspaceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkerPage = WorkerPage;
