"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDefinedMessageSubscriptionListInstance = exports.UserDefinedMessageSubscriptionInstance = exports.UserDefinedMessageSubscriptionContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class UserDefinedMessageSubscriptionContextImpl {
    constructor(_version, accountSid, callSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(callSid)) {
            throw new Error("Parameter 'callSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { accountSid, callSid, sid };
        this._uri = `/Accounts/${accountSid}/Calls/${callSid}/UserDefinedMessageSubscriptions/${sid}.json`;
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserDefinedMessageSubscriptionContextImpl = UserDefinedMessageSubscriptionContextImpl;
class UserDefinedMessageSubscriptionInstance {
    constructor(_version, payload, accountSid, callSid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.callSid = payload.call_sid;
        this.sid = payload.sid;
        this.dateCreated = deserialize.rfc2822DateTime(payload.date_created);
        this.uri = payload.uri;
        this._solution = { accountSid, callSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new UserDefinedMessageSubscriptionContextImpl(this._version, this._solution.accountSid, this._solution.callSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a UserDefinedMessageSubscriptionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            callSid: this.callSid,
            sid: this.sid,
            dateCreated: this.dateCreated,
            uri: this.uri,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserDefinedMessageSubscriptionInstance = UserDefinedMessageSubscriptionInstance;
function UserDefinedMessageSubscriptionListInstance(version, accountSid, callSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(callSid)) {
        throw new Error("Parameter 'callSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new UserDefinedMessageSubscriptionContextImpl(version, accountSid, callSid, sid);
    };
    instance._version = version;
    instance._solution = { accountSid, callSid };
    instance._uri = `/Accounts/${accountSid}/Calls/${callSid}/UserDefinedMessageSubscriptions.json`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["callback"] === null || params["callback"] === undefined) {
            throw new Error("Required parameter \"params['callback']\" missing.");
        }
        let data = {};
        data["Callback"] = params["callback"];
        if (params["idempotencyKey"] !== undefined)
            data["IdempotencyKey"] = params["idempotencyKey"];
        if (params["method"] !== undefined)
            data["Method"] = params["method"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserDefinedMessageSubscriptionInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.callSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UserDefinedMessageSubscriptionListInstance = UserDefinedMessageSubscriptionListInstance;
