"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChallengePage = exports.ChallengeListInstance = exports.ChallengeInstance = exports.ChallengeContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
const notification_1 = require("./challenge/notification");
class ChallengeContextImpl {
    constructor(_version, serviceSid, identity, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(serviceSid)) {
            throw new Error("Parameter 'serviceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(identity)) {
            throw new Error("Parameter 'identity' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { serviceSid, identity, sid };
        this._uri = `/Services/${serviceSid}/Entities/${identity}/Challenges/${sid}`;
    }
    get notifications() {
        this._notifications =
            this._notifications ||
                (0, notification_1.NotificationListInstance)(this._version, this._solution.serviceSid, this._solution.identity, this._solution.sid);
        return this._notifications;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ChallengeInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.identity, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["authPayload"] !== undefined)
            data["AuthPayload"] = params["authPayload"];
        if (params["metadata"] !== undefined)
            data["Metadata"] = serialize.object(params["metadata"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ChallengeInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.identity, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ChallengeContextImpl = ChallengeContextImpl;
class ChallengeInstance {
    constructor(_version, payload, serviceSid, identity, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.serviceSid = payload.service_sid;
        this.entitySid = payload.entity_sid;
        this.identity = payload.identity;
        this.factorSid = payload.factor_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.dateResponded = deserialize.iso8601DateTime(payload.date_responded);
        this.expirationDate = deserialize.iso8601DateTime(payload.expiration_date);
        this.status = payload.status;
        this.respondedReason = payload.responded_reason;
        this.details = payload.details;
        this.hiddenDetails = payload.hidden_details;
        this.metadata = payload.metadata;
        this.factorType = payload.factor_type;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { serviceSid, identity, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ChallengeContextImpl(this._version, this._solution.serviceSid, this._solution.identity, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a ChallengeInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ChallengeInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the notifications.
     */
    notifications() {
        return this._proxy.notifications;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            serviceSid: this.serviceSid,
            entitySid: this.entitySid,
            identity: this.identity,
            factorSid: this.factorSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            dateResponded: this.dateResponded,
            expirationDate: this.expirationDate,
            status: this.status,
            respondedReason: this.respondedReason,
            details: this.details,
            hiddenDetails: this.hiddenDetails,
            metadata: this.metadata,
            factorType: this.factorType,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ChallengeInstance = ChallengeInstance;
function ChallengeListInstance(version, serviceSid, identity) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(identity)) {
        throw new Error("Parameter 'identity' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new ChallengeContextImpl(version, serviceSid, identity, sid);
    };
    instance._version = version;
    instance._solution = { serviceSid, identity };
    instance._uri = `/Services/${serviceSid}/Entities/${identity}/Challenges`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["factorSid"] === null || params["factorSid"] === undefined) {
            throw new Error("Required parameter \"params['factorSid']\" missing.");
        }
        let data = {};
        data["FactorSid"] = params["factorSid"];
        if (params["expirationDate"] !== undefined)
            data["ExpirationDate"] = serialize.iso8601DateTime(params["expirationDate"]);
        if (params["details.message"] !== undefined)
            data["Details.Message"] = params["details.message"];
        if (params["details.fields"] !== undefined)
            data["Details.Fields"] = serialize.map(params["details.fields"], (e) => e);
        if (params["hiddenDetails"] !== undefined)
            data["HiddenDetails"] = serialize.object(params["hiddenDetails"]);
        if (params["authPayload"] !== undefined)
            data["AuthPayload"] = params["authPayload"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ChallengeInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.identity));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["factorSid"] !== undefined)
            data["FactorSid"] = params["factorSid"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["order"] !== undefined)
            data["Order"] = params["order"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ChallengePage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ChallengePage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ChallengeListInstance = ChallengeListInstance;
class ChallengePage extends Page_1.default {
    /**
     * Initialize the ChallengePage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ChallengeInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ChallengeInstance(this._version, payload, this._solution.serviceSid, this._solution.identity);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ChallengePage = ChallengePage;
