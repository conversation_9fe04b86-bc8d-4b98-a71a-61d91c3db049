"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssistantPage = exports.AssistantListInstance = exports.AssistantInstance = exports.AssistantContextImpl = exports.AssistantsV1ServiceUpdateAssistantRequest = exports.AssistantsV1ServiceTool = exports.AssistantsV1ServiceSegmentCredential = exports.AssistantsV1ServiceKnowledge = exports.AssistantsV1ServiceCustomerAi = exports.AssistantsV1ServiceCreateAssistantRequest = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const assistantsKnowledge_1 = require("./assistant/assistantsKnowledge");
const assistantsTool_1 = require("./assistant/assistantsTool");
const feedback_1 = require("./assistant/feedback");
const message_1 = require("./assistant/message");
class AssistantsV1ServiceCreateAssistantRequest {
}
exports.AssistantsV1ServiceCreateAssistantRequest = AssistantsV1ServiceCreateAssistantRequest;
class AssistantsV1ServiceCustomerAi {
}
exports.AssistantsV1ServiceCustomerAi = AssistantsV1ServiceCustomerAi;
class AssistantsV1ServiceKnowledge {
}
exports.AssistantsV1ServiceKnowledge = AssistantsV1ServiceKnowledge;
class AssistantsV1ServiceSegmentCredential {
}
exports.AssistantsV1ServiceSegmentCredential = AssistantsV1ServiceSegmentCredential;
class AssistantsV1ServiceTool {
}
exports.AssistantsV1ServiceTool = AssistantsV1ServiceTool;
class AssistantsV1ServiceUpdateAssistantRequest {
}
exports.AssistantsV1ServiceUpdateAssistantRequest = AssistantsV1ServiceUpdateAssistantRequest;
class AssistantContextImpl {
    constructor(_version, id) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(id)) {
            throw new Error("Parameter 'id' is not valid.");
        }
        this._solution = { id };
        this._uri = `/Assistants/${id}`;
    }
    get assistantsKnowledge() {
        this._assistantsKnowledge =
            this._assistantsKnowledge ||
                (0, assistantsKnowledge_1.AssistantsKnowledgeListInstance)(this._version, this._solution.id);
        return this._assistantsKnowledge;
    }
    get assistantsTools() {
        this._assistantsTools =
            this._assistantsTools ||
                (0, assistantsTool_1.AssistantsToolListInstance)(this._version, this._solution.id);
        return this._assistantsTools;
    }
    get feedbacks() {
        this._feedbacks =
            this._feedbacks || (0, feedback_1.FeedbackListInstance)(this._version, this._solution.id);
        return this._feedbacks;
    }
    get messages() {
        this._messages =
            this._messages || (0, message_1.MessageListInstance)(this._version, this._solution.id);
        return this._messages;
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssistantInstance(operationVersion, payload, instance._solution.id));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, headers, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        data = params;
        if (headers === null || headers === undefined) {
            headers = {};
        }
        headers["Content-Type"] = "application/json";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "put",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssistantInstance(operationVersion, payload, instance._solution.id));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssistantContextImpl = AssistantContextImpl;
class AssistantInstance {
    constructor(_version, payload, id) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.customerAi = payload.customer_ai;
        this.id = payload.id;
        this.model = payload.model;
        this.name = payload.name;
        this.owner = payload.owner;
        this.url = payload.url;
        this.personalityPrompt = payload.personality_prompt;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.knowledge = payload.knowledge;
        this.tools = payload.tools;
        this._solution = { id: id || this.id };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AssistantContextImpl(this._version, this._solution.id);
        return this._context;
    }
    /**
     * Remove a AssistantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a AssistantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AssistantInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the assistantsKnowledge.
     */
    assistantsKnowledge() {
        return this._proxy.assistantsKnowledge;
    }
    /**
     * Access the assistantsTools.
     */
    assistantsTools() {
        return this._proxy.assistantsTools;
    }
    /**
     * Access the feedbacks.
     */
    feedbacks() {
        return this._proxy.feedbacks;
    }
    /**
     * Access the messages.
     */
    messages() {
        return this._proxy.messages;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            customerAi: this.customerAi,
            id: this.id,
            model: this.model,
            name: this.name,
            owner: this.owner,
            url: this.url,
            personalityPrompt: this.personalityPrompt,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            knowledge: this.knowledge,
            tools: this.tools,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssistantInstance = AssistantInstance;
function AssistantListInstance(version) {
    const instance = ((id) => instance.get(id));
    instance.get = function get(id) {
        return new AssistantContextImpl(version, id);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Assistants`;
    instance.create = function create(params, headers, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        let data = {};
        data = params;
        if (headers === null || headers === undefined) {
            headers = {};
        }
        headers["Content-Type"] = "application/json";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssistantInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssistantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AssistantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AssistantListInstance = AssistantListInstance;
class AssistantPage extends Page_1.default {
    /**
     * Initialize the AssistantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AssistantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AssistantInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssistantPage = AssistantPage;
