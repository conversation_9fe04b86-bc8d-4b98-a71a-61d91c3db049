var q=function(H){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(H)},S=function(H,G){var Y=Object.keys(H);if(Object.getOwnPropertySymbols){var U=Object.getOwnPropertySymbols(H);G&&(U=U.filter(function(x){return Object.getOwnPropertyDescriptor(H,x).enumerable})),Y.push.apply(Y,U)}return Y},T=function(H){for(var G=1;G<arguments.length;G++){var Y=arguments[G]!=null?arguments[G]:{};G%2?S(Object(Y),!0).forEach(function(U){C0(H,U,Y[U])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(Y)):S(Object(Y)).forEach(function(U){Object.defineProperty(H,U,Object.getOwnPropertyDescriptor(Y,U))})}return H},C0=function(H,G,Y){if(G=G0(G),G in H)Object.defineProperty(H,G,{value:Y,enumerable:!0,configurable:!0,writable:!0});else H[G]=Y;return H},G0=function(H){var G=H0(H,"string");return q(G)=="symbol"?G:String(G)},H0=function(H,G){if(q(H)!="object"||!H)return H;var Y=H[Symbol.toPrimitive];if(Y!==void 0){var U=Y.call(H,G||"default");if(q(U)!="object")return U;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,Y=function A(E,B){for(var C in B)G(E,C,{get:B[C],enumerable:!0,configurable:!0,set:function J(X){return B[C]=function(){return X}}})};function U(A){return function(E,B){var C=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",J;if(C==="formatting"&&A.formattingValues){var X=A.defaultFormattingWidth||A.defaultWidth,Z=B!==null&&B!==void 0&&B.width?String(B.width):X;J=A.formattingValues[Z]||A.formattingValues[X]}else{var I=A.defaultWidth,O=B!==null&&B!==void 0&&B.width?String(B.width):A.defaultWidth;J=A.values[O]||A.values[I]}var D=A.argumentCallback?A.argumentCallback(E):E;return J[D]}}var x=function A(E,B){if(E>18&&E<=31)return B+"\u09B6\u09C7";else switch(E){case 1:return B+"\u09B2\u09BE";case 2:case 3:return B+"\u09B0\u09BE";case 4:return B+"\u09A0\u09BE";default:return B+"\u0987"}};function z(A){return A.toString().replace(/\d/g,function(E){return $.locale[E]})}var $={locale:{1:"\u09E7",2:"\u09E8",3:"\u09E9",4:"\u09EA",5:"\u09EB",6:"\u09EC",7:"\u09ED",8:"\u09EE",9:"\u09EF",0:"\u09E6"},number:{"\u09E7":"1","\u09E8":"2","\u09E9":"3","\u09EA":"4","\u09EB":"5","\u09EC":"6","\u09ED":"7","\u09EE":"8","\u09EF":"9","\u09E6":"0"}},M={narrow:["\u0996\u09CD\u09B0\u09BF\u0983\u09AA\u09C2\u0983","\u0996\u09CD\u09B0\u09BF\u0983"],abbreviated:["\u0996\u09CD\u09B0\u09BF\u0983\u09AA\u09C2\u09B0\u09CD\u09AC","\u0996\u09CD\u09B0\u09BF\u0983"],wide:["\u0996\u09CD\u09B0\u09BF\u09B8\u09CD\u099F\u09AA\u09C2\u09B0\u09CD\u09AC","\u0996\u09CD\u09B0\u09BF\u09B8\u09CD\u099F\u09BE\u09AC\u09CD\u09A6"]},R={narrow:["\u09E7","\u09E8","\u09E9","\u09EA"],abbreviated:["\u09E7\u09A4\u09CD\u09B0\u09C8","\u09E8\u09A4\u09CD\u09B0\u09C8","\u09E9\u09A4\u09CD\u09B0\u09C8","\u09EA\u09A4\u09CD\u09B0\u09C8"],wide:["\u09E7\u09AE \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995","\u09E8\u09DF \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995","\u09E9\u09DF \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995","\u09EA\u09B0\u09CD\u09A5 \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995"]},V={narrow:["\u099C\u09BE\u09A8\u09C1","\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1","\u09AE\u09BE\u09B0\u09CD\u099A","\u098F\u09AA\u09CD\u09B0\u09BF\u09B2","\u09AE\u09C7","\u099C\u09C1\u09A8","\u099C\u09C1\u09B2\u09BE\u0987","\u0986\u0997\u09B8\u09CD\u099F","\u09B8\u09C7\u09AA\u09CD\u099F","\u0985\u0995\u09CD\u099F\u09CB","\u09A8\u09AD\u09C7","\u09A1\u09BF\u09B8\u09C7"],abbreviated:["\u099C\u09BE\u09A8\u09C1","\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1","\u09AE\u09BE\u09B0\u09CD\u099A","\u098F\u09AA\u09CD\u09B0\u09BF\u09B2","\u09AE\u09C7","\u099C\u09C1\u09A8","\u099C\u09C1\u09B2\u09BE\u0987","\u0986\u0997\u09B8\u09CD\u099F","\u09B8\u09C7\u09AA\u09CD\u099F","\u0985\u0995\u09CD\u099F\u09CB","\u09A8\u09AD\u09C7","\u09A1\u09BF\u09B8\u09C7"],wide:["\u099C\u09BE\u09A8\u09C1\u09DF\u09BE\u09B0\u09BF","\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1\u09DF\u09BE\u09B0\u09BF","\u09AE\u09BE\u09B0\u09CD\u099A","\u098F\u09AA\u09CD\u09B0\u09BF\u09B2","\u09AE\u09C7","\u099C\u09C1\u09A8","\u099C\u09C1\u09B2\u09BE\u0987","\u0986\u0997\u09B8\u09CD\u099F","\u09B8\u09C7\u09AA\u09CD\u099F\u09C7\u09AE\u09CD\u09AC\u09B0","\u0985\u0995\u09CD\u099F\u09CB\u09AC\u09B0","\u09A8\u09AD\u09C7\u09AE\u09CD\u09AC\u09B0","\u09A1\u09BF\u09B8\u09C7\u09AE\u09CD\u09AC\u09B0"]},L={narrow:["\u09B0","\u09B8\u09CB","\u09AE","\u09AC\u09C1","\u09AC\u09C3","\u09B6\u09C1","\u09B6"],short:["\u09B0\u09AC\u09BF","\u09B8\u09CB\u09AE","\u09AE\u0999\u09CD\u0997\u09B2","\u09AC\u09C1\u09A7","\u09AC\u09C3\u09B9","\u09B6\u09C1\u0995\u09CD\u09B0","\u09B6\u09A8\u09BF"],abbreviated:["\u09B0\u09AC\u09BF","\u09B8\u09CB\u09AE","\u09AE\u0999\u09CD\u0997\u09B2","\u09AC\u09C1\u09A7","\u09AC\u09C3\u09B9","\u09B6\u09C1\u0995\u09CD\u09B0","\u09B6\u09A8\u09BF"],wide:["\u09B0\u09AC\u09BF\u09AC\u09BE\u09B0","\u09B8\u09CB\u09AE\u09AC\u09BE\u09B0","\u09AE\u0999\u09CD\u0997\u09B2\u09AC\u09BE\u09B0","\u09AC\u09C1\u09A7\u09AC\u09BE\u09B0","\u09AC\u09C3\u09B9\u09B8\u09CD\u09AA\u09A4\u09BF\u09AC\u09BE\u09B0 ","\u09B6\u09C1\u0995\u09CD\u09B0\u09AC\u09BE\u09B0","\u09B6\u09A8\u09BF\u09AC\u09BE\u09B0"]},j={narrow:{am:"\u09AA\u09C2",pm:"\u0985\u09AA",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},abbreviated:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},wide:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"}},f={narrow:{am:"\u09AA\u09C2",pm:"\u0985\u09AA",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},abbreviated:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},wide:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"}},v=function A(E,B){var C=Number(E),J=z(C),X=B===null||B===void 0?void 0:B.unit;if(X==="date")return x(C,J);if(C>10||C===0)return J+"\u09A4\u09AE";var Z=C%10;switch(Z){case 2:case 3:return J+"\u09DF";case 4:return J+"\u09B0\u09CD\u09A5";case 6:return J+"\u09B7\u09CD\u09A0";default:return J+"\u09AE"}},P={ordinalNumber:v,era:U({values:M,defaultWidth:"wide"}),quarter:U({values:R,defaultWidth:"wide",argumentCallback:function A(E){return E-1}}),month:U({values:V,defaultWidth:"wide"}),day:U({values:L,defaultWidth:"wide"}),dayPeriod:U({values:j,defaultWidth:"wide",formattingValues:f,defaultFormattingWidth:"wide"})},F={lessThanXSeconds:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1"},xSeconds:{one:"\u09E7 \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1",other:"{{count}} \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1"},halfAMinute:"\u0986\u09A7 \u09AE\u09BF\u09A8\u09BF\u099F",lessThanXMinutes:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AE\u09BF\u09A8\u09BF\u099F",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AE\u09BF\u09A8\u09BF\u099F"},xMinutes:{one:"\u09E7 \u09AE\u09BF\u09A8\u09BF\u099F",other:"{{count}} \u09AE\u09BF\u09A8\u09BF\u099F"},aboutXHours:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u0998\u09A8\u09CD\u099F\u09BE",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u0998\u09A8\u09CD\u099F\u09BE"},xHours:{one:"\u09E7 \u0998\u09A8\u09CD\u099F\u09BE",other:"{{count}} \u0998\u09A8\u09CD\u099F\u09BE"},xDays:{one:"\u09E7 \u09A6\u09BF\u09A8",other:"{{count}} \u09A6\u09BF\u09A8"},aboutXWeeks:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9"},xWeeks:{one:"\u09E7 \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9",other:"{{count}} \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9"},aboutXMonths:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AE\u09BE\u09B8",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AE\u09BE\u09B8"},xMonths:{one:"\u09E7 \u09AE\u09BE\u09B8",other:"{{count}} \u09AE\u09BE\u09B8"},aboutXYears:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AC\u099B\u09B0",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AC\u099B\u09B0"},xYears:{one:"\u09E7 \u09AC\u099B\u09B0",other:"{{count}} \u09AC\u099B\u09B0"},overXYears:{one:"\u09E7 \u09AC\u099B\u09B0\u09C7\u09B0 \u09AC\u09C7\u09B6\u09BF",other:"{{count}} \u09AC\u099B\u09B0\u09C7\u09B0 \u09AC\u09C7\u09B6\u09BF"},almostXYears:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AC\u099B\u09B0",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AC\u099B\u09B0"}},_=function A(E,B,C){var J,X=F[E];if(typeof X==="string")J=X;else if(B===1)J=X.one;else J=X.other.replace("{{count}}",z(B));if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return J+" \u098F\u09B0 \u09AE\u09A7\u09CD\u09AF\u09C7";else return J+" \u0986\u0997\u09C7";return J};function K(A){return function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=E.width?String(E.width):A.defaultWidth,C=A.formats[B]||A.formats[A.defaultWidth];return C}}var w={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},N={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},k={full:"{{date}} {{time}} '\u09B8\u09AE\u09DF'",long:"{{date}} {{time}} '\u09B8\u09AE\u09DF'",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},h={date:K({formats:w,defaultWidth:"full"}),time:K({formats:N,defaultWidth:"full"}),dateTime:K({formats:k,defaultWidth:"full"})},y={lastWeek:"'\u0997\u09A4' eeee '\u09B8\u09AE\u09DF' p",yesterday:"'\u0997\u09A4\u0995\u09BE\u09B2' '\u09B8\u09AE\u09DF' p",today:"'\u0986\u099C' '\u09B8\u09AE\u09DF' p",tomorrow:"'\u0986\u0997\u09BE\u09AE\u09C0\u0995\u09BE\u09B2' '\u09B8\u09AE\u09DF' p",nextWeek:"eeee '\u09B8\u09AE\u09DF' p",other:"P"},b=function A(E,B,C,J){return y[E]};function Q(A){return function(E){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.width,J=C&&A.matchPatterns[C]||A.matchPatterns[A.defaultMatchWidth],X=E.match(J);if(!X)return null;var Z=X[0],I=C&&A.parsePatterns[C]||A.parsePatterns[A.defaultParseWidth],O=Array.isArray(I)?u(I,function(W){return W.test(Z)}):c(I,function(W){return W.test(Z)}),D;D=A.valueCallback?A.valueCallback(O):O,D=B.valueCallback?B.valueCallback(D):D;var E0=E.slice(Z.length);return{value:D,rest:E0}}}var c=function A(E,B){for(var C in E)if(Object.prototype.hasOwnProperty.call(E,C)&&B(E[C]))return C;return},u=function A(E,B){for(var C=0;C<E.length;C++)if(B(E[C]))return C;return};function p(A){return function(E){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=E.match(A.matchPattern);if(!C)return null;var J=C[0],X=E.match(A.parsePattern);if(!X)return null;var Z=A.valueCallback?A.valueCallback(X[0]):X[0];Z=B.valueCallback?B.valueCallback(Z):Z;var I=E.slice(J.length);return{value:Z,rest:I}}}var m=/^(\d+)(ম|য়|র্থ|ষ্ঠ|শে|ই|তম)?/i,d=/\d+/i,g={narrow:/^(খ্রিঃপূঃ|খ্রিঃ)/i,abbreviated:/^(খ্রিঃপূর্ব|খ্রিঃ)/i,wide:/^(খ্রিস্টপূর্ব|খ্রিস্টাব্দ)/i},l={narrow:[/^খ্রিঃপূঃ/i,/^খ্রিঃ/i],abbreviated:[/^খ্রিঃপূর্ব/i,/^খ্রিঃ/i],wide:[/^খ্রিস্টপূর্ব/i,/^খ্রিস্টাব্দ/i]},i={narrow:/^[১২৩৪]/i,abbreviated:/^[১২৩৪]ত্রৈ/i,wide:/^[১২৩৪](ম|য়|র্থ)? ত্রৈমাসিক/i},n={any:[/১/i,/২/i,/৩/i,/৪/i]},s={narrow:/^(জানু|ফেব্রু|মার্চ|এপ্রিল|মে|জুন|জুলাই|আগস্ট|সেপ্ট|অক্টো|নভে|ডিসে)/i,abbreviated:/^(জানু|ফেব্রু|মার্চ|এপ্রিল|মে|জুন|জুলাই|আগস্ট|সেপ্ট|অক্টো|নভে|ডিসে)/i,wide:/^(জানুয়ারি|ফেব্রুয়ারি|মার্চ|এপ্রিল|মে|জুন|জুলাই|আগস্ট|সেপ্টেম্বর|অক্টোবর|নভেম্বর|ডিসেম্বর)/i},o={any:[/^জানু/i,/^ফেব্রু/i,/^মার্চ/i,/^এপ্রিল/i,/^মে/i,/^জুন/i,/^জুলাই/i,/^আগস্ট/i,/^সেপ্ট/i,/^অক্টো/i,/^নভে/i,/^ডিসে/i]},r={narrow:/^(র|সো|ম|বু|বৃ|শু|শ)+/i,short:/^(রবি|সোম|মঙ্গল|বুধ|বৃহ|শুক্র|শনি)+/i,abbreviated:/^(রবি|সোম|মঙ্গল|বুধ|বৃহ|শুক্র|শনি)+/i,wide:/^(রবিবার|সোমবার|মঙ্গলবার|বুধবার|বৃহস্পতিবার |শুক্রবার|শনিবার)+/i},a={narrow:[/^র/i,/^সো/i,/^ম/i,/^বু/i,/^বৃ/i,/^শু/i,/^শ/i],short:[/^রবি/i,/^সোম/i,/^মঙ্গল/i,/^বুধ/i,/^বৃহ/i,/^শুক্র/i,/^শনি/i],abbreviated:[/^রবি/i,/^সোম/i,/^মঙ্গল/i,/^বুধ/i,/^বৃহ/i,/^শুক্র/i,/^শনি/i],wide:[/^রবিবার/i,/^সোমবার/i,/^মঙ্গলবার/i,/^বুধবার/i,/^বৃহস্পতিবার /i,/^শুক্রবার/i,/^শনিবার/i]},t={narrow:/^(পূ|অপ|মধ্যরাত|মধ্যাহ্ন|সকাল|বিকাল|সন্ধ্যা|রাত)/i,abbreviated:/^(পূর্বাহ্ন|অপরাহ্ন|মধ্যরাত|মধ্যাহ্ন|সকাল|বিকাল|সন্ধ্যা|রাত)/i,wide:/^(পূর্বাহ্ন|অপরাহ্ন|মধ্যরাত|মধ্যাহ্ন|সকাল|বিকাল|সন্ধ্যা|রাত)/i},e={any:{am:/^পূ/i,pm:/^অপ/i,midnight:/^মধ্যরাত/i,noon:/^মধ্যাহ্ন/i,morning:/সকাল/i,afternoon:/বিকাল/i,evening:/সন্ধ্যা/i,night:/রাত/i}},B0={ordinalNumber:p({matchPattern:m,parsePattern:d,valueCallback:function A(E){return parseInt(E,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"wide"}),quarter:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function A(E){return E+1}}),month:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),day:Q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"wide"}),dayPeriod:Q({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"})},A0={code:"bn",formatDistance:_,formatLong:h,formatRelative:b,localize:P,match:B0,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=T(T({},window.dateFns),{},{locale:T(T({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{bn:A0})})})();

//# debugId=F2583D5C5DA7A6F564756e2164756e21
