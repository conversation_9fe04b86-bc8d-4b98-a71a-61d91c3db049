import React from 'react';
import { useTranslation } from 'react-i18next';
import './i18n';
import LanguageSwitcher from './components/LanguageSwitcher';
import './App.css';

function App() {
  const { t } = useTranslation();

  return (
    <div className="App">
      <header className="App-header">
        <LanguageSwitcher />
        <h1>{t('common.welcome')}</h1>
        <nav>
          <ul>
            <li>{t('common.dashboard')}</li>
            <li>{t('common.settings')}</li>
            <li>{t('common.profile')}</li>
          </ul>
        </nav>
      </header>
    </div>
  );
}

export default App; 